#!/bin/bash

# Chrome Launch Script for Hardware Fingerprinting Protection
# Disables all major hardware fingerprinting vectors for Augment spoofing
# Created for macOS with comprehensive flag coverage

echo "🎭 Launching Chrome with hardware fingerprinting protection..."

# Check if Chrome is installed
CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
if [ ! -f "$CHROME_PATH" ]; then
    echo "❌ Google Chrome not found at: $CHROME_PATH"
    echo "Please install Google Chrome first."
    exit 1
fi

# Create spoofed profile directory
PROFILE_DIR="$HOME/chrome_spoofed_profile"
mkdir -p "$PROFILE_DIR"

# Launch Chrome with comprehensive hardware fingerprinting disabled
"$CHROME_PATH" \
  --user-data-dir="$PROFILE_DIR" \
  --disable-webgl \
  --disable-webgl2 \
  --disable-webgl-draft-extensions \
  --disable-webgl-image-chromium \
  --disable-gpu \
  --disable-gpu-sandbox \
  --disable-gpu-rasterization \
  --disable-software-rasterizer \
  --disable-accelerated-2d-canvas \
  --disable-accelerated-video-decode \
  --disable-accelerated-video-encode \
  --disable-canvas-aa \
  --disable-2d-canvas-clip-aa \
  --disable-webaudio \
  --disable-background-networking \
  --disable-font-subpixel-positioning \
  --disable-lcd-text \
  --disable-accelerometer-and-gyroscope \
  --disable-sensors \
  --disable-device-orientation \
  --disable-device-motion \
  --disable-geolocation \
  --disable-notifications \
  --disable-permissions-api \
  --disable-presentation-api \
  --disable-remote-fonts \
  --disable-speech-api \
  --disable-web-bluetooth \
  --disable-webrtc \
  --disable-webrtc-hw-decoding \
  --disable-webrtc-hw-encoding \
  --disable-webrtc-multiple-routes \
  --disable-webrtc-hw-vp8-encoding \
  --disable-webrtc-hw-vp9-encoding \
  --disable-media-stream \
  --disable-camera \
  --disable-microphone \
  --disable-usb-devices \
  --disable-hid-devices \
  --disable-serial-devices \
  --disable-bluetooth \
  --disable-plugins \
  --disable-plugins-discovery \
  --disable-component-extensions-with-background-pages \
  --disable-default-apps \
  --disable-sync \
  --disable-translate \
  --disable-ipc-flooding-protection \
  --disable-renderer-backgrounding \
  --disable-background-timer-throttling \
  --disable-backgrounding-occluded-windows \
  --disable-features=VizDisplayCompositor,AudioServiceOutOfProcess \
  --disable-dev-shm-usage \
  --no-sandbox \
  --no-zygote \
  --no-first-run \
  --no-default-browser-check \
  --disable-popup-blocking \
  --disable-prompt-on-repost \
  --disable-hang-monitor \
  --disable-logging \
  --disable-web-security \
  --allow-running-insecure-content \
  --ignore-certificate-errors \
  --ignore-ssl-errors \
  --ignore-certificate-errors-spki-list \
  --user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" \
  > /dev/null 2>&1 &

echo "✅ Chrome launched with spoofed profile at: $PROFILE_DIR"
echo "🔒 Hardware fingerprinting protection active"
echo ""
echo "📋 Protection Summary:"
echo "   • WebGL/WebGL2: Disabled"
echo "   • GPU Acceleration: Disabled"
echo "   • Canvas Fingerprinting: Disabled"
echo "   • Audio Context: Disabled"
echo "   • Sensors/Gyroscope: Disabled"
echo "   • WebRTC: Disabled"
echo "   • Geolocation: Disabled"
echo "   • Device APIs: Disabled"
echo "   • Font Fingerprinting: Reduced"
echo ""
echo "🧪 Test your protection at:"
echo "   • https://browserleaks.com/"
echo "   • https://coveryourtracks.eff.org/"
echo "   • https://amiunique.org/"
echo ""
echo "⚠️  Note: Some websites may not work properly with these restrictions."
echo "💡 Use this profile only for Augment OAuth and spoofing activities."

#!/usr/bin/env python3

"""
Chrome DevTools Protocol Data Injector
Injects spoofed hardware data directly into Chrome using CDP
Most powerful method - can override any JavaScript API
"""

import json
import asyncio
import websockets
import subprocess
import time
import sys

class ChromeCDPInjector:
    def __init__(self):
        self.chrome_process = None
        self.websocket = None
        self.message_id = 1
        
    def start_chrome_with_debugging(self):
        """Start Chrome with remote debugging enabled"""
        chrome_cmd = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "--remote-debugging-port=9222",
            "--user-data-dir=/tmp/chrome_cdp_spoofed",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-background-networking",
            "--disable-sync"
        ]
        
        print("🚀 Starting Chrome with CDP debugging...")
        self.chrome_process = subprocess.Popen(chrome_cmd)
        time.sleep(3)  # Wait for Chrome to start
        
    async def connect_to_chrome(self):
        """Connect to Chrome via WebSocket"""
        try:
            # Get available tabs
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:9222/json') as resp:
                    tabs = await resp.json()
                    
            if not tabs:
                print("❌ No Chrome tabs found")
                return False
                
            # Connect to first tab
            websocket_url = tabs[0]['webSocketDebuggerUrl']
            self.websocket = await websockets.connect(websocket_url)
            print("✅ Connected to Chrome via CDP")
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect to Chrome: {e}")
            return False
    
    async def send_cdp_command(self, method, params=None):
        """Send command to Chrome via CDP"""
        if not self.websocket:
            return None
            
        message = {
            "id": self.message_id,
            "method": method,
            "params": params or {}
        }
        
        await self.websocket.send(json.dumps(message))
        response = await self.websocket.recv()
        self.message_id += 1
        
        return json.loads(response)
    
    async def inject_hardware_spoofing(self):
        """Inject comprehensive hardware spoofing code"""
        
        spoofing_script = """
        (function() {
            console.log('🎭 CDP Hardware Spoofing Injection Started');
            
            // Spoofed MacBook M2 Pro data (different from real machine)
            const SPOOFED_HARDWARE = {
                gpu: {
                    vendor: "Apple",
                    renderer: "Apple M2 Pro (Spoofed)",
                    version: "WebGL 2.0 (OpenGL ES 3.0 Chromium)",
                    unmaskedVendor: "Apple Inc.",
                    unmaskedRenderer: "Apple M2 Pro GPU (Spoofed ID: 7C84)"
                },
                screen: {
                    width: 1728,
                    height: 1117,
                    colorDepth: 30,
                    pixelDepth: 30,
                    availWidth: 1728,
                    availHeight: 1077,
                    devicePixelRatio: 2
                },
                navigator: {
                    hardwareConcurrency: 10,  // Spoofed core count
                    deviceMemory: 16,         // Spoofed memory
                    platform: "MacIntel",
                    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                },
                canvas: {
                    fingerprint: "spoofed_canvas_hash_12345abcdef"
                },
                audio: {
                    sampleRate: 48000,
                    maxChannelCount: 2,
                    baseLatency: 0.005333333333333333
                }
            };
            
            // Override WebGL Context
            const originalGetContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                const context = originalGetContext.call(this, contextType, contextAttributes);
                
                if ((contextType === 'webgl' || contextType === 'webgl2') && context) {
                    // Override getParameter for WebGL
                    const originalGetParameter = context.getParameter;
                    context.getParameter = function(parameter) {
                        switch (parameter) {
                            case context.VENDOR:
                                return SPOOFED_HARDWARE.gpu.vendor;
                            case context.RENDERER:
                                return SPOOFED_HARDWARE.gpu.renderer;
                            case context.VERSION:
                                return SPOOFED_HARDWARE.gpu.version;
                            case context.SHADING_LANGUAGE_VERSION:
                                return "WebGL GLSL ES 3.00 (OpenGL ES GLSL ES 3.0 Chromium)";
                            default:
                                return originalGetParameter.call(this, parameter);
                        }
                    };
                    
                    // Override getExtension for unmasked renderer info
                    const originalGetExtension = context.getExtension;
                    context.getExtension = function(name) {
                        const ext = originalGetExtension.call(this, name);
                        if (name === 'WEBGL_debug_renderer_info' && ext) {
                            const originalGetParam = context.getParameter;
                            context.getParameter = function(param) {
                                if (param === ext.UNMASKED_VENDOR_WEBGL) {
                                    return SPOOFED_HARDWARE.gpu.unmaskedVendor;
                                }
                                if (param === ext.UNMASKED_RENDERER_WEBGL) {
                                    return SPOOFED_HARDWARE.gpu.unmaskedRenderer;
                                }
                                return originalGetParam.call(this, param);
                            };
                        }
                        return ext;
                    };
                }
                
                return context;
            };
            
            // Override Navigator properties
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                value: SPOOFED_HARDWARE.navigator.hardwareConcurrency,
                writable: false,
                configurable: false
            });
            
            if ('deviceMemory' in navigator) {
                Object.defineProperty(navigator, 'deviceMemory', {
                    value: SPOOFED_HARDWARE.navigator.deviceMemory,
                    writable: false,
                    configurable: false
                });
            }
            
            // Override Screen properties
            Object.defineProperty(screen, 'width', {
                value: SPOOFED_HARDWARE.screen.width,
                writable: false,
                configurable: false
            });
            
            Object.defineProperty(screen, 'height', {
                value: SPOOFED_HARDWARE.screen.height,
                writable: false,
                configurable: false
            });
            
            Object.defineProperty(screen, 'colorDepth', {
                value: SPOOFED_HARDWARE.screen.colorDepth,
                writable: false,
                configurable: false
            });
            
            Object.defineProperty(screen, 'pixelDepth', {
                value: SPOOFED_HARDWARE.screen.pixelDepth,
                writable: false,
                configurable: false
            });
            
            // Override devicePixelRatio
            Object.defineProperty(window, 'devicePixelRatio', {
                value: SPOOFED_HARDWARE.screen.devicePixelRatio,
                writable: false,
                configurable: false
            });
            
            // Override Canvas toDataURL for fingerprinting
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function() {
                // Return consistent spoofed canvas fingerprint
                return "data:image/png;base64," + SPOOFED_HARDWARE.canvas.fingerprint;
            };
            
            // Override AudioContext
            if (window.AudioContext || window.webkitAudioContext) {
                const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
                function SpoofedAudioContext() {
                    const ctx = new OriginalAudioContext();
                    
                    Object.defineProperty(ctx, 'sampleRate', {
                        value: SPOOFED_HARDWARE.audio.sampleRate,
                        writable: false
                    });
                    
                    Object.defineProperty(ctx, 'baseLatency', {
                        value: SPOOFED_HARDWARE.audio.baseLatency,
                        writable: false
                    });
                    
                    return ctx;
                }
                
                SpoofedAudioContext.prototype = OriginalAudioContext.prototype;
                window.AudioContext = SpoofedAudioContext;
                if (window.webkitAudioContext) {
                    window.webkitAudioContext = SpoofedAudioContext;
                }
            }
            
            console.log('✅ CDP Hardware Spoofing Injection Complete');
            console.log('🎭 All hardware APIs now return spoofed data');
            
        })();
        """
        
        # Enable Runtime domain
        await self.send_cdp_command("Runtime.enable")
        
        # Inject the spoofing script
        result = await self.send_cdp_command("Runtime.evaluate", {
            "expression": spoofing_script,
            "includeCommandLineAPI": True
        })
        
        if result.get('result', {}).get('type') == 'undefined':
            print("✅ Hardware spoofing script injected successfully")
            return True
        else:
            print(f"❌ Script injection failed: {result}")
            return False
    
    async def navigate_to_url(self, url):
        """Navigate to a specific URL"""
        await self.send_cdp_command("Page.enable")
        await self.send_cdp_command("Page.navigate", {"url": url})
        print(f"🌐 Navigated to: {url}")
    
    async def run_spoofing_session(self, target_url=None):
        """Run complete spoofing session"""
        try:
            # Start Chrome
            self.start_chrome_with_debugging()
            
            # Connect via CDP
            if not await self.connect_to_chrome():
                return False
            
            # Inject spoofing
            if not await self.inject_hardware_spoofing():
                return False
            
            # Navigate to target URL if provided
            if target_url:
                await self.navigate_to_url(target_url)
            
            print("🎭 Chrome is now running with spoofed hardware data")
            print("🔗 You can now use Chrome normally - all hardware APIs are spoofed")
            print("🧪 Test at: https://browserleaks.com/")
            print("🔐 For Augment: https://augmentcode.com/")
            print("⏹️  Press Ctrl+C to stop")
            
            # Keep connection alive
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping spoofing session...")
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            if self.websocket:
                await self.websocket.close()
            if self.chrome_process:
                self.chrome_process.terminate()

async def main():
    if len(sys.argv) > 1:
        target_url = sys.argv[1]
    else:
        target_url = "https://browserleaks.com/"
    
    injector = ChromeCDPInjector()
    await injector.run_spoofing_session(target_url)

if __name__ == "__main__":
    print("🎭 Chrome CDP Hardware Data Injector")
    print("=====================================")
    print("This script uses Chrome DevTools Protocol to inject spoofed hardware data")
    print("Usage: python3 chrome_cdp_injector.py [url]")
    print("")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

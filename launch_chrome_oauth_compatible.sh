#!/bin/bash

# Chrome Launch Script for OAuth-Compatible Hardware Fingerprinting Protection
# Balanced approach: Maximum protection while allowing OAuth flows to work
# Created for Augment authentication compatibility

echo "🎭 Launching OAuth-compatible Chrome with fingerprinting protection..."

# Check if Chrome is installed
CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
if [ ! -f "$CHROME_PATH" ]; then
    echo "❌ Google Chrome not found at: $CHROME_PATH"
    echo "Please install Google Chrome first."
    exit 1
fi

# Create OAuth-compatible spoofed profile directory
PROFILE_DIR="$HOME/chrome_oauth_spoofed_profile"
mkdir -p "$PROFILE_DIR"

# Launch Chrome with OAuth-compatible fingerprinting protection
"$CHROME_PATH" \
  --user-data-dir="$PROFILE_DIR" \
  --disable-webgl \
  --disable-webgl2 \
  --disable-webgl-draft-extensions \
  --disable-gpu-rasterization \
  --disable-accelerated-2d-canvas \
  --disable-accelerated-video-decode \
  --disable-canvas-aa \
  --disable-2d-canvas-clip-aa \
  --disable-webaudio \
  --disable-font-subpixel-positioning \
  --disable-lcd-text \
  --disable-accelerometer-and-gyroscope \
  --disable-sensors \
  --disable-device-orientation \
  --disable-device-motion \
  --disable-geolocation \
  --disable-remote-fonts \
  --disable-speech-api \
  --disable-web-bluetooth \
  --disable-webrtc-hw-decoding \
  --disable-webrtc-hw-encoding \
  --disable-webrtc-multiple-routes \
  --disable-webrtc-hw-vp8-encoding \
  --disable-webrtc-hw-vp9-encoding \
  --disable-usb-devices \
  --disable-hid-devices \
  --disable-serial-devices \
  --disable-bluetooth \
  --disable-plugins-discovery \
  --disable-component-extensions-with-background-pages \
  --disable-default-apps \
  --disable-translate \
  --disable-renderer-backgrounding \
  --disable-background-timer-throttling \
  --disable-backgrounding-occluded-windows \
  --disable-features=VizDisplayCompositor \
  --disable-dev-shm-usage \
  --no-first-run \
  --no-default-browser-check \
  --disable-hang-monitor \
  --disable-logging \
  --user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" \
  > /dev/null 2>&1 &

echo "✅ OAuth-compatible Chrome launched with spoofed profile at: $PROFILE_DIR"
echo "🔒 Fingerprinting protection active (OAuth-compatible mode)"
echo ""
echo "📋 Protection Summary:"
echo "   • WebGL/WebGL2: Disabled ✅"
echo "   • Canvas Fingerprinting: Disabled ✅"
echo "   • Audio Context: Disabled ✅"
echo "   • Sensors/Gyroscope: Disabled ✅"
echo "   • Device APIs: Disabled ✅"
echo "   • Font Fingerprinting: Reduced ✅"
echo "   • GPU Sandbox: Enabled (for OAuth compatibility) ⚠️"
echo "   • WebRTC: Partially enabled (for OAuth) ⚠️"
echo "   • Notifications: Enabled (for OAuth) ⚠️"
echo ""
echo "🔐 OAuth Compatibility:"
echo "   • Cloudflare challenges: Should work ✅"
echo "   • Auth0 flows: Should work ✅"
echo "   • CAPTCHA verification: Should work ✅"
echo "   • WebGPU: Disabled (fingerprinting protection) ✅"
echo ""
echo "🧪 Test your protection at:"
echo "   • https://browserleaks.com/"
echo "   • https://coveryourtracks.eff.org/"
echo ""
echo "🎯 For Augment OAuth:"
echo "   • Visit: https://augmentcode.com/"
echo "   • Sign in should work normally"
echo "   • VS Code authentication should complete"
echo ""
echo "💡 This profile balances protection with OAuth functionality."

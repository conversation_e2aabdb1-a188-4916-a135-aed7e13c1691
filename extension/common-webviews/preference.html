<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <script type="module" crossorigin src="./assets/preference-ChWVk5ci.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BGEGncoZ.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-Bbk8_XKh.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-TjBwLyJp.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DR78svzs.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/Content-CTqTUTf_.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-Cor0M5Er.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-NgqNgjwU.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/rules-parser-D8-cU5vK.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-D9Au3xFg.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/github-DSqbVTgM.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-CVKdq1wC.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/types-BSMhNRWH.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/types-Cgd-nZOV.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BS_CDetd.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/types-DlPx64PZ.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/check-DCGJZZwz.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-oII5PndH.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BDfvUmRL.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-MYqAlrEH.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-D3MPLNkT.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-DVDPX5m5.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/folder-CJRvA1r9.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-gmDD0p1L.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DZMhbPz9.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/expand-BMjiP8tK.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/mcp-logo-DrDhW48o.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-BWJckjMw.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-DiLEQXmz.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-Ct6C9T4k.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-87D86vFi.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/magnifying-glass-BYgnDWrw.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-MCURCZji.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-CprfUNtE.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-D5_sJEjT.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CdGA9F9C.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-iIh97Yc5.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-tJmvlmov.js" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/pen-to-square-Dvw-pMXw.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/Content-EO9gowBV.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/folder-B6hY0a9d.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-DB7Uu6wb.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/index-eY12-hdZ.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-BDGK8ykF.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
    <link rel="stylesheet" crossorigin href="./assets/preference-Dn6mpF6J.css" nonce="nonce-Zoj7x7NwTV/f/QSck0NvXg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>

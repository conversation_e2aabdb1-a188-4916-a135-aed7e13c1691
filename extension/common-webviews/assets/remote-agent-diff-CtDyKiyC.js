var ps=Object.defineProperty;var Qt=r=>{throw TypeError(r)};var fs=(r,e,t)=>e in r?ps(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var fe=(r,e,t)=>fs(r,typeof e!="symbol"?e+"":e,t),gs=(r,e,t)=>e.has(r)||Qt("Cannot "+t);var Gt=(r,e,t)=>e.has(r)?Qt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t);var gt=(r,e,t)=>(gs(r,e,"access private method"),t);import{ai as Ue,S as ee,i as te,s as X,V as b,c as x,a3 as Ae,e as g,f as q,n as Y,h as $,w as qe,G as T,H as le,y as L,z as M,u as f,t as h,B as R,D as I,ab as be,q as H,r as U,ao as zi,ag as Me,a0 as Li,a1 as Ke,F as Re,E as xe,ap as Dt,an as zt,af as st,x as Ge,A as Je,ah as Mi,T as ge,aD as Ne,a5 as rt,a7 as Ri,ad as $s,ae as Jt,b as qi,R as Ee,a as Ni,aB as Ft,X as _e,Y as Be,Z as ze,g as Ti,aA as Xt,aC as hs,a8 as ms,ac as Ds,am as Tt}from"./SpinnerAugment-BGEGncoZ.js";import"./design-system-init-BFmnHByk.js";import{g as $t,p as Fs,a as xs}from"./index-yERhhNs7.js";import"./design-system-init-UkRPuLDM.js";import{W as Qe,e as pe,u as Oi,o as Si,h as Pi}from"./BaseButton-Bbk8_XKh.js";import{T as He,M as Cs}from"./TextTooltipAugment-Cor0M5Er.js";import{s as Yt}from"./index-jTYqnJCw.js";import{h as xt,p as ks,j as Ie,M as ws,c as Ii,b as ji,P as ut,e as vs,i as ys,f as As,k as bs}from"./diff-utils-DVDPX5m5.js";import{a as Ot,b as St,g as Vi,S as Es,M as _s}from"./index-D3MPLNkT.js";import{I as yt,A as Bs}from"./IconButtonAugment-DR78svzs.js";import{V as ct}from"./VSCodeCodicon-Ci_TiUhH.js";import{B as Ve}from"./ButtonAugment-DZMhbPz9.js";import{M as At}from"./MaterialIcon-MCURCZji.js";import{n as Hi,a as Ze,g as se}from"./file-paths-BcSg4gks.js";import{T as Xe}from"./Content-CTqTUTf_.js";import{O as Ui}from"./open-in-new-window-BWJckjMw.js";import{F as zs}from"./types-Cgd-nZOV.js";import{L as Ls}from"./LanguageIcon-Ct6C9T4k.js";import{g as Ms}from"./globals-D0QH3NT1.js";import{E as Rs}from"./expand-BMjiP8tK.js";import{E as Zi}from"./exclamation-triangle-DimRUUrg.js";import"./toggleHighContrast-Th-X2FgN.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BS_CDetd.js";class Ct{constructor(e){fe(this,"_opts",null);fe(this,"_subscribers",new Set);this._asyncMsgSender=e}subscribe(e){return this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}}notifySubscribers(){this._subscribers.forEach(e=>e(this))}get opts(){return this._opts}updateOpts(e){this._opts=e,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const e=await this._asyncMsgSender.send({type:Qe.remoteAgentDiffPanelLoaded});this.updateOpts(e.data)}catch(e){console.error("Failed to load diff panel:",e),this.updateOpts(null)}}handleMessageFromExtension(e){const t=e.data;return!(!t||!t.type)&&t.type===Qe.remoteAgentDiffPanelSetOpts&&(this.updateOpts(t.data),!0)}}fe(Ct,"key","remoteAgentDiffModel");class ot{constructor(e){fe(this,"_applyingFilePaths",Ue([]));fe(this,"_appliedFilePaths",Ue([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,n=3e4){try{return(await this._asyncMsgSender.send({type:Qe.diffExplanationRequest,data:{changedFiles:e,apikey:t}},n)).data.explanation}catch(i){return console.error("Failed to get diff explanation:",i),[]}}async groupChanges(e,t=!1,n){try{return(await this._asyncMsgSender.send({type:Qe.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:n}})).data.groupedChanges}catch(i){return console.error("Failed to group changes:",i),[]}}async getDescriptions(e,t){try{const n=await this._asyncMsgSender.send({type:Qe.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}},1e5);return{explanation:n.data.explanation,error:n.data.error}}catch(n){return console.error("Failed to get descriptions:",n),{explanation:[],error:`Failed to get descriptions: ${n instanceof Error?n.message:String(n)}`}}}async applyChanges(e,t,n){this._applyingFilePaths.update(i=>[...i.filter(s=>s!==e),e]);try{(await this._asyncMsgSender.send({type:Qe.applyChangesRequest,data:{path:e,originalCode:t,newCode:n}},3e4)).data.success&&this._appliedFilePaths.update(i=>[...i.filter(s=>s!==e),e])}catch(i){console.error("applyChanges error",i)}finally{this._applyingFilePaths.update(i=>i.filter(s=>s!==e))}}async openFile(e){try{const t=await this._asyncMsgSender.send({type:Qe.openFileRequest,data:{path:e}},1e4);return t.data.success||console.error("Failed to open file:",t.data.error),t.data.success}catch(t){console.error("openFile error",t)}return!1}}fe(ot,"key","remoteAgentsDiffOpsModel");function Kt(r,e,t,n,i={}){const{context:s=3,generateId:o=!0}=i,l=xt(r,e,t,n,"","",{context:s}),a=e||r;let u;return o?u=`${Ie(a)}-${Ie(t+n)}`:u=Math.random().toString(36).substring(2,15),{id:u,path:a,diff:l,originalCode:t,modifiedCode:n}}function Lt(r){const e=r.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function Wi(r){return!r.originalCode||r.originalCode.trim()===""}function Qi(r){return!r.modifiedCode||r.modifiedCode.trim()===""}class qs{static generateDiff(e,t,n,i){return Kt(e,t,n,i)}static generateDiffs(e){return function(t,n={}){return t.map(i=>Kt(i.oldPath,i.newPath,i.oldContent,i.newContent,n))}(e)}static getDiffStats(e){return Lt(e)}static getDiffObjectStats(e){return Lt(e.diff)}static isNewFile(e){return Wi(e)}static isDeletedFile(e){return Qi(e)}}function Ns(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function Ts(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function Os(r){let e,t,n;function i(l,a){return l[2]?Ts:Ns}let s=i(r),o=s(r);return{c(){e=b("span"),t=b("code"),o.c(),x(t,"class","markdown-codespan svelte-164mxpf"),x(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ae(t,"markdown-string",r[4])},m(l,a){g(l,e,a),q(e,t),o.m(t,null),r[6](e)},p(l,[a]){s===(s=i(l))&&o?o.p(l,a):(o.d(1),o=s(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(t,"style",n),16&a&&Ae(t,"markdown-string",l[4])},i:Y,o:Y,d(l){l&&$(e),o.d(),r[6](null)}}}function Ss(r,e,t){let n,i,s,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,i=n.startsWith('"')),2&r.$$.dirty&&t(2,s=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=s&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),p=parseInt(u.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),p=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[a,n,s,o,i,l,function(u){qe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}let Ps=class extends ee{constructor(r){super(),te(this,r,Ss,Os,X,{token:5,element:0})}};function Is(r){let e,t;return e=new ws({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.markdown=n[1](n[0])),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function js(r,e,t){let{markdown:n}=e;const i={codespan:Ps};return r.$$set=s=>{"markdown"in s&&t(0,n=s.markdown)},[n,s=>s.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),i]}let Gi=class extends ee{constructor(r){super(),te(this,r,js,Is,X,{markdown:0})}};function en(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function tn(r){let e,t,n,i,s;t=new yt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Us]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=nn(en(r,o,u));const a=u=>h(l[u],1,1,()=>{l[u]=null});return{c(){e=b("div"),L(t.$$.fragment),n=I(),i=b("div");for(let u=0;u<l.length;u+=1)l[u].c();x(e,"class","toggle-button svelte-14s1ghg"),x(i,"class","descriptions svelte-14s1ghg"),be(i,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,e,c),M(t,e,null),g(u,n,c),g(u,i,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(i,null);s=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let p;for(o=pe(u[1]),p=0;p<o.length;p+=1){const F=en(u,o,p);l[p]?(l[p].p(F,c),f(l[p],1)):(l[p]=nn(F),l[p].c(),f(l[p],1),l[p].m(i,null))}for(H(),p=o.length;p<l.length;p+=1)a(p);U()}(!s||16&c[0])&&be(i,"transform","translateY("+-u[4]+"px)")},i(u){if(!s){f(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)f(l[c]);s=!0}},o(u){h(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)h(l[c]);s=!1},d(u){u&&($(e),$(n),$(i)),R(t),Re(l,u)}}}function Vs(r){let e,t;return e=new ct({props:{icon:"book"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Hs(r){let e,t;return e=new ct({props:{icon:"x"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Us(r){let e,t,n,i;const s=[Hs,Vs],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),h(o[c],1,1,()=>{o[c]=null}),U(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function nn(r){let e,t,n,i;return t=new Gi({props:{markdown:r[47].text}}),{c(){e=b("div"),L(t.$$.fragment),n=I(),x(e,"class","description svelte-14s1ghg"),be(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),be(e,"--ds-panel-solid","transparent")},m(s,o){g(s,e,o),M(t,e,null),q(e,n),i=!0},p(s,o){const l={};2&o[0]&&(l.markdown=s[47].text),t.$set(l),(!i||34&o[0])&&be(e,"top",(s[5][s[49]]||s[9](s[47]))+"px")},i(s){i||(f(t.$$.fragment,s),i=!0)},o(s){h(t.$$.fragment,s),i=!1},d(s){s&&$(e),R(t)}}}function Zs(r){let e,t,n,i,s=r[1].length>0&&tn(r);return{c(){e=b("div"),t=b("div"),n=I(),s&&s.c(),x(t,"class","editor-container svelte-14s1ghg"),be(t,"height",r[3]+"px"),x(e,"class","monaco-diff-container svelte-14s1ghg"),Ae(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),q(e,t),r[23](t),q(e,n),s&&s.m(e,null),i=!0},p(o,l){(!i||8&l[0])&&be(t,"height",o[3]+"px"),o[1].length>0?s?(s.p(o,l),2&l[0]&&f(s,1)):(s=tn(o),s.c(),f(s,1),s.m(e,null)):s&&(H(),h(s,1,1,()=>{s=null}),U()),(!i||3&l[0])&&Ae(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){i||(f(s),i=!0)},o(o){h(s),i=!1},d(o){o&&$(e),r[23](null),s&&s.d()}}}function Ws(r,e,t){let n,i,s;const o=zi();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:F=[]}=e,{theme:y}=e,{areDescriptionsVisible:w=!0}=e,{isNewFile:_=!1}=e,{isDeletedFile:E=!1}=e;const z=Ot.getContext().monaco;let m,D,C,A;Me(r,z,k=>t(22,n=k));let B,O=[];const V=St();let W,Q=Ue(0);Me(r,Q,k=>t(4,i=k));let re=_?20*a.split(`
`).length+40:100;const ae=n?n.languages.getLanguages().map(k=>k.id):[];function ce(k,N){var S,j;if(N){const P=(S=N.split(".").pop())==null?void 0:S.toLowerCase();if(P){const Z=(j=n==null?void 0:n.languages.getLanguages().find(K=>{var ne;return(ne=K.extensions)==null?void 0:ne.includes("."+P)}))==null?void 0:j.id;if(Z&&ae.includes(Z))return Z}}return"plaintext"}const de=Ue({});Me(r,de,k=>t(5,s=k));let G=null;function Fe(){if(!m)return;O=O.filter(S=>(S.dispose(),!1));const k=m.getOriginalEditor(),N=m.getModifiedEditor();O.push(k.onDidScrollChange(()=>{Dt(Q,i=k.getScrollTop(),i)}),N.onDidScrollChange(()=>{Dt(Q,i=N.getScrollTop(),i)}))}function $e(){if(!m||!B)return;const k=m.getOriginalEditor(),N=m.getModifiedEditor();O.push(N.onDidContentSizeChange(()=>V.requestLayout()),k.onDidContentSizeChange(()=>V.requestLayout()),m.onDidUpdateDiff(()=>V.requestLayout()),N.onDidChangeHiddenAreas(()=>V.requestLayout()),k.onDidChangeHiddenAreas(()=>V.requestLayout()),N.onDidLayoutChange(()=>V.requestLayout()),k.onDidLayoutChange(()=>V.requestLayout()),N.onDidFocusEditorWidget(()=>{v(!0)}),k.onDidFocusEditorWidget(()=>{v(!0)}),N.onDidBlurEditorWidget(()=>{v(!1)}),k.onDidBlurEditorWidget(()=>{v(!1)}),N.onDidChangeModelContent(()=>{Ce=!0,ue=Date.now();const S=(A==null?void 0:A.getValue())||"";if(S===a)return;const j=S.replace(p.join(""),"").replace(F.join(""),"");o("codeChange",{modifiedCode:j});const P=setTimeout(()=>{Ce=!1},500);O.push({dispose:()=>clearTimeout(P)})})),function(){!B||!m||(G&&clearTimeout(G),G=setTimeout(()=>{if(!B.__hasClickListener){const S=j=>{const P=j.target;P&&(P.closest('[title="Show Unchanged Region"]')||P.closest('[title="Hide Unchanged Region"]'))&&we()};B.addEventListener("click",S),t(2,B.__hasClickListener=!0,B),O.push({dispose:()=>{B.removeEventListener("click",S)}})}m&&O.push(m.onDidUpdateDiff(()=>{we()}))},300))}()}Li(()=>{m==null||m.dispose(),D==null||D.dispose(),C==null||C.dispose(),A==null||A.dispose(),O.forEach(k=>k.dispose()),G&&clearTimeout(G),W==null||W()});let ie=null;function we(){ie&&clearTimeout(ie),ie=setTimeout(()=>{V.requestLayout(),ie=null},100),ie&&O.push({dispose:()=>{ie&&(clearTimeout(ie),ie=null)}})}function ve(k,N,S,j=[],P=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");C==null||C.dispose(),A==null||A.dispose(),N=N||"",S=S||"";const Z=j.join(""),K=P.join("");if(N=_?S.split(`
`).map(()=>" ").join(`
`):Z+N+K,S=Z+S+K,C=n.editor.createModel(N,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0),E&&(S=S.split(`
`).map(()=>" ").join(`
`)),t(21,A=n.editor.createModel(S,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0)),m){m.setModel({original:C,modified:A});const ne=m.getOriginalEditor();ne&&ne.updateOptions({lineNumbers:"off"}),Fe(),G&&clearTimeout(G),G=setTimeout(()=>{$e(),G=null},300)}}Ke(()=>{if(n)if(_){t(20,D=n.editor.create(B,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:y,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:j=>`${d-p.length+j}`}));const k=ce(0,u);t(21,A=n.editor.createModel(a,k,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),D.setModel(A),O.push(D.onDidChangeModelContent(()=>{Ce=!0,ue=Date.now();const j=(A==null?void 0:A.getValue())||"";if(j===a)return;o("codeChange",{modifiedCode:j});const P=setTimeout(()=>{Ce=!1},500);O.push({dispose:()=>clearTimeout(P)})})),O.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const N=D.getContentHeight();t(3,re=Math.max(N,60));const S=setTimeout(()=>{D==null||D.layout()},0);O.push({dispose:()=>clearTimeout(S)})}else t(19,m=n.editor.createDiffEditor(B,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:y,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:k=>`${d-p.length+k}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),W&&W(),W=V.registerEditor({editor:m,updateHeight:ye,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ve(u,l,a,p,F),Fe(),$e(),G&&clearTimeout(G),G=setTimeout(()=>{V.requestLayout(),G=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ce=!1,ue=0;function ke(k,N=!0){return m?(N?m.getModifiedEditor():m.getOriginalEditor()).getTopForLineNumber(k):18*k}function ye(){if(!m)return;const k=m.getModel(),N=k==null?void 0:k.original,S=k==null?void 0:k.modified;if(!N||!S)return;const j=m.getOriginalEditor(),P=m.getModifiedEditor(),Z=m.getLineChanges()||[];let K;if(Z.length===0){const ne=j.getContentHeight(),De=P.getContentHeight();K=Math.max(100,ne,De)}else{let ne=0,De=0;for(const Te of Z)Te.originalEndLineNumber>0&&(ne=Math.max(ne,Te.originalEndLineNumber)),Te.modifiedEndLineNumber>0&&(De=Math.max(De,Te.modifiedEndLineNumber));ne=Math.min(ne+3,N.getLineCount()),De=Math.min(De+3,S.getLineCount());const Le=j.getTopForLineNumber(ne),Se=P.getTopForLineNumber(De);K=Math.max(Le,Se)+60}t(3,re=Math.min(K,2e4)),m.layout(),oe()}function v(k){if(!m)return;const N=m.getOriginalEditor(),S=m.getModifiedEditor();N.updateOptions({scrollbar:{handleMouseWheel:k}}),S.updateOptions({scrollbar:{handleMouseWheel:k}})}function J(k){if(!m)return 0;const N=m.getModel(),S=N==null?void 0:N.original,j=N==null?void 0:N.modified;if(!S||!j)return 0;const P=ke(k.range.start+1,!1),Z=ke(k.range.start+1,!0);return P&&!Z?P:!P&&Z?Z:Math.min(P,Z)}function oe(){if(!m||c.length===0)return;const k={};c.forEach((N,S)=>{k[S]=J(N)}),de.set(k)}return r.$$set=k=>{"originalCode"in k&&t(10,l=k.originalCode),"modifiedCode"in k&&t(11,a=k.modifiedCode),"path"in k&&t(12,u=k.path),"descriptions"in k&&t(1,c=k.descriptions),"lineOffset"in k&&t(13,d=k.lineOffset),"extraPrefixLines"in k&&t(14,p=k.extraPrefixLines),"extraSuffixLines"in k&&t(15,F=k.extraSuffixLines),"theme"in k&&t(16,y=k.theme),"areDescriptionsVisible"in k&&t(0,w=k.areDescriptionsVisible),"isNewFile"in k&&t(17,_=k.isNewFile),"isDeletedFile"in k&&t(18,E=k.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(k=a,!(Ce||Date.now()-ue<1e3||A&&A.getValue()===p.join("")+k+F.join(""))))if(_&&D){if(A)A.setValue(a);else{const N=ce(0,u);n&&t(21,A=n.editor.createModel(a,N,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),A&&D.setModel(A)}t(3,re=20*a.split(`
`).length+40),D.layout()}else!_&&m&&(ve(u,l,a,p,F),V.requestLayout());var k;if(524290&r.$$.dirty[0]&&m&&c.length>0&&oe(),1181696&r.$$.dirty[0]&&_&&a&&D){const N=D.getContentHeight();t(3,re=Math.max(N,60)),D.layout()}},[w,c,B,re,i,s,z,Q,de,J,l,a,u,d,p,F,y,_,E,m,D,A,n,function(k){qe[k?"unshift":"push"](()=>{B=k,t(2,B)})},()=>t(0,w=!w)]}let Qs=class extends ee{constructor(r){super(),te(this,r,Ws,Zs,X,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}};const Gs=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],Js=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],Ji=1048576;function Pt(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function lt(r){switch(Pt(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function sn(r){const e=Pt(r);return Gs.includes(e)}function rn(r){return r>Ji}const Xi=Symbol("focusedPath");function Yi(){return st(Xi)}function Mt(r){return`file-diff-${Ie(r)}`}function Xs(r){let e,t,n;function i(o){r[41](o)}let s={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(s.areDescriptionsVisible=r[1]),e=new Qs({props:s}),qe.push(()=>Ge(e,"areDescriptionsVisible",i)),e.$on("codeChange",r[26]),{c(){L(e.$$.fragment)},m(o,l){M(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],Je(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){h(e.$$.fragment,o),n=!1},d(o){R(e,o)}}}function Ys(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[tr]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","too-large-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};5888&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Ks(r){let e,t,n;return t=new ge({props:{$$slots:{default:[rr]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","binary-file-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};2101632&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function er(r){let e,t,n,i;const s=[lr,or],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=s[t](r)),{c(){e=b("div"),n&&n.c(),x(e,"class","image-container svelte-1536g7w")},m(a,u){g(a,e,u),~t&&o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(H(),h(o[c],1,1,()=>{o[c]=null}),U()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),~t&&o[t].d()}}}function tr(r){let e,t,n,i,s,o,l,a=se(r[12])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=T('File "'),t=T(a),n=T('" is too large to display a diff (size: '),i=T(u),s=T(" bytes, max: "),o=T(Ji),l=T(" bytes).")},m(c,d){g(c,e,d),g(c,t,d),g(c,n,d),g(c,i,d),g(c,s,d),g(c,o,d),g(c,l,d)},p(c,d){4096&d[0]&&a!==(a=se(c[12])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(i,u)},d(c){c&&($(e),$(t),$(n),$(i),$(s),$(o),$(l))}}}function nr(r){let e,t,n,i=se(r[12])+"";return{c(){e=T("Binary file modified: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=se(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function ir(r){let e,t,n,i=se(r[12])+"";return{c(){e=T("Binary file deleted: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=se(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function sr(r){let e,t,n,i=se(r[12])+"";return{c(){e=T("Binary file added: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=se(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function rr(r){let e;function t(s,o){return s[21]||s[7]?sr:s[8]?ir:nr}let n=t(r),i=n(r);return{c(){i.c(),e=T(`
            No text preview available.`)},m(s,o){i.m(s,o),g(s,e,o)},p(s,o){n===(n=t(s))&&i?i.p(s,o):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},d(s){s&&$(e),i.d(s)}}}function or(r){let e,t,n,i,s,o,l,a;e=new ge({props:{class:"image-info-text",$$slots:{default:[cr]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&on(r);return{c(){L(e.$$.fragment),t=I(),n=b("img"),o=I(),u&&u.c(),l=xe(),Ne(n.src,i="data:"+r[19]+";base64,"+btoa(r[6]))||x(n,"src",i),x(n,"alt",s="Current "+se(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(c,d){M(e,c,d),g(c,t,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const p={};2101376&d[0]|8192&d[1]&&(p.$$scope={dirty:d,ctx:c}),e.$set(p),(!a||524352&d[0]&&!Ne(n.src,i="data:"+c[19]+";base64,"+btoa(c[6])))&&x(n,"src",i),(!a||4096&d[0]&&s!==(s="Current "+se(c[12])))&&x(n,"alt",s),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[21]?u?(u.p(c,d),2097217&d[0]&&f(u,1)):(u=on(c),u.c(),f(u,1),u.m(l.parentNode,l)):u&&(H(),h(u,1,1,()=>{u=null}),U())},i(c){a||(f(e.$$.fragment,c),f(u),a=!0)},o(c){h(e.$$.fragment,c),h(u),a=!1},d(c){c&&($(t),$(n),$(o),$(l)),R(e,c),u&&u.d(c)}}}function lr(r){let e,t,n,i;e=new ge({props:{class:"image-info-text",$$slots:{default:[pr]},$$scope:{ctx:r}}});let s=r[0].originalCode&&ln(r);return{c(){L(e.$$.fragment),t=I(),s&&s.c(),n=xe()},m(o,l){M(e,o,l),g(o,t,l),s&&s.m(o,l),g(o,n,l),i=!0},p(o,l){const a={};4096&l[0]|8192&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?s?(s.p(o,l),1&l[0]&&f(s,1)):(s=ln(o),s.c(),f(s,1),s.m(n.parentNode,n)):s&&(H(),h(s,1,1,()=>{s=null}),U())},i(o){i||(f(e.$$.fragment,o),f(s),i=!0)},o(o){h(e.$$.fragment,o),h(s),i=!1},d(o){o&&($(t),$(n)),R(e,o),s&&s.d(o)}}}function ar(r){let e;return{c(){e=T("Image modified")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function ur(r){let e;return{c(){e=T("New image added")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function cr(r){let e,t,n=se(r[12])+"";function i(l,a){return l[21]||l[7]?ur:ar}let s=i(r),o=s(r);return{c(){o.c(),e=T(": "),t=T(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){s!==(s=i(l))&&(o.d(1),o=s(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=se(l[12])+"")&&le(t,n)},d(l){l&&($(e),$(t)),o.d(l)}}}function on(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[dr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=I(),n=b("img"),Ne(n.src,i="data:"+lt(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+se(r[12])),x(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};8192&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+lt(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+se(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function dr(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function pr(r){let e,t,n=se(r[12])+"";return{c(){e=T("Image deleted: "),t=T(n)},m(i,s){g(i,e,s),g(i,t,s)},p(i,s){4096&s[0]&&n!==(n=se(i[12])+"")&&le(t,n)},d(i){i&&($(e),$(t))}}}function ln(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[fr]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=I(),n=b("img"),Ne(n.src,i="data:"+lt(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+se(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};8192&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+lt(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+se(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function fr(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function gr(r){let e,t,n,i;const s=[er,Ks,Ys,Xs],o=[];function l(a,u){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=s[t](r),{c(){e=b("div"),n.c(),x(e,"class","changes svelte-1536g7w")},m(a,u){g(a,e,u),o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(H(),h(o[c],1,1,()=>{o[c]=null}),U(),n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null))},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),o[t].d()}}}function $r(r){let e,t=se(r[12])+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){4096&i[0]&&t!==(t=se(n[12])+"")&&le(e,t)},d(n){n&&$(e)}}}function hr(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[$r]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};4096&i[0]|8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function an(r){let e,t,n=Ze(r[12])+"";return{c(){e=b("span"),t=T(n),x(e,"class","c-directory svelte-1536g7w")},m(i,s){g(i,e,s),q(e,t)},p(i,s){4096&s[0]&&n!==(n=Ze(i[12])+"")&&le(t,n)},d(i){i&&$(e)}}}function mr(r){let e,t,n,i=r[23]>0&&un(r),s=r[22]>0&&cn(r);return{c(){e=b("div"),i&&i.c(),t=I(),s&&s.c(),x(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),i&&i.m(e,null),q(e,t),s&&s.m(e,null),n=!0},p(o,l){o[23]>0?i?(i.p(o,l),8388608&l[0]&&f(i,1)):(i=un(o),i.c(),f(i,1),i.m(e,t)):i&&(H(),h(i,1,1,()=>{i=null}),U()),o[22]>0?s?(s.p(o,l),4194304&l[0]&&f(s,1)):(s=cn(o),s.c(),f(s,1),s.m(e,null)):s&&(H(),h(s,1,1,()=>{s=null}),U())},i(o){n||(f(i),f(s),n=!0)},o(o){h(i),h(s),n=!1},d(o){o&&$(e),i&&i.d(),s&&s.d()}}}function Dr(r){let e;return{c(){e=b("span"),e.textContent="New File",x(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:Y,i:Y,o:Y,d(t){t&&$(e)}}}function un(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[Fr]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),x(e,"class","additions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};8388608&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Fr(r){let e,t;return{c(){e=T("+"),t=T(r[23])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){8388608&i[0]&&le(t,n[23])},d(n){n&&($(e),$(t))}}}function cn(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[xr]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),x(e,"class","deletions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};4194304&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function xr(r){let e,t;return{c(){e=T("-"),t=T(r[22])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){4194304&i[0]&&le(t,n[22])},d(n){n&&($(e),$(t))}}}function Cr(r){let e;return{c(){e=T("Apply")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function kr(r){let e;return{c(){e=T("Applied")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function wr(r){let e,t,n;return t=new ut({}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","applied__icon svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function vr(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","applied svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function yr(r){let e,t,n,i,s;function o(p,F){return p[5]?kr:Cr}let l=o(r),a=l(r);const u=[vr,wr],c=[];function d(p,F){return p[5]?0:1}return t=d(r),n=c[t]=u[t](r),{c(){a.c(),e=I(),n.c(),i=xe()},m(p,F){a.m(p,F),g(p,e,F),c[t].m(p,F),g(p,i,F),s=!0},p(p,F){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let y=t;t=d(p),t!==y&&(H(),h(c[y],1,1,()=>{c[y]=null}),U(),n=c[t],n||(n=c[t]=u[t](p),n.c()),f(n,1),n.m(i.parentNode,i))},i(p){s||(f(n),s=!0)},o(p){h(n),s=!1},d(p){p&&($(e),$(i)),a.d(p),c[t].d(p)}}}function Ar(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[yr]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};16384&i[0]&&(s.disabled=n[14]),32&i[0]|8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function dn(r){let e,t;return e=new He({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[Er]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};2048&i[0]&&(s.content=n[11]),8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function br(r){let e,t;return e=new Ui({}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Er(r){let e,t;return e=new yt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[br]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function _r(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,y=Ze(r[12]);t=new ji({}),s=new He({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[hr]},$$scope:{ctx:r}}});let w=y&&an(r);const _=[Dr,mr],E=[];function z(D,C){return D[21]?0:1}a=z(r),u=E[a]=_[a](r),d=new He({props:{content:r[13],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[Ar]},$$scope:{ctx:r}}});let m=r[5]&&dn(r);return{c(){e=b("div"),L(t.$$.fragment),n=I(),i=b("div"),L(s.$$.fragment),o=I(),w&&w.c(),l=I(),u.c(),c=I(),L(d.$$.fragment),p=I(),m&&m.c(),x(i,"class","c-path svelte-1536g7w"),x(e,"slot","header"),x(e,"class","header svelte-1536g7w")},m(D,C){g(D,e,C),M(t,e,null),q(e,n),q(e,i),M(s,i,null),q(i,o),w&&w.m(i,null),q(e,l),E[a].m(e,null),q(e,c),M(d,e,null),q(e,p),m&&m.m(e,null),F=!0},p(D,C){const A={};2048&C[0]&&(A.content=D[11]),4096&C[0]|8192&C[1]&&(A.$$scope={dirty:C,ctx:D}),s.$set(A),4096&C[0]&&(y=Ze(D[12])),y?w?w.p(D,C):(w=an(D),w.c(),w.m(i,null)):w&&(w.d(1),w=null);let B=a;a=z(D),a===B?E[a].p(D,C):(H(),h(E[B],1,1,()=>{E[B]=null}),U(),u=E[a],u?u.p(D,C):(u=E[a]=_[a](D),u.c()),f(u,1),u.m(e,c));const O={};8192&C[0]&&(O.content=D[13]),16416&C[0]|8192&C[1]&&(O.$$scope={dirty:C,ctx:D}),d.$set(O),D[5]?m?(m.p(D,C),32&C[0]&&f(m,1)):(m=dn(D),m.c(),f(m,1),m.m(e,null)):m&&(H(),h(m,1,1,()=>{m=null}),U())},i(D){F||(f(t.$$.fragment,D),f(s.$$.fragment,D),f(u),f(d.$$.fragment,D),f(m),F=!0)},o(D){h(t.$$.fragment,D),h(s.$$.fragment,D),h(u),h(d.$$.fragment,D),h(m),F=!1},d(D){D&&$(e),R(t),R(s),w&&w.d(),E[a].d(),R(d),m&&m.d()}}}function Br(r){let e,t,n,i,s;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[_r],default:[gr]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Ii({props:l}),qe.push(()=>Ge(t,"collapsed",o)),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","c svelte-1536g7w"),x(e,"id",i=Mt(r[3])),Ae(e,"focused",r[24]===r[3])},m(a,u){g(a,e,u),M(t,e,null),s=!0},p(a,u){const c={};16777211&u[0]|8192&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],Je(()=>n=!1)),t.$set(c),(!s||8&u[0]&&i!==(i=Mt(a[3])))&&x(e,"id",i),(!s||16777224&u[0])&&Ae(e,"focused",a[24]===a[3])},i(a){s||(f(t.$$.fragment,a),s=!0)},o(a){h(t.$$.fragment,a),s=!1},d(a){a&&$(e),R(t)}}}function zr(r,e,t){let n,i,s,o,l,a,u,c,d,p,F,y,w,_,E,z,m,D,C,A,B,O,V;Me(r,Mi,v=>t(40,O=v));let{path:W}=e,{change:Q}=e,{descriptions:re=[]}=e,{areDescriptionsVisible:ae=!0}=e,{isExpandedDefault:ce}=e,{isCollapsed:de=!ce}=e,{isApplying:G}=e,{hasApplied:Fe}=e,{onApplyChanges:$e}=e,{onCodeChange:ie}=e,{onOpenFile:we}=e,{isAgentFromDifferentRepo:ve=!1}=e;const Ce=Yi();Me(r,Ce,v=>t(24,V=v));let ue=Q.modifiedCode,ke=C;function ye(){t(11,ke=`Open ${C??"file"}`)}return Ke(()=>{ye()}),r.$$set=v=>{"path"in v&&t(3,W=v.path),"change"in v&&t(0,Q=v.change),"descriptions"in v&&t(4,re=v.descriptions),"areDescriptionsVisible"in v&&t(1,ae=v.areDescriptionsVisible),"isExpandedDefault"in v&&t(29,ce=v.isExpandedDefault),"isCollapsed"in v&&t(2,de=v.isCollapsed),"isApplying"in v&&t(30,G=v.isApplying),"hasApplied"in v&&t(5,Fe=v.hasApplied),"onApplyChanges"in v&&t(31,$e=v.onApplyChanges),"onCodeChange"in v&&t(32,ie=v.onCodeChange),"onOpenFile"in v&&t(33,we=v.onOpenFile),"isAgentFromDifferentRepo"in v&&t(34,ve=v.isAgentFromDifferentRepo)},r.$$.update=()=>{var v;1&r.$$.dirty[0]&&t(6,ue=Q.modifiedCode),1&r.$$.dirty[0]&&t(39,n=Lt(Q.diff)),256&r.$$.dirty[1]&&t(23,i=n.additions),256&r.$$.dirty[1]&&t(22,s=n.deletions),1&r.$$.dirty[0]&&t(21,o=Wi(Q)),1&r.$$.dirty[0]&&t(20,l=Qi(Q)),8&r.$$.dirty[0]&&t(38,a=sn(W)),8&r.$$.dirty[0]&&t(19,u=lt(W)),8&r.$$.dirty[0]&&t(37,c=function(J){if(sn(J))return!1;const oe=Pt(J);return Js.includes(oe)}(W)),1&r.$$.dirty[0]&&t(10,d=((v=Q.originalCode)==null?void 0:v.length)||0),64&r.$$.dirty[0]&&t(9,p=(ue==null?void 0:ue.length)||0),1024&r.$$.dirty[0]&&t(36,F=rn(d)),512&r.$$.dirty[0]&&t(35,y=rn(p)),65&r.$$.dirty[0]&&t(8,w=!ue&&!!Q.originalCode),65&r.$$.dirty[0]&&t(7,_=!!ue&&!Q.originalCode),128&r.$$.dirty[1]&&t(18,E=a),192&r.$$.dirty[1]&&t(17,z=!a&&c),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,m=!a&&!c&&(y||w&&F||_&&y)),512&r.$$.dirty[1]&&t(15,D=Vi(O==null?void 0:O.category,O==null?void 0:O.intensity)),8&r.$$.dirty[0]&&t(12,C=Hi(W)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,A=G||ve),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,B=G?"Applying changes...":Fe?"Reapply changes to local file":ve?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[Q,ae,de,W,re,Fe,ue,_,w,p,d,ke,C,B,A,D,m,z,E,u,l,o,s,i,V,Ce,function(v){t(6,ue=v.detail.modifiedCode),ie==null||ie(ue)},function(){t(0,Q.modifiedCode=ue,Q),ie==null||ie(ue),$e==null||$e()},async function(){we&&(t(11,ke="Opening file..."),await we()?ye():(t(11,ke="Failed to open file. Does the file exist?"),setTimeout(()=>{ye()},2e3)))},ce,G,$e,ie,we,ve,y,F,c,a,n,O,function(v){ae=v,t(1,ae)},function(v){de=v,t(2,de)}]}let Lr=class extends ee{constructor(r){super(),te(this,r,zr,Br,X,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}};function pn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Mr(r){let e,t;return e=new Ls({props:{filename:r[0].name}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.filename=n[0].name),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Rr(r){let e,t;return e=new ct({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.icon=n[0].isExpanded?"chevron-down":"chevron-right"),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function qr(r){let e,t,n=(r[0].displayName||r[0].name)+"";return{c(){e=b("span"),t=T(n),x(e,"class","full-path-text svelte-qnxoj")},m(i,s){g(i,e,s),q(e,t)},p(i,s){1&s&&n!==(n=(i[0].displayName||i[0].name)+"")&&le(t,n)},d(i){i&&$(e)}}}function fn(r){let e,t,n=pe(Array.from(r[0].children.values()).sort($n)),i=[];for(let o=0;o<n.length;o+=1)i[o]=gn(pn(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){e=b("div");for(let o=0;o<i.length;o+=1)i[o].c();x(e,"class","tree-node__children svelte-qnxoj"),x(e,"role","group")},m(o,l){g(o,e,l);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(e,null);t=!0},p(o,l){if(3&l){let a;for(n=pe(Array.from(o[0].children.values()).sort($n)),a=0;a<n.length;a+=1){const u=pn(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=gn(u),i[a].c(),f(i[a],1),i[a].m(e,null))}for(H(),a=n.length;a<i.length;a+=1)s(a);U()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(Boolean);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Re(i,o)}}}function gn(r){let e,t;return e=new Ki({props:{node:r[6],indentLevel:r[1]+1}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.node=n[6]),2&i&&(s.indentLevel=n[1]+1),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Nr(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,y,w,_,E;const z=[Rr,Mr],m=[];function D(A,B){return A[0].isFile?1:0}o=D(r),l=m[o]=z[o](r),c=new ge({props:{size:1,$$slots:{default:[qr]},$$scope:{ctx:r}}});let C=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&fn(r);return{c(){e=b("div"),t=b("div"),n=b("div"),i=I(),s=b("div"),l.c(),a=I(),u=b("span"),L(c.$$.fragment),y=I(),C&&C.c(),x(n,"class","tree-node__indent svelte-qnxoj"),be(n,"width",6*r[1]+"px"),x(s,"class","tree-node__icon-container svelte-qnxoj"),x(u,"class","tree-node__label svelte-qnxoj"),x(u,"title",d=r[0].displayName||r[0].name),Ae(u,"full-path",r[0].displayName),x(t,"class","tree-node__content svelte-qnxoj"),x(t,"role","treeitem"),x(t,"tabindex","0"),x(t,"aria-selected",p=r[0].path===r[2]),x(t,"aria-expanded",F=r[0].isFile?void 0:r[0].isExpanded),Ae(t,"selected",r[0].path===r[2]),Ae(t,"collapsed-folder",r[0].displayName&&!r[0].isFile),x(e,"class","tree-node svelte-qnxoj")},m(A,B){g(A,e,B),q(e,t),q(t,n),q(t,i),q(t,s),m[o].m(s,null),q(t,a),q(t,u),M(c,u,null),q(e,y),C&&C.m(e,null),w=!0,_||(E=[rt(t,"click",r[4]),rt(t,"keydown",r[5])],_=!0)},p(A,[B]){(!w||2&B)&&be(n,"width",6*A[1]+"px");let O=o;o=D(A),o===O?m[o].p(A,B):(H(),h(m[O],1,1,()=>{m[O]=null}),U(),l=m[o],l?l.p(A,B):(l=m[o]=z[o](A),l.c()),f(l,1),l.m(s,null));const V={};513&B&&(V.$$scope={dirty:B,ctx:A}),c.$set(V),(!w||1&B&&d!==(d=A[0].displayName||A[0].name))&&x(u,"title",d),(!w||1&B)&&Ae(u,"full-path",A[0].displayName),(!w||5&B&&p!==(p=A[0].path===A[2]))&&x(t,"aria-selected",p),(!w||1&B&&F!==(F=A[0].isFile?void 0:A[0].isExpanded))&&x(t,"aria-expanded",F),(!w||5&B)&&Ae(t,"selected",A[0].path===A[2]),(!w||1&B)&&Ae(t,"collapsed-folder",A[0].displayName&&!A[0].isFile),!A[0].isFile&&A[0].isExpanded&&A[0].children.size>0?C?(C.p(A,B),1&B&&f(C,1)):(C=fn(A),C.c(),f(C,1),C.m(e,null)):C&&(H(),h(C,1,1,()=>{C=null}),U())},i(A){w||(f(l),f(c.$$.fragment,A),f(C),w=!0)},o(A){h(l),h(c.$$.fragment,A),h(C),w=!1},d(A){A&&$(e),m[o].d(),R(c),C&&C.d(),_=!1,Ri(E)}}}const $n=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Tr(r,e,t){let n,{node:i}=e,{indentLevel:s=0}=e;const o=Yi();function l(){i.isFile?o.set(i.path):t(0,i.isExpanded=!i.isExpanded,i)}return Me(r,o,a=>t(2,n=a)),r.$$set=a=>{"node"in a&&t(0,i=a.node),"indentLevel"in a&&t(1,s=a.indentLevel)},[i,s,n,o,l,a=>a.key==="Enter"&&l()]}class Ki extends ee{constructor(e){super(),te(this,e,Tr,Nr,X,{node:0,indentLevel:1})}}function hn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Or(r){let e,t,n=pe(Array.from(r[1].children.values()).sort(Dn)),i=[];for(let o=0;o<n.length;o+=1)i[o]=mn(hn(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=xe()},m(o,l){for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(2&l){let a;for(n=pe(Array.from(o[1].children.values()).sort(Dn)),a=0;a<n.length;a+=1){const u=hn(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=mn(u),i[a].c(),f(i[a],1),i[a].m(e.parentNode,e))}for(H(),a=n.length;a<i.length;a+=1)s(a);U()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(Boolean);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Re(i,o)}}}function Sr(r){let e,t,n;return t=new ge({props:{size:1,color:"neutral",$$slots:{default:[Ir]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","tree-view__empty svelte-1tnd9l7")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};128&s&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Pr(r){let e;return{c(){e=b("div"),e.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',x(e,"class","tree-view__loading svelte-1tnd9l7")},m(t,n){g(t,e,n)},p:Y,i:Y,o:Y,d(t){t&&$(e)}}}function mn(r){let e,t;return e=new Ki({props:{node:r[4],indentLevel:0}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};2&i&&(s.node=n[4]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Ir(r){let e;return{c(){e=T("No changed files")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function jr(r){let e,t,n,i,s;const o=[Pr,Sr,Or],l=[];function a(u,c){return u[0]?0:u[1].children.size===0?1:2}return n=a(r),i=l[n]=o[n](r),{c(){e=b("div"),t=b("div"),i.c(),x(t,"class","tree-view__content svelte-1tnd9l7"),x(t,"role","tree"),x(t,"aria-label","Changed Files"),x(e,"class","tree-view svelte-1tnd9l7")},m(u,c){g(u,e,c),q(e,t),l[n].m(t,null),s=!0},p(u,[c]){let d=n;n=a(u),n===d?l[n].p(u,c):(H(),h(l[d],1,1,()=>{l[d]=null}),U(),i=l[n],i?i.p(u,c):(i=l[n]=o[n](u),i.c()),f(i,1),i.m(t,null))},i(u){s||(f(i),s=!0)},o(u){h(i),s=!1},d(u){u&&$(e),l[n].d()}}}function Rt(r,e=!1){if(r.isFile)return;let t="";e&&(t=function(o){let l=o.path.split("/"),a=o;for(;;){const u=Array.from(a.children.values()).filter(d=>!d.isFile),c=Array.from(a.children.values()).filter(d=>d.isFile);if(u.length!==1||c.length!==0)break;a=u[0],l.push(a.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)Rt(o);const i=Array.from(r.children.values()).filter(o=>!o.isFile),s=Array.from(r.children.values()).filter(o=>o.isFile);if(i.length===1&&s.length===0){const o=i[0],l=o.name;if(e){r.displayName=t||`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${a}`;r.children.set(c,u)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${l}/${a}`;r.children.set(c,u)}r.children.delete(l)}}}const Dn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Vr(r,e,t){let n,{changedFiles:i=[]}=e,{isLoading:s=!1}=e;function o(l){const a={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(u=>{const c=u.change_type===zs.deleted?u.old_path:u.new_path;c&&function(d,p){const F=p.split("/");let y=d;for(let w=0;w<F.length;w++){const _=F[w],E=w===F.length-1,z=F.slice(0,w+1).join("/");y.children.has(_)||y.children.set(_,{name:_,path:z,isFile:E,children:new Map,isExpanded:!0}),y=y.children.get(_)}}(a,c)}),function(u){if(!u.isFile)if(u.path!=="")Rt(u);else{const c=Array.from(u.children.values()).filter(d=>!d.isFile);for(const d of c)Rt(d,!0)}}(a),a}return r.$$set=l=>{"changedFiles"in l&&t(2,i=l.changedFiles),"isLoading"in l&&t(0,s=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o(i))},[s,n,i]}class es extends ee{constructor(e){super(),te(this,e,Vr,jr,X,{changedFiles:2,isLoading:0})}}function Fn(r,e,t){const n=r.slice();return n[19]=e[t],n}function Hr(r){let e;return{c(){e=T("Changed files")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Ur(r){let e,t,n;return t=new ge({props:{size:1,color:"neutral",$$slots:{default:[Wr]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};4194304&s&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Zr(r){let e,t,n,i,s,o,l=[],a=new Map,u=r[9].length>0&&xn(r),c=pe(r[9]);const d=p=>p[19].qualifiedPathName.relPath;for(let p=0;p<c.length;p+=1){let F=Fn(r,c,p),y=d(F);a.set(y,l[p]=Cn(y,F))}return{c(){e=b("div"),t=b("div"),u&&u.c(),n=I(),i=b("div"),s=b("div");for(let p=0;p<l.length;p+=1)l[p].c();x(t,"class","c-edits-list-controls svelte-6iqvaj"),x(e,"class","c-edits-list-header svelte-6iqvaj"),x(s,"class","c-edits-section svelte-6iqvaj"),x(i,"class","c-edits-list svelte-6iqvaj")},m(p,F){g(p,e,F),q(e,t),u&&u.m(t,null),g(p,n,F),g(p,i,F),q(i,s);for(let y=0;y<l.length;y+=1)l[y]&&l[y].m(s,null);o=!0},p(p,F){p[9].length>0?u?(u.p(p,F),512&F&&f(u,1)):(u=xn(p),u.c(),f(u,1),u.m(t,null)):u&&(H(),h(u,1,1,()=>{u=null}),U()),2654&F&&(c=pe(p[9]),H(),l=Oi(l,F,d,1,p,c,a,s,Si,Cn,null,Fn),U())},i(p){if(!o){f(u);for(let F=0;F<c.length;F+=1)f(l[F]);o=!0}},o(p){h(u);for(let F=0;F<l.length;F+=1)h(l[F]);o=!1},d(p){p&&($(e),$(n),$(i)),u&&u.d();for(let F=0;F<l.length;F+=1)l[F].d()}}}function Wr(r){let e;return{c(){e=T("No changes to show")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function xn(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[7]||r[8]||r[3].length>0||!r[10],$$slots:{default:[Xr]},$$scope:{ctx:r}}}),e.$on("click",r[12]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1416&i&&(s.disabled=n[7]||n[8]||n[3].length>0||!n[10]),4194688&i&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Qr(r){let e;return{c(){e=T("Apply all")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Gr(r){let e;return{c(){e=T("All applied")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Jr(r){let e;return{c(){e=T("Applying...")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Xr(r){let e,t,n,i;function s(a,u){return a[7]?Jr:a[8]?Gr:Qr}let o=s(r),l=o(r);return n=new ut({}),{c(){l.c(),e=I(),t=b("div"),L(n.$$.fragment),x(t,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(a,u){l.m(a,u),g(a,e,u),g(a,t,u),M(n,t,null),i=!0},p(a,u){o!==(o=s(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(e.parentNode,e)))},i(a){i||(f(n.$$.fragment,a),i=!0)},o(a){h(n.$$.fragment,a),i=!1},d(a){a&&($(e),$(t)),l.d(a),R(n)}}}function Cn(r,e){let t,n,i,s,o;function l(...c){return e[16](e[19],...c)}function a(){return e[17](e[19])}function u(){return e[18](e[19])}return n=new Lr({props:{path:e[19].qualifiedPathName.relPath,change:e[19].diff,isApplying:e[3].includes(e[19].qualifiedPathName.relPath),hasApplied:e[4].includes(e[19].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:a,onOpenFile:e[2]?u:void 0,isExpandedDefault:!0}}),{key:r,first:null,c(){t=b("div"),L(n.$$.fragment),i=I(),x(t,"class",""),this.first=t},m(c,d){g(c,t,d),M(n,t,null),q(t,i),o=!0},p(c,d){e=c;const p={};512&d&&(p.path=e[19].qualifiedPathName.relPath),512&d&&(p.change=e[19].diff),520&d&&(p.isApplying=e[3].includes(e[19].qualifiedPathName.relPath)),528&d&&(p.hasApplied=e[4].includes(e[19].qualifiedPathName.relPath)),512&d&&(p.onCodeChange=l),578&d&&(p.onApplyChanges=a),516&d&&(p.onOpenFile=e[2]?u:void 0),n.$set(p)},i(c){o||(f(n.$$.fragment,c),c&&$s(()=>{o&&(s||(s=Jt(t,Yt,{},!0)),s.run(1))}),o=!0)},o(c){h(n.$$.fragment,c),c&&(s||(s=Jt(t,Yt,{},!1)),s.run(0)),o=!1},d(c){c&&$(t),R(n),c&&s&&s.end()}}}function Yr(r){let e,t,n,i,s,o,l,a,u,c,d,p;s=new ge({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[Hr]},$$scope:{ctx:r}}}),l=new es({props:{changedFiles:r[0],isLoading:r[5]}});const F=[Zr,Ur],y=[];function w(_,E){return _[9].length>0?0:1}return c=w(r),d=y[c]=F[c](r),{c(){e=b("div"),t=b("div"),n=b("div"),i=b("div"),L(s.$$.fragment),o=I(),L(l.$$.fragment),a=I(),u=b("div"),d.c(),x(i,"class","c-file-explorer__tree__header svelte-6iqvaj"),x(n,"class","c-file-explorer__tree svelte-6iqvaj"),x(u,"class","c-file-explorer__details svelte-6iqvaj"),x(t,"class","c-file-explorer__layout svelte-6iqvaj"),x(e,"class","c-edits-list-container svelte-6iqvaj")},m(_,E){g(_,e,E),q(e,t),q(t,n),q(n,i),M(s,i,null),q(i,o),M(l,i,null),q(t,a),q(t,u),y[c].m(u,null),p=!0},p(_,[E]){const z={};4194304&E&&(z.$$scope={dirty:E,ctx:_}),s.$set(z);const m={};1&E&&(m.changedFiles=_[0]),32&E&&(m.isLoading=_[5]),l.$set(m);let D=c;c=w(_),c===D?y[c].p(_,E):(H(),h(y[D],1,1,()=>{y[D]=null}),U(),d=y[c],d?d.p(_,E):(d=y[c]=F[c](_),d.c()),f(d,1),d.m(u,null))},i(_){p||(f(s.$$.fragment,_),f(l.$$.fragment,_),f(d),p=!0)},o(_){h(s.$$.fragment,_),h(l.$$.fragment,_),h(d),p=!1},d(_){_&&$(e),R(s),R(l),y[c].d()}}}function Kr(r,e,t){let n,i,s,o,l,{changedFiles:a}=e,{onApplyChanges:u}=e,{onOpenFile:c}=e,{pendingFiles:d=[]}=e,{appliedFiles:p=[]}=e,{isLoadingTreeView:F=!1}=e,y={},w=!1,_=!1;function E(z,m){t(6,y[z]=m,y)}return r.$$set=z=>{"changedFiles"in z&&t(0,a=z.changedFiles),"onApplyChanges"in z&&t(1,u=z.onApplyChanges),"onOpenFile"in z&&t(2,c=z.onOpenFile),"pendingFiles"in z&&t(3,d=z.pendingFiles),"appliedFiles"in z&&t(4,p=z.appliedFiles),"isLoadingTreeView"in z&&t(5,F=z.isLoadingTreeView)},r.$$.update=()=>{if(1&r.$$.dirty&&t(15,n=JSON.stringify(a)),16&r.$$.dirty&&t(13,i=JSON.stringify(p)),8&r.$$.dirty&&t(14,s=JSON.stringify(d)),32768&r.$$.dirty&&n&&(t(6,y={}),t(7,w=!1),t(8,_=!1)),65&r.$$.dirty&&t(9,l=a.map(z=>{const m=z.new_path||z.old_path,D=z.old_contents||"",C=z.new_contents||"",A=qs.generateDiff(z.old_path,z.new_path,D,C),B=function(O,V){const W=xt("oldFile","newFile",O,V,"","",{context:3}),Q=ks(W);let re=0,ae=0,ce=[];for(const de of Q)for(const G of de.hunks)for(const Fe of G.lines){const $e=Fe.startsWith("+"),ie=Fe.startsWith("-");$e&&re++,ie&&ae++,ce.push({value:Fe,added:$e,removed:ie})}return{totalAddedLines:re,totalRemovedLines:ae,changes:ce,diff:W}}(D,C);return y[m]||t(6,y[m]=C,y),{qualifiedPathName:{rootPath:"",relPath:m},lineChanges:B,oldContents:D,newContents:C,diff:A}})),57880&r.$$.dirty&&t(10,o=(()=>{if(n&&i&&s){const z=l.map(m=>m.qualifiedPathName.relPath);return z.length!==0&&z.some(m=>!p.includes(m)&&!d.includes(m))}return!1})()),664&r.$$.dirty&&w){const z=l.map(m=>m.qualifiedPathName.relPath);z.filter(m=>!p.includes(m)&&!d.includes(m)).length===0&&z.every(m=>p.includes(m)||d.includes(m))&&d.length===0&&p.length>0&&(t(7,w=!1),t(8,_=!0))}if(9104&r.$$.dirty&&l.length>0&&!w&&i){const z=l.map(m=>m.qualifiedPathName.relPath);if(z.length>0){const m=z.every(D=>p.includes(D));m&&p.length>0?t(8,_=!0):!m&&_&&t(8,_=!1)}}},[a,u,c,d,p,F,y,w,_,l,o,E,function(){if(!u)return;const z=l.map(D=>D.qualifiedPathName.relPath);if(z.every(D=>p.includes(D)))return void t(8,_=!0);const m=z.filter(D=>!p.includes(D)&&!d.includes(D));m.length!==0&&(t(7,w=!0),t(8,_=!1),m.forEach(D=>{const C=l.find(A=>A.qualifiedPathName.relPath===D);if(C){const A=y[D]||C.newContents;u(D,C.oldContents,A)}}))},i,s,n,(z,m)=>{E(z.qualifiedPathName.relPath,m)},z=>{const m=y[z.qualifiedPathName.relPath]||z.newContents;u(z.qualifiedPathName.relPath,z.oldContents,m)},z=>c(z.qualifiedPathName.relPath)]}class eo extends ee{constructor(e){super(),te(this,e,Kr,Yr,X,{changedFiles:0,onApplyChanges:1,onOpenFile:2,pendingFiles:3,appliedFiles:4,isLoadingTreeView:5})}}function kn(r,e,t){const n=r.slice();return n[3]=e[t],n}function wn(r){let e,t=pe(r[1].paths),n=[];for(let i=0;i<t.length;i+=1)n[i]=vn(kn(r,t,i));return{c(){for(let i=0;i<n.length;i+=1)n[i].c();e=xe()},m(i,s){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(i,s);g(i,e,s)},p(i,s){if(2&s){let o;for(t=pe(i[1].paths),o=0;o<t.length;o+=1){const l=kn(i,t,o);n[o]?n[o].p(l,s):(n[o]=vn(l),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(i){i&&$(e),Re(n,i)}}}function vn(r){let e,t;return{c(){e=qi("path"),x(e,"d",t=r[3]),x(e,"fill-rule","evenodd"),x(e,"clip-rule","evenodd")},m(n,i){g(n,e,i)},p(n,i){2&i&&t!==(t=n[3])&&x(e,"d",t)},d(n){n&&$(e)}}}function to(r){let e,t=r[1]&&wn(r);return{c(){e=qi("svg"),t&&t.c(),x(e,"width","14"),x(e,"viewBox","0 0 20 20"),x(e,"fill","currentColor"),x(e,"class","svelte-10h4f31")},m(n,i){g(n,e,i),t&&t.m(e,null)},p(n,i){n[1]?t?t.p(n,i):(t=wn(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&$(e),t&&t.d()}}}function no(r){let e,t;return e=new He({props:{content:`This is a ${r[0]} change`,triggerOn:[Xe.Hover],$$slots:{default:[to]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.content=`This is a ${n[0]} change`),66&i&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function io(r,e,t){let n,{type:i}=e;const s={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&t(0,i=o.type)},r.$$.update=()=>{1&r.$$.dirty&&t(1,n=s[i]??s.other)},[i,n]}class so extends ee{constructor(e){super(),te(this,e,io,no,X,{type:0})}}function yn(r,e,t){const n=r.slice();return n[47]=e[t],n[49]=t,n}function An(r){let e,t,n,i,s;t=new yt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[lo]},$$scope:{ctx:r}}}),t.$on("click",r[24]);let o=pe(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=bn(yn(r,o,u));const a=u=>h(l[u],1,1,()=>{l[u]=null});return{c(){e=b("div"),L(t.$$.fragment),n=I(),i=b("div");for(let u=0;u<l.length;u+=1)l[u].c();x(e,"class","toggle-button svelte-14s1ghg"),x(i,"class","descriptions svelte-14s1ghg"),be(i,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,e,c),M(t,e,null),g(u,n,c),g(u,i,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(i,null);s=!0},p(u,c){const d={};if(1&c[0]|524288&c[1]&&(d.$$scope={dirty:c,ctx:u}),t.$set(d),546&c[0]){let p;for(o=pe(u[1]),p=0;p<o.length;p+=1){const F=yn(u,o,p);l[p]?(l[p].p(F,c),f(l[p],1)):(l[p]=bn(F),l[p].c(),f(l[p],1),l[p].m(i,null))}for(H(),p=o.length;p<l.length;p+=1)a(p);U()}(!s||16&c[0])&&be(i,"transform","translateY("+-u[4]+"px)")},i(u){if(!s){f(t.$$.fragment,u);for(let c=0;c<o.length;c+=1)f(l[c]);s=!0}},o(u){h(t.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)h(l[c]);s=!1},d(u){u&&($(e),$(n),$(i)),R(t),Re(l,u)}}}function ro(r){let e,t;return e=new ct({props:{icon:"book"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function oo(r){let e,t;return e=new ct({props:{icon:"x"}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function lo(r){let e,t,n,i;const s=[oo,ro],o=[];function l(a,u){return a[0]?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),h(o[c],1,1,()=>{o[c]=null}),U(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function bn(r){let e,t,n,i;return t=new Gi({props:{markdown:r[47].text}}),{c(){e=b("div"),L(t.$$.fragment),n=I(),x(e,"class","description svelte-14s1ghg"),be(e,"top",(r[5][r[49]]||r[9](r[47]))+"px"),be(e,"--ds-panel-solid","transparent")},m(s,o){g(s,e,o),M(t,e,null),q(e,n),i=!0},p(s,o){const l={};2&o[0]&&(l.markdown=s[47].text),t.$set(l),(!i||34&o[0])&&be(e,"top",(s[5][s[49]]||s[9](s[47]))+"px")},i(s){i||(f(t.$$.fragment,s),i=!0)},o(s){h(t.$$.fragment,s),i=!1},d(s){s&&$(e),R(t)}}}function ao(r){let e,t,n,i,s=r[1].length>0&&An(r);return{c(){e=b("div"),t=b("div"),n=I(),s&&s.c(),x(t,"class","editor-container svelte-14s1ghg"),be(t,"height",r[3]+"px"),x(e,"class","monaco-diff-container svelte-14s1ghg"),Ae(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,e,l),q(e,t),r[23](t),q(e,n),s&&s.m(e,null),i=!0},p(o,l){(!i||8&l[0])&&be(t,"height",o[3]+"px"),o[1].length>0?s?(s.p(o,l),2&l[0]&&f(s,1)):(s=An(o),s.c(),f(s,1),s.m(e,null)):s&&(H(),h(s,1,1,()=>{s=null}),U()),(!i||3&l[0])&&Ae(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){i||(f(s),i=!0)},o(o){h(s),i=!1},d(o){o&&$(e),r[23](null),s&&s.d()}}}function uo(r,e,t){let n,i,s;const o=zi();let{originalCode:l=""}=e,{modifiedCode:a=""}=e,{path:u}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:F=[]}=e,{theme:y}=e,{areDescriptionsVisible:w=!0}=e,{isNewFile:_=!1}=e,{isDeletedFile:E=!1}=e;const z=Ot.getContext().monaco;let m,D,C,A;Me(r,z,k=>t(22,n=k));let B,O=[];const V=St();let W,Q=Ue(0);Me(r,Q,k=>t(4,i=k));let re=_?20*a.split(`
`).length+40:100;const ae=n?n.languages.getLanguages().map(k=>k.id):[];function ce(k,N){var S,j;if(N){const P=(S=N.split(".").pop())==null?void 0:S.toLowerCase();if(P){const Z=(j=n==null?void 0:n.languages.getLanguages().find(K=>{var ne;return(ne=K.extensions)==null?void 0:ne.includes("."+P)}))==null?void 0:j.id;if(Z&&ae.includes(Z))return Z}}return"plaintext"}const de=Ue({});Me(r,de,k=>t(5,s=k));let G=null;function Fe(){if(!m)return;O=O.filter(S=>(S.dispose(),!1));const k=m.getOriginalEditor(),N=m.getModifiedEditor();O.push(k.onDidScrollChange(()=>{Dt(Q,i=k.getScrollTop(),i)}),N.onDidScrollChange(()=>{Dt(Q,i=N.getScrollTop(),i)}))}function $e(){if(!m||!B)return;const k=m.getOriginalEditor(),N=m.getModifiedEditor();O.push(N.onDidContentSizeChange(()=>V.requestLayout()),k.onDidContentSizeChange(()=>V.requestLayout()),m.onDidUpdateDiff(()=>V.requestLayout()),N.onDidChangeHiddenAreas(()=>V.requestLayout()),k.onDidChangeHiddenAreas(()=>V.requestLayout()),N.onDidLayoutChange(()=>V.requestLayout()),k.onDidLayoutChange(()=>V.requestLayout()),N.onDidFocusEditorWidget(()=>{v(!0)}),k.onDidFocusEditorWidget(()=>{v(!0)}),N.onDidBlurEditorWidget(()=>{v(!1)}),k.onDidBlurEditorWidget(()=>{v(!1)}),N.onDidChangeModelContent(()=>{Ce=!0,ue=Date.now();const S=(A==null?void 0:A.getValue())||"";if(S===a)return;const j=S.replace(p.join(""),"").replace(F.join(""),"");o("codeChange",{modifiedCode:j});const P=setTimeout(()=>{Ce=!1},500);O.push({dispose:()=>clearTimeout(P)})})),function(){!B||!m||(G&&clearTimeout(G),G=setTimeout(()=>{if(!B.__hasClickListener){const S=j=>{const P=j.target;P&&(P.closest('[title="Show Unchanged Region"]')||P.closest('[title="Hide Unchanged Region"]'))&&we()};B.addEventListener("click",S),t(2,B.__hasClickListener=!0,B),O.push({dispose:()=>{B.removeEventListener("click",S)}})}m&&O.push(m.onDidUpdateDiff(()=>{we()}))},300))}()}Li(()=>{m==null||m.dispose(),D==null||D.dispose(),C==null||C.dispose(),A==null||A.dispose(),O.forEach(k=>k.dispose()),G&&clearTimeout(G),W==null||W()});let ie=null;function we(){ie&&clearTimeout(ie),ie=setTimeout(()=>{V.requestLayout(),ie=null},100),ie&&O.push({dispose:()=>{ie&&(clearTimeout(ie),ie=null)}})}function ve(k,N,S,j=[],P=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");C==null||C.dispose(),A==null||A.dispose(),N=N||"",S=S||"";const Z=j.join(""),K=P.join("");if(N=_?S.split(`
`).map(()=>" ").join(`
`):Z+N+K,S=Z+S+K,C=n.editor.createModel(N,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0),E&&(S=S.split(`
`).map(()=>" ").join(`
`)),t(21,A=n.editor.createModel(S,void 0,k!==void 0?n.Uri.parse("file://"+k+`#${crypto.randomUUID()}`):void 0)),m){m.setModel({original:C,modified:A});const ne=m.getOriginalEditor();ne&&ne.updateOptions({lineNumbers:"off"}),Fe(),G&&clearTimeout(G),G=setTimeout(()=>{$e(),G=null},300)}}Ke(()=>{if(n)if(_){t(20,D=n.editor.create(B,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:y,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:j=>`${d-p.length+j}`}));const k=ce(0,u);t(21,A=n.editor.createModel(a,k,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),D.setModel(A),O.push(D.onDidChangeModelContent(()=>{Ce=!0,ue=Date.now();const j=(A==null?void 0:A.getValue())||"";if(j===a)return;o("codeChange",{modifiedCode:j});const P=setTimeout(()=>{Ce=!1},500);O.push({dispose:()=>clearTimeout(P)})})),O.push(D.onDidFocusEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!0}})}),D.onDidBlurEditorWidget(()=>{D==null||D.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const N=D.getContentHeight();t(3,re=Math.max(N,60));const S=setTimeout(()=>{D==null||D.layout()},0);O.push({dispose:()=>clearTimeout(S)})}else t(19,m=n.editor.createDiffEditor(B,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:y,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:k=>`${d-p.length+k}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),W&&W(),W=V.registerEditor({editor:m,updateHeight:ye,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ve(u,l,a,p,F),Fe(),$e(),G&&clearTimeout(G),G=setTimeout(()=>{V.requestLayout(),G=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ce=!1,ue=0;function ke(k,N=!0){return m?(N?m.getModifiedEditor():m.getOriginalEditor()).getTopForLineNumber(k):18*k}function ye(){if(!m)return;const k=m.getModel(),N=k==null?void 0:k.original,S=k==null?void 0:k.modified;if(!N||!S)return;const j=m.getOriginalEditor(),P=m.getModifiedEditor(),Z=m.getLineChanges()||[];let K;if(Z.length===0){const ne=j.getContentHeight(),De=P.getContentHeight();K=Math.max(100,ne,De)}else{let ne=0,De=0;for(const Te of Z)Te.originalEndLineNumber>0&&(ne=Math.max(ne,Te.originalEndLineNumber)),Te.modifiedEndLineNumber>0&&(De=Math.max(De,Te.modifiedEndLineNumber));ne=Math.min(ne+3,N.getLineCount()),De=Math.min(De+3,S.getLineCount());const Le=j.getTopForLineNumber(ne),Se=P.getTopForLineNumber(De);K=Math.max(Le,Se)+60}t(3,re=Math.min(K,2e4)),m.layout(),oe()}function v(k){if(!m)return;const N=m.getOriginalEditor(),S=m.getModifiedEditor();N.updateOptions({scrollbar:{handleMouseWheel:k}}),S.updateOptions({scrollbar:{handleMouseWheel:k}})}function J(k){if(!m)return 0;const N=m.getModel(),S=N==null?void 0:N.original,j=N==null?void 0:N.modified;if(!S||!j)return 0;const P=ke(k.range.start+1,!1),Z=ke(k.range.start+1,!0);return P&&!Z?P:!P&&Z?Z:Math.min(P,Z)}function oe(){if(!m||c.length===0)return;const k={};c.forEach((N,S)=>{k[S]=J(N)}),de.set(k)}return r.$$set=k=>{"originalCode"in k&&t(10,l=k.originalCode),"modifiedCode"in k&&t(11,a=k.modifiedCode),"path"in k&&t(12,u=k.path),"descriptions"in k&&t(1,c=k.descriptions),"lineOffset"in k&&t(13,d=k.lineOffset),"extraPrefixLines"in k&&t(14,p=k.extraPrefixLines),"extraSuffixLines"in k&&t(15,F=k.extraSuffixLines),"theme"in k&&t(16,y=k.theme),"areDescriptionsVisible"in k&&t(0,w=k.areDescriptionsVisible),"isNewFile"in k&&t(17,_=k.isNewFile),"isDeletedFile"in k&&t(18,E=k.isDeletedFile)},r.$$.update=()=>{if(8051712&r.$$.dirty[0]&&(k=a,!(Ce||Date.now()-ue<1e3||A&&A.getValue()===p.join("")+k+F.join(""))))if(_&&D){if(A)A.setValue(a);else{const N=ce(0,u);n&&t(21,A=n.editor.createModel(a,N,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),A&&D.setModel(A)}t(3,re=20*a.split(`
`).length+40),D.layout()}else!_&&m&&(ve(u,l,a,p,F),V.requestLayout());var k;if(524290&r.$$.dirty[0]&&m&&c.length>0&&oe(),1181696&r.$$.dirty[0]&&_&&a&&D){const N=D.getContentHeight();t(3,re=Math.max(N,60)),D.layout()}},[w,c,B,re,i,s,z,Q,de,J,l,a,u,d,p,F,y,_,E,m,D,A,n,function(k){qe[k?"unshift":"push"](()=>{B=k,t(2,B)})},()=>t(0,w=!w)]}class co extends ee{constructor(e){super(),te(this,e,uo,ao,X,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17,isDeletedFile:18},null,[-1,-1])}}const po=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],fo=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],ts=1048576;function It(r){if(!r)return"";const e=r.lastIndexOf(".");return e===-1||e===r.length-1?"":r.substring(e+1).toLowerCase()}function at(r){switch(It(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function En(r){const e=It(r);return po.includes(e)}function _n(r){return r>ts}const go=Symbol("focusedPath");function Bn(r){return`file-diff-${Ie(r)}`}function $o(r){let e,t,n;function i(o){r[41](o)}let s={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(s.areDescriptionsVisible=r[1]),e=new co({props:s}),qe.push(()=>Ge(e,"areDescriptionsVisible",i)),e.$on("codeChange",r[26]),{c(){L(e.$$.fragment)},m(o,l){M(e,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!t&&2&l[0]&&(t=!0,a.areDescriptionsVisible=o[1],Je(()=>t=!1)),e.$set(a)},i(o){n||(f(e.$$.fragment,o),n=!0)},o(o){h(e.$$.fragment,o),n=!1},d(o){R(e,o)}}}function ho(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[Fo]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","too-large-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};5888&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function mo(r){let e,t,n;return t=new ge({props:{$$slots:{default:[wo]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","binary-file-message svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};2101632&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Do(r){let e,t,n,i;const s=[yo,vo],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(t=l(r))&&(n=o[t]=s[t](r)),{c(){e=b("div"),n&&n.c(),x(e,"class","image-container svelte-1536g7w")},m(a,u){g(a,e,u),~t&&o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?~t&&o[t].p(a,u):(n&&(H(),h(o[c],1,1,()=>{o[c]=null}),U()),~t?(n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null)):n=null)},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),~t&&o[t].d()}}}function Fo(r){let e,t,n,i,s,o,l,a=se(r[12])+"",u=(r[8]?r[10]:r[9])+"";return{c(){e=T('File "'),t=T(a),n=T('" is too large to display a diff (size: '),i=T(u),s=T(" bytes, max: "),o=T(ts),l=T(" bytes).")},m(c,d){g(c,e,d),g(c,t,d),g(c,n,d),g(c,i,d),g(c,s,d),g(c,o,d),g(c,l,d)},p(c,d){4096&d[0]&&a!==(a=se(c[12])+"")&&le(t,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&le(i,u)},d(c){c&&($(e),$(t),$(n),$(i),$(s),$(o),$(l))}}}function xo(r){let e,t,n,i=se(r[12])+"";return{c(){e=T("Binary file modified: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=se(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function Co(r){let e,t,n,i=se(r[12])+"";return{c(){e=T("Binary file deleted: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=se(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function ko(r){let e,t,n,i=se(r[12])+"";return{c(){e=T("Binary file added: "),t=T(i),n=T(".")},m(s,o){g(s,e,o),g(s,t,o),g(s,n,o)},p(s,o){4096&o[0]&&i!==(i=se(s[12])+"")&&le(t,i)},d(s){s&&($(e),$(t),$(n))}}}function wo(r){let e;function t(s,o){return s[21]||s[7]?ko:s[8]?Co:xo}let n=t(r),i=n(r);return{c(){i.c(),e=T(`
            No text preview available.`)},m(s,o){i.m(s,o),g(s,e,o)},p(s,o){n===(n=t(s))&&i?i.p(s,o):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},d(s){s&&$(e),i.d(s)}}}function vo(r){let e,t,n,i,s,o,l,a;e=new ge({props:{class:"image-info-text",$$slots:{default:[Eo]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&zn(r);return{c(){L(e.$$.fragment),t=I(),n=b("img"),o=I(),u&&u.c(),l=xe(),Ne(n.src,i="data:"+r[19]+";base64,"+btoa(r[6]))||x(n,"src",i),x(n,"alt",s="Current "+se(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(c,d){M(e,c,d),g(c,t,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const p={};2101376&d[0]|8192&d[1]&&(p.$$scope={dirty:d,ctx:c}),e.$set(p),(!a||524352&d[0]&&!Ne(n.src,i="data:"+c[19]+";base64,"+btoa(c[6])))&&x(n,"src",i),(!a||4096&d[0]&&s!==(s="Current "+se(c[12])))&&x(n,"alt",s),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[21]?u?(u.p(c,d),2097217&d[0]&&f(u,1)):(u=zn(c),u.c(),f(u,1),u.m(l.parentNode,l)):u&&(H(),h(u,1,1,()=>{u=null}),U())},i(c){a||(f(e.$$.fragment,c),f(u),a=!0)},o(c){h(e.$$.fragment,c),h(u),a=!1},d(c){c&&($(t),$(n),$(o),$(l)),R(e,c),u&&u.d(c)}}}function yo(r){let e,t,n,i;e=new ge({props:{class:"image-info-text",$$slots:{default:[Bo]},$$scope:{ctx:r}}});let s=r[0].originalCode&&Ln(r);return{c(){L(e.$$.fragment),t=I(),s&&s.c(),n=xe()},m(o,l){M(e,o,l),g(o,t,l),s&&s.m(o,l),g(o,n,l),i=!0},p(o,l){const a={};4096&l[0]|8192&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[0].originalCode?s?(s.p(o,l),1&l[0]&&f(s,1)):(s=Ln(o),s.c(),f(s,1),s.m(n.parentNode,n)):s&&(H(),h(s,1,1,()=>{s=null}),U())},i(o){i||(f(e.$$.fragment,o),f(s),i=!0)},o(o){h(e.$$.fragment,o),h(s),i=!1},d(o){o&&($(t),$(n)),R(e,o),s&&s.d(o)}}}function Ao(r){let e;return{c(){e=T("Image modified")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function bo(r){let e;return{c(){e=T("New image added")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Eo(r){let e,t,n=se(r[12])+"";function i(l,a){return l[21]||l[7]?bo:Ao}let s=i(r),o=s(r);return{c(){o.c(),e=T(": "),t=T(n)},m(l,a){o.m(l,a),g(l,e,a),g(l,t,a)},p(l,a){s!==(s=i(l))&&(o.d(1),o=s(l),o&&(o.c(),o.m(e.parentNode,e))),4096&a[0]&&n!==(n=se(l[12])+"")&&le(t,n)},d(l){l&&($(e),$(t)),o.d(l)}}}function zn(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[_o]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=I(),n=b("img"),Ne(n.src,i="data:"+at(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+se(r[12])),x(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};8192&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+at(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+se(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function _o(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Bo(r){let e,t,n=se(r[12])+"";return{c(){e=T("Image deleted: "),t=T(n)},m(i,s){g(i,e,s),g(i,t,s)},p(i,s){4096&s[0]&&n!==(n=se(i[12])+"")&&le(t,n)},d(i){i&&($(e),$(t))}}}function Ln(r){let e,t,n,i,s,o;return e=new ge({props:{class:"image-info-text",$$slots:{default:[zo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=I(),n=b("img"),Ne(n.src,i="data:"+at(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",i),x(n,"alt",s="Original "+se(r[12])),x(n,"class","image-preview svelte-1536g7w")},m(l,a){M(e,l,a),g(l,t,a),g(l,n,a),o=!0},p(l,a){const u={};8192&a[1]&&(u.$$scope={dirty:a,ctx:l}),e.$set(u),(!o||9&a[0]&&!Ne(n.src,i="data:"+at(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",i),(!o||4096&a[0]&&s!==(s="Original "+se(l[12])))&&x(n,"alt",s)},i(l){o||(f(e.$$.fragment,l),o=!0)},o(l){h(e.$$.fragment,l),o=!1},d(l){l&&($(t),$(n)),R(e,l)}}}function zo(r){let e;return{c(){e=T("Previous version:")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Lo(r){let e,t,n,i;const s=[Do,mo,ho,$o],o=[];function l(a,u){return a[18]?0:a[17]?1:a[16]?2:3}return t=l(r),n=o[t]=s[t](r),{c(){e=b("div"),n.c(),x(e,"class","changes svelte-1536g7w")},m(a,u){g(a,e,u),o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(H(),h(o[c],1,1,()=>{o[c]=null}),U(),n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null))},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),o[t].d()}}}function Mo(r){let e,t=se(r[12])+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){4096&i[0]&&t!==(t=se(n[12])+"")&&le(e,t)},d(n){n&&$(e)}}}function Ro(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[Mo]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};4096&i[0]|8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Mn(r){let e,t,n=Ze(r[12])+"";return{c(){e=b("span"),t=T(n),x(e,"class","c-directory svelte-1536g7w")},m(i,s){g(i,e,s),q(e,t)},p(i,s){4096&s[0]&&n!==(n=Ze(i[12])+"")&&le(t,n)},d(i){i&&$(e)}}}function qo(r){let e,t,n,i=r[23]>0&&Rn(r),s=r[22]>0&&qn(r);return{c(){e=b("div"),i&&i.c(),t=I(),s&&s.c(),x(e,"class","changes-indicator svelte-1536g7w")},m(o,l){g(o,e,l),i&&i.m(e,null),q(e,t),s&&s.m(e,null),n=!0},p(o,l){o[23]>0?i?(i.p(o,l),8388608&l[0]&&f(i,1)):(i=Rn(o),i.c(),f(i,1),i.m(e,t)):i&&(H(),h(i,1,1,()=>{i=null}),U()),o[22]>0?s?(s.p(o,l),4194304&l[0]&&f(s,1)):(s=qn(o),s.c(),f(s,1),s.m(e,null)):s&&(H(),h(s,1,1,()=>{s=null}),U())},i(o){n||(f(i),f(s),n=!0)},o(o){h(i),h(s),n=!1},d(o){o&&$(e),i&&i.d(),s&&s.d()}}}function No(r){let e;return{c(){e=b("span"),e.textContent="New File",x(e,"class","new-file-badge svelte-1536g7w")},m(t,n){g(t,e,n)},p:Y,i:Y,o:Y,d(t){t&&$(e)}}}function Rn(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[To]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),x(e,"class","additions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};8388608&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function To(r){let e,t;return{c(){e=T("+"),t=T(r[23])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){8388608&i[0]&&le(t,n[23])},d(n){n&&($(e),$(t))}}}function qn(r){let e,t,n;return t=new ge({props:{size:1,$$slots:{default:[Oo]},$$scope:{ctx:r}}}),{c(){e=b("span"),L(t.$$.fragment),x(e,"class","deletions svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};4194304&s[0]|8192&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Oo(r){let e,t;return{c(){e=T("-"),t=T(r[22])},m(n,i){g(n,e,i),g(n,t,i)},p(n,i){4194304&i[0]&&le(t,n[22])},d(n){n&&($(e),$(t))}}}function So(r){let e;return{c(){e=T("Apply")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Po(r){let e;return{c(){e=T("Applied")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Io(r){let e,t,n;return t=new ut({}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","applied__icon svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function jo(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","applied svelte-1536g7w")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Vo(r){let e,t,n,i,s;function o(p,F){return p[5]?Po:So}let l=o(r),a=l(r);const u=[jo,Io],c=[];function d(p,F){return p[5]?0:1}return t=d(r),n=c[t]=u[t](r),{c(){a.c(),e=I(),n.c(),i=xe()},m(p,F){a.m(p,F),g(p,e,F),c[t].m(p,F),g(p,i,F),s=!0},p(p,F){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(e.parentNode,e)));let y=t;t=d(p),t!==y&&(H(),h(c[y],1,1,()=>{c[y]=null}),U(),n=c[t],n||(n=c[t]=u[t](p),n.c()),f(n,1),n.m(i.parentNode,i))},i(p){s||(f(n),s=!0)},o(p){h(n),s=!1},d(p){p&&($(e),$(i)),a.d(p),c[t].d(p)}}}function Ho(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Vo]},$$scope:{ctx:r}}}),e.$on("click",r[27]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};16384&i[0]&&(s.disabled=n[14]),32&i[0]|8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Nn(r){let e,t;return e=new He({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[Zo]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};2048&i[0]&&(s.content=n[11]),8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Uo(r){let e,t;return e=new Ui({}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Zo(r){let e,t;return e=new yt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Uo]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};8192&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Wo(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,y=Ze(r[12]);t=new ji({}),s=new He({props:{content:r[11],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[Ro]},$$scope:{ctx:r}}});let w=y&&Mn(r);const _=[No,qo],E=[];function z(D,C){return D[21]?0:1}a=z(r),u=E[a]=_[a](r),d=new He({props:{content:r[13],triggerOn:[Xe.Hover],delayDurationMs:300,$$slots:{default:[Ho]},$$scope:{ctx:r}}});let m=r[5]&&Nn(r);return{c(){e=b("div"),L(t.$$.fragment),n=I(),i=b("div"),L(s.$$.fragment),o=I(),w&&w.c(),l=I(),u.c(),c=I(),L(d.$$.fragment),p=I(),m&&m.c(),x(i,"class","c-path svelte-1536g7w"),x(e,"slot","header"),x(e,"class","header svelte-1536g7w")},m(D,C){g(D,e,C),M(t,e,null),q(e,n),q(e,i),M(s,i,null),q(i,o),w&&w.m(i,null),q(e,l),E[a].m(e,null),q(e,c),M(d,e,null),q(e,p),m&&m.m(e,null),F=!0},p(D,C){const A={};2048&C[0]&&(A.content=D[11]),4096&C[0]|8192&C[1]&&(A.$$scope={dirty:C,ctx:D}),s.$set(A),4096&C[0]&&(y=Ze(D[12])),y?w?w.p(D,C):(w=Mn(D),w.c(),w.m(i,null)):w&&(w.d(1),w=null);let B=a;a=z(D),a===B?E[a].p(D,C):(H(),h(E[B],1,1,()=>{E[B]=null}),U(),u=E[a],u?u.p(D,C):(u=E[a]=_[a](D),u.c()),f(u,1),u.m(e,c));const O={};8192&C[0]&&(O.content=D[13]),16416&C[0]|8192&C[1]&&(O.$$scope={dirty:C,ctx:D}),d.$set(O),D[5]?m?(m.p(D,C),32&C[0]&&f(m,1)):(m=Nn(D),m.c(),f(m,1),m.m(e,null)):m&&(H(),h(m,1,1,()=>{m=null}),U())},i(D){F||(f(t.$$.fragment,D),f(s.$$.fragment,D),f(u),f(d.$$.fragment,D),f(m),F=!0)},o(D){h(t.$$.fragment,D),h(s.$$.fragment,D),h(u),h(d.$$.fragment,D),h(m),F=!1},d(D){D&&$(e),R(t),R(s),w&&w.d(),E[a].d(),R(d),m&&m.d()}}}function Qo(r){let e,t,n,i,s;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[Wo],default:[Lo]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),t=new Ii({props:l}),qe.push(()=>Ge(t,"collapsed",o)),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","c svelte-1536g7w"),x(e,"id",i=Bn(r[3])),Ae(e,"focused",r[24]===r[3])},m(a,u){g(a,e,u),M(t,e,null),s=!0},p(a,u){const c={};16777211&u[0]|8192&u[1]&&(c.$$scope={dirty:u,ctx:a}),!n&&4&u[0]&&(n=!0,c.collapsed=a[2],Je(()=>n=!1)),t.$set(c),(!s||8&u[0]&&i!==(i=Bn(a[3])))&&x(e,"id",i),(!s||16777224&u[0])&&Ae(e,"focused",a[24]===a[3])},i(a){s||(f(t.$$.fragment,a),s=!0)},o(a){h(t.$$.fragment,a),s=!1},d(a){a&&$(e),R(t)}}}function Go(r,e,t){let n,i,s,o,l,a,u,c,d,p,F,y,w,_,E,z,m,D,C,A,B,O,V;Me(r,Mi,v=>t(40,O=v));let{path:W}=e,{change:Q}=e,{descriptions:re=[]}=e,{areDescriptionsVisible:ae=!0}=e,{isExpandedDefault:ce}=e,{isCollapsed:de=!ce}=e,{isApplying:G}=e,{hasApplied:Fe}=e,{onApplyChanges:$e}=e,{onCodeChange:ie}=e,{onOpenFile:we}=e,{isAgentFromDifferentRepo:ve=!1}=e;const Ce=st(go);Me(r,Ce,v=>t(24,V=v));let ue=Q.modifiedCode,ke=C;function ye(){t(11,ke=`Open ${C??"file"}`)}return Ke(()=>{ye()}),r.$$set=v=>{"path"in v&&t(3,W=v.path),"change"in v&&t(0,Q=v.change),"descriptions"in v&&t(4,re=v.descriptions),"areDescriptionsVisible"in v&&t(1,ae=v.areDescriptionsVisible),"isExpandedDefault"in v&&t(29,ce=v.isExpandedDefault),"isCollapsed"in v&&t(2,de=v.isCollapsed),"isApplying"in v&&t(30,G=v.isApplying),"hasApplied"in v&&t(5,Fe=v.hasApplied),"onApplyChanges"in v&&t(31,$e=v.onApplyChanges),"onCodeChange"in v&&t(32,ie=v.onCodeChange),"onOpenFile"in v&&t(33,we=v.onOpenFile),"isAgentFromDifferentRepo"in v&&t(34,ve=v.isAgentFromDifferentRepo)},r.$$.update=()=>{var v;1&r.$$.dirty[0]&&t(6,ue=Q.modifiedCode),1&r.$$.dirty[0]&&t(39,n=vs(Q.diff)),256&r.$$.dirty[1]&&t(23,i=n.additions),256&r.$$.dirty[1]&&t(22,s=n.deletions),1&r.$$.dirty[0]&&t(21,o=ys(Q)),1&r.$$.dirty[0]&&t(20,l=As(Q)),8&r.$$.dirty[0]&&t(38,a=En(W)),8&r.$$.dirty[0]&&t(19,u=at(W)),8&r.$$.dirty[0]&&t(37,c=function(J){if(En(J))return!1;const oe=It(J);return fo.includes(oe)}(W)),1&r.$$.dirty[0]&&t(10,d=((v=Q.originalCode)==null?void 0:v.length)||0),64&r.$$.dirty[0]&&t(9,p=(ue==null?void 0:ue.length)||0),1024&r.$$.dirty[0]&&t(36,F=_n(d)),512&r.$$.dirty[0]&&t(35,y=_n(p)),65&r.$$.dirty[0]&&t(8,w=!ue&&!!Q.originalCode),65&r.$$.dirty[0]&&t(7,_=!!ue&&!Q.originalCode),128&r.$$.dirty[1]&&t(18,E=a),192&r.$$.dirty[1]&&t(17,z=!a&&c),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&t(16,m=!a&&!c&&(y||w&&F||_&&y)),512&r.$$.dirty[1]&&t(15,D=Vi(O==null?void 0:O.category,O==null?void 0:O.intensity)),8&r.$$.dirty[0]&&t(12,C=Hi(W)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(14,A=G||ve),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&t(13,B=G?"Applying changes...":Fe?"Reapply changes to local file":ve?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[Q,ae,de,W,re,Fe,ue,_,w,p,d,ke,C,B,A,D,m,z,E,u,l,o,s,i,V,Ce,function(v){t(6,ue=v.detail.modifiedCode),ie==null||ie(ue)},function(){t(0,Q.modifiedCode=ue,Q),ie==null||ie(ue),$e==null||$e()},async function(){we&&(t(11,ke="Opening file..."),await we()?ye():(t(11,ke="Failed to open file. Does the file exist?"),setTimeout(()=>{ye()},2e3)))},ce,G,$e,ie,we,ve,y,F,c,a,n,O,function(v){ae=v,t(1,ae)},function(v){de=v,t(2,de)}]}class Jo extends ee{constructor(e){super(),te(this,e,Go,Qo,X,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}}function Tn(r,e,t){const n=r.slice();return n[1]=e[t],n[3]=t,n}function Xo(r,e,t){const n=r.slice();return n[1]=e[t],n}function Yo(r,e,t){const n=r.slice();return n[1]=e[t],n}function Ko(r){let e;return{c(){e=b("div"),e.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',x(e,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(t,n){g(t,e,n)},p:Y,d(t){t&&$(e)}}}function el(r){let e,t,n,i,s=pe(Array(2)),o=[];for(let l=0;l<s.length;l+=1)o[l]=Ko(Yo(r,s,l));return{c(){e=b("div"),t=b("div"),t.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=I(),i=b("div");for(let l=0;l<o.length;l+=1)o[l].c();x(t,"class","c-skeleton-diff__header svelte-1eiztmz"),x(i,"class","c-skeleton-diff__changes svelte-1eiztmz"),x(e,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,a){g(l,e,a),q(e,t),q(e,n),q(e,i);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(i,null)},p:Y,d(l){l&&$(e),Re(o,l)}}}function On(r){let e,t,n,i,s,o,l=r[3]===0&&function(c){let d;return{c(){d=b("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',x(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(p,F){g(p,d,F)},d(p){p&&$(d)}}}(),a=pe(Array(2)),u=[];for(let c=0;c<a.length;c+=1)u[c]=el(Xo(r,a,c));return{c(){e=b("div"),t=b("div"),n=b("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',i=I(),l&&l.c(),s=I();for(let c=0;c<u.length;c+=1)u[c].c();o=I(),x(n,"class","c-skeleton-diff__content svelte-1eiztmz"),x(t,"class","c-skeleton-diff__header svelte-1eiztmz"),x(e,"class","c-skeleton-diff__section svelte-1eiztmz")},m(c,d){g(c,e,d),q(e,t),q(t,n),q(t,i),l&&l.m(t,null),q(e,s);for(let p=0;p<u.length;p+=1)u[p]&&u[p].m(e,null);q(e,o)},p(c,d){},d(c){c&&$(e),l&&l.d(),Re(u,c)}}}function tl(r){let e,t=pe(Array(r[0])),n=[];for(let i=0;i<t.length;i+=1)n[i]=On(Tn(r,t,i));return{c(){e=b("div");for(let i=0;i<n.length;i+=1)n[i].c();x(e,"class","c-skeleton-diff svelte-1eiztmz")},m(i,s){g(i,e,s);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(e,null)},p(i,[s]){if(1&s){let o;for(t=pe(Array(i[0])),o=0;o<t.length;o+=1){const l=Tn(i,t,o);n[o]?n[o].p(l,s):(n[o]=On(l),n[o].c(),n[o].m(e,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},i:Y,o:Y,d(i){i&&$(e),Re(n,i)}}}function nl(r,e,t){let{count:n=2}=e;return r.$$set=i=>{"count"in i&&t(0,n=i.count)},[n]}class il extends ee{constructor(e){super(),te(this,e,nl,tl,X,{count:0})}}function Sn(...r){return"/"+r.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function Pn(r){return r.startsWith("/")||r.startsWith("#")}function _t(r){let e,t;const n=r[5].default,i=Ee(n,r,r[4],null);let s=[{id:r[1]}],o={};for(let l=0;l<s.length;l+=1)o=Ni(o,s[l]);return{c(){e=b(`h${r[0].depth}`),i&&i.c(),Ft(`h${r[0].depth}`)(e,o)},m(l,a){g(l,e,a),i&&i.m(e,null),t=!0},p(l,a){i&&i.p&&(!t||16&a)&&_e(i,n,l,l[4],t?ze(n,l[4],a,null):Be(l[4]),null),Ft(`h${l[0].depth}`)(e,o=Ti(s,[(!t||2&a)&&{id:l[1]}]))},i(l){t||(f(i,l),t=!0)},o(l){h(i,l),t=!1},d(l){l&&$(e),i&&i.d(l)}}}function sl(r){let e,t,n=`h${r[0].depth}`,i=`h${r[0].depth}`&&_t(r);return{c(){i&&i.c(),e=xe()},m(s,o){i&&i.m(s,o),g(s,e,o),t=!0},p(s,[o]){`h${s[0].depth}`?n?X(n,`h${s[0].depth}`)?(i.d(1),i=_t(s),n=`h${s[0].depth}`,i.c(),i.m(e.parentNode,e)):i.p(s,o):(i=_t(s),n=`h${s[0].depth}`,i.c(),i.m(e.parentNode,e)):n&&(i.d(1),i=null,n=`h${s[0].depth}`)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function rl(r,e,t){let{$$slots:n={},$$scope:i}=e,{token:s}=e,{options:o}=e,l;return r.$$set=a=>{"token"in a&&t(0,s=a.token),"options"in a&&t(2,o=a.options),"$$scope"in a&&t(4,i=a.$$scope)},r.$$.update=()=>{var a,u;5&r.$$.dirty&&t(1,(a=s.text,u=o.slugger,l=u.slug(a).replace(/--+/g,"-")))},[s,l,o,void 0,i,n]}class ol extends ee{constructor(e){super(),te(this,e,rl,sl,X,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function ll(r){let e,t;const n=r[4].default,i=Ee(n,r,r[3],null);return{c(){e=b("blockquote"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&_e(i,n,s,s[3],t?ze(n,s[3],o,null):Be(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function al(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class ul extends ee{constructor(e){super(),te(this,e,al,ll,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function In(r,e,t){const n=r.slice();return n[3]=e[t],n}function jn(r){let e,t,n=pe(r[0]),i=[];for(let o=0;o<n.length;o+=1)i[o]=Vn(In(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=xe()},m(o,l){for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(7&l){let a;for(n=pe(o[0]),a=0;a<n.length;a+=1){const u=In(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=Vn(u),i[a].c(),f(i[a],1),i[a].m(e.parentNode,e))}for(H(),a=n.length;a<i.length;a+=1)s(a);U()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(Boolean);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Re(i,o)}}}function Vn(r){let e,t;return e=new ns({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.token=n[3]),2&i&&(s.renderers=n[1]),4&i&&(s.options=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function cl(r){let e,t,n=r[0]&&jn(r);return{c(){n&&n.c(),e=xe()},m(i,s){n&&n.m(i,s),g(i,e,s),t=!0},p(i,[s]){i[0]?n?(n.p(i,s),1&s&&f(n,1)):(n=jn(i),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(H(),h(n,1,1,()=>{n=null}),U())},i(i){t||(f(n),t=!0)},o(i){h(n),t=!1},d(i){i&&$(e),n&&n.d(i)}}}function dl(r,e,t){let{tokens:n}=e,{renderers:i}=e,{options:s}=e;return r.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,i=o.renderers),"options"in o&&t(2,s=o.options)},[n,i,s]}class bt extends ee{constructor(e){super(),te(this,e,dl,cl,X,{tokens:0,renderers:1,options:2})}}function Hn(r){let e,t,n;var i=r[1][r[0].type];function s(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[gl]},$$scope:{ctx:o}}}}return i&&(e=Xt(i,s(r))),{c(){e&&L(e.$$.fragment),t=xe()},m(o,l){e&&M(e,o,l),g(o,t,l),n=!0},p(o,l){if(3&l&&i!==(i=o[1][o[0].type])){if(e){H();const a=e;h(a.$$.fragment,1,0,()=>{R(a,1)}),U()}i?(e=Xt(i,s(o)),L(e.$$.fragment),f(e.$$.fragment,1),M(e,t.parentNode,t)):e=null}else if(i){const a={};1&l&&(a.token=o[0]),4&l&&(a.options=o[2]),2&l&&(a.renderers=o[1]),15&l&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)}},i(o){n||(e&&f(e.$$.fragment,o),n=!0)},o(o){e&&h(e.$$.fragment,o),n=!1},d(o){o&&$(t),e&&R(e,o)}}}function pl(r){let e,t=r[0].raw+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){1&i&&t!==(t=n[0].raw+"")&&le(e,t)},i:Y,o:Y,d(n){n&&$(e)}}}function fl(r){let e,t;return e=new bt({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.tokens=n[0].tokens),2&i&&(s.renderers=n[1]),4&i&&(s.options=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function gl(r){let e,t,n,i;const s=[fl,pl],o=[];function l(a,u){return"tokens"in a[0]&&a[0].tokens?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(H(),h(o[c],1,1,()=>{o[c]=null}),U(),t=o[e],t?t.p(a,u):(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function $l(r){let e,t,n=r[1][r[0].type]&&Hn(r);return{c(){n&&n.c(),e=xe()},m(i,s){n&&n.m(i,s),g(i,e,s),t=!0},p(i,[s]){i[1][i[0].type]?n?(n.p(i,s),3&s&&f(n,1)):(n=Hn(i),n.c(),f(n,1),n.m(e.parentNode,e)):n&&(H(),h(n,1,1,()=>{n=null}),U())},i(i){t||(f(n),t=!0)},o(i){h(n),t=!1},d(i){i&&$(e),n&&n.d(i)}}}function hl(r,e,t){let{token:n}=e,{renderers:i}=e,{options:s}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,i=o.renderers),"options"in o&&t(2,s=o.options)},[n,i,s]}class ns extends ee{constructor(e){super(),te(this,e,hl,$l,X,{token:0,renderers:1,options:2})}}function Un(r,e,t){const n=r.slice();return n[4]=e[t],n}function Zn(r){let e,t;return e=new ns({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.token={...n[4]}),2&i&&(s.options=n[1]),4&i&&(s.renderers=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Bt(r){let e,t,n,i=pe(r[0].items),s=[];for(let u=0;u<i.length;u+=1)s[u]=Zn(Un(r,i,u));const o=u=>h(s[u],1,1,()=>{s[u]=null});let l=[{start:t=r[0].start||1}],a={};for(let u=0;u<l.length;u+=1)a=Ni(a,l[u]);return{c(){e=b(r[3]);for(let u=0;u<s.length;u+=1)s[u].c();Ft(r[3])(e,a)},m(u,c){g(u,e,c);for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(e,null);n=!0},p(u,c){if(7&c){let d;for(i=pe(u[0].items),d=0;d<i.length;d+=1){const p=Un(u,i,d);s[d]?(s[d].p(p,c),f(s[d],1)):(s[d]=Zn(p),s[d].c(),f(s[d],1),s[d].m(e,null))}for(H(),d=i.length;d<s.length;d+=1)o(d);U()}Ft(u[3])(e,a=Ti(l,[(!n||1&c&&t!==(t=u[0].start||1))&&{start:t}]))},i(u){if(!n){for(let c=0;c<i.length;c+=1)f(s[c]);n=!0}},o(u){s=s.filter(Boolean);for(let c=0;c<s.length;c+=1)h(s[c]);n=!1},d(u){u&&$(e),Re(s,u)}}}function ml(r){let e,t=r[3],n=r[3]&&Bt(r);return{c(){n&&n.c(),e=xe()},m(i,s){n&&n.m(i,s),g(i,e,s)},p(i,[s]){i[3]?t?X(t,i[3])?(n.d(1),n=Bt(i),t=i[3],n.c(),n.m(e.parentNode,e)):n.p(i,s):(n=Bt(i),t=i[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=i[3])},i:Y,o(i){h(n,i)},d(i){i&&$(e),n&&n.d(i)}}}function Dl(r,e,t){let n,{token:i}=e,{options:s}=e,{renderers:o}=e;return r.$$set=l=>{"token"in l&&t(0,i=l.token),"options"in l&&t(1,s=l.options),"renderers"in l&&t(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&t(3,n=i.ordered?"ol":"ul")},[i,s,o,n]}class Fl extends ee{constructor(e){super(),te(this,e,Dl,ml,X,{token:0,options:1,renderers:2})}}function xl(r){let e,t;const n=r[4].default,i=Ee(n,r,r[3],null);return{c(){e=b("li"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&_e(i,n,s,s[3],t?ze(n,s[3],o,null):Be(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Cl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class kl extends ee{constructor(e){super(),te(this,e,Cl,xl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function wl(r){let e;return{c(){e=b("br")},m(t,n){g(t,e,n)},p:Y,i:Y,o:Y,d(t){t&&$(e)}}}function vl(r,e,t){return[void 0,void 0,void 0]}class yl extends ee{constructor(e){super(),te(this,e,vl,wl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Al(r){let e,t,n,i,s=r[0].text+"";return{c(){e=b("pre"),t=b("code"),n=T(s),x(t,"class",i=`lang-${r[0].lang}`)},m(o,l){g(o,e,l),q(e,t),q(t,n)},p(o,[l]){1&l&&s!==(s=o[0].text+"")&&le(n,s),1&l&&i!==(i=`lang-${o[0].lang}`)&&x(t,"class",i)},i:Y,o:Y,d(o){o&&$(e)}}}function bl(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class El extends ee{constructor(e){super(),te(this,e,bl,Al,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function _l(r){let e,t,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){e=b("code"),t=T(n)},m(i,s){g(i,e,s),q(e,t)},p(i,[s]){1&s&&n!==(n=i[0].raw.slice(1,i[0].raw.length-1)+"")&&le(t,n)},i:Y,o:Y,d(i){i&&$(e)}}}function Bl(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class zl extends ee{constructor(e){super(),te(this,e,Bl,_l,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Wn(r,e,t){const n=r.slice();return n[3]=e[t],n}function Qn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Gn(r,e,t){const n=r.slice();return n[9]=e[t],n}function Jn(r){let e,t,n,i;return t=new bt({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){e=b("th"),L(t.$$.fragment),n=I(),x(e,"scope","col")},m(s,o){g(s,e,o),M(t,e,null),q(e,n),i=!0},p(s,o){const l={};1&o&&(l.tokens=s[9].tokens),2&o&&(l.options=s[1]),4&o&&(l.renderers=s[2]),t.$set(l)},i(s){i||(f(t.$$.fragment,s),i=!0)},o(s){h(t.$$.fragment,s),i=!1},d(s){s&&$(e),R(t)}}}function Xn(r){let e,t,n;return t=new bt({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){e=b("td"),L(t.$$.fragment)},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};1&s&&(o.tokens=i[6].tokens),2&s&&(o.options=i[1]),4&s&&(o.renderers=i[2]),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Yn(r){let e,t,n,i=pe(r[3]),s=[];for(let l=0;l<i.length;l+=1)s[l]=Xn(Qn(r,i,l));const o=l=>h(s[l],1,1,()=>{s[l]=null});return{c(){e=b("tr");for(let l=0;l<s.length;l+=1)s[l].c();t=I()},m(l,a){g(l,e,a);for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(e,null);q(e,t),n=!0},p(l,a){if(7&a){let u;for(i=pe(l[3]),u=0;u<i.length;u+=1){const c=Qn(l,i,u);s[u]?(s[u].p(c,a),f(s[u],1)):(s[u]=Xn(c),s[u].c(),f(s[u],1),s[u].m(e,t))}for(H(),u=i.length;u<s.length;u+=1)o(u);U()}},i(l){if(!n){for(let a=0;a<i.length;a+=1)f(s[a]);n=!0}},o(l){s=s.filter(Boolean);for(let a=0;a<s.length;a+=1)h(s[a]);n=!1},d(l){l&&$(e),Re(s,l)}}}function Ll(r){let e,t,n,i,s,o,l=pe(r[0].header),a=[];for(let F=0;F<l.length;F+=1)a[F]=Jn(Gn(r,l,F));const u=F=>h(a[F],1,1,()=>{a[F]=null});let c=pe(r[0].rows),d=[];for(let F=0;F<c.length;F+=1)d[F]=Yn(Wn(r,c,F));const p=F=>h(d[F],1,1,()=>{d[F]=null});return{c(){e=b("table"),t=b("thead"),n=b("tr");for(let F=0;F<a.length;F+=1)a[F].c();i=I(),s=b("tbody");for(let F=0;F<d.length;F+=1)d[F].c()},m(F,y){g(F,e,y),q(e,t),q(t,n);for(let w=0;w<a.length;w+=1)a[w]&&a[w].m(n,null);q(e,i),q(e,s);for(let w=0;w<d.length;w+=1)d[w]&&d[w].m(s,null);o=!0},p(F,[y]){if(7&y){let w;for(l=pe(F[0].header),w=0;w<l.length;w+=1){const _=Gn(F,l,w);a[w]?(a[w].p(_,y),f(a[w],1)):(a[w]=Jn(_),a[w].c(),f(a[w],1),a[w].m(n,null))}for(H(),w=l.length;w<a.length;w+=1)u(w);U()}if(7&y){let w;for(c=pe(F[0].rows),w=0;w<c.length;w+=1){const _=Wn(F,c,w);d[w]?(d[w].p(_,y),f(d[w],1)):(d[w]=Yn(_),d[w].c(),f(d[w],1),d[w].m(s,null))}for(H(),w=c.length;w<d.length;w+=1)p(w);U()}},i(F){if(!o){for(let y=0;y<l.length;y+=1)f(a[y]);for(let y=0;y<c.length;y+=1)f(d[y]);o=!0}},o(F){a=a.filter(Boolean);for(let y=0;y<a.length;y+=1)h(a[y]);d=d.filter(Boolean);for(let y=0;y<d.length;y+=1)h(d[y]);o=!1},d(F){F&&$(e),Re(a,F),Re(d,F)}}}function Ml(r,e,t){let{token:n}=e,{options:i}=e,{renderers:s}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,i=o.options),"renderers"in o&&t(2,s=o.renderers)},[n,i,s]}class Rl extends ee{constructor(e){super(),te(this,e,Ml,Ll,X,{token:0,options:1,renderers:2})}}function ql(r){let e,t,n=r[0].text+"";return{c(){e=new hs(!1),t=xe(),e.a=t},m(i,s){e.m(n,i,s),g(i,t,s)},p(i,[s]){1&s&&n!==(n=i[0].text+"")&&e.p(n)},i:Y,o:Y,d(i){i&&($(t),e.d())}}}function Nl(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class Tl extends ee{constructor(e){super(),te(this,e,Nl,ql,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ol(r){let e,t;const n=r[4].default,i=Ee(n,r,r[3],null);return{c(){e=b("p"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&_e(i,n,s,s[3],t?ze(n,s[3],o,null):Be(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Sl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}let Pl=class extends ee{constructor(r){super(),te(this,r,Sl,Ol,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function Il(r){let e,t,n,i;const s=r[4].default,o=Ee(s,r,r[3],null);return{c(){e=b("a"),o&&o.c(),x(e,"href",t=Pn(r[0].href)?Sn(r[1].baseUrl,r[0].href):r[0].href),x(e,"title",n=r[0].title)},m(l,a){g(l,e,a),o&&o.m(e,null),i=!0},p(l,[a]){o&&o.p&&(!i||8&a)&&_e(o,s,l,l[3],i?ze(s,l[3],a,null):Be(l[3]),null),(!i||3&a&&t!==(t=Pn(l[0].href)?Sn(l[1].baseUrl,l[0].href):l[0].href))&&x(e,"href",t),(!i||1&a&&n!==(n=l[0].title))&&x(e,"title",n)},i(l){i||(f(o,l),i=!0)},o(l){h(o,l),i=!1},d(l){l&&$(e),o&&o.d(l)}}}function jl(r,e,t){let{$$slots:n={},$$scope:i}=e,{token:s}=e,{options:o}=e;return r.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(1,o=l.options),"$$scope"in l&&t(3,i=l.$$scope)},[s,o,void 0,i,n]}class Vl extends ee{constructor(e){super(),te(this,e,jl,Il,X,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function Hl(r){let e;const t=r[4].default,n=Ee(t,r,r[3],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,[s]){n&&n.p&&(!e||8&s)&&_e(n,t,i,i[3],e?ze(t,i[3],s,null):Be(i[3]),null)},i(i){e||(f(n,i),e=!0)},o(i){h(n,i),e=!1},d(i){n&&n.d(i)}}}function Ul(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Zl extends ee{constructor(e){super(),te(this,e,Ul,Hl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Wl(r){let e,t;const n=r[4].default,i=Ee(n,r,r[3],null);return{c(){e=b("dfn"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&_e(i,n,s,s[3],t?ze(n,s[3],o,null):Be(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Ql(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Gl extends ee{constructor(e){super(),te(this,e,Ql,Wl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Jl(r){let e,t;const n=r[4].default,i=Ee(n,r,r[3],null);return{c(){e=b("del"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&_e(i,n,s,s[3],t?ze(n,s[3],o,null):Be(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Xl(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Yl extends ee{constructor(e){super(),te(this,e,Xl,Jl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Kl(r){let e,t;const n=r[4].default,i=Ee(n,r,r[3],null);return{c(){e=b("em"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&_e(i,n,s,s[3],t?ze(n,s[3],o,null):Be(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function ea(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class ta extends ee{constructor(e){super(),te(this,e,ea,Kl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function na(r){let e;return{c(){e=b("hr")},m(t,n){g(t,e,n)},p:Y,i:Y,o:Y,d(t){t&&$(e)}}}function ia(r,e,t){return[void 0,void 0,void 0]}class sa extends ee{constructor(e){super(),te(this,e,ia,na,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ra(r){let e,t;const n=r[4].default,i=Ee(n,r,r[3],null);return{c(){e=b("strong"),i&&i.c()},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||8&o)&&_e(i,n,s,s[3],t?ze(n,s[3],o,null):Be(s[3]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function oa(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class la extends ee{constructor(e){super(),te(this,e,oa,ra,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function aa(r){let e,t,n,i;return{c(){e=b("img"),Ne(e.src,t=r[0].href)||x(e,"src",t),x(e,"title",n=r[0].title),x(e,"alt",i=r[0].text),x(e,"class","markdown-image svelte-z38cge")},m(s,o){g(s,e,o)},p(s,[o]){1&o&&!Ne(e.src,t=s[0].href)&&x(e,"src",t),1&o&&n!==(n=s[0].title)&&x(e,"title",n),1&o&&i!==(i=s[0].text)&&x(e,"alt",i)},i:Y,o:Y,d(s){s&&$(e)}}}function ua(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n,void 0,void 0]}class ca extends ee{constructor(e){super(),te(this,e,ua,aa,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function da(r){let e;const t=r[4].default,n=Ee(t,r,r[3],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,[s]){n&&n.p&&(!e||8&s)&&_e(n,t,i,i[3],e?ze(t,i[3],s,null):Be(i[3]),null)},i(i){e||(f(n,i),e=!0)},o(i){h(n,i),e=!1},d(i){n&&n.d(i)}}}function pa(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(3,i=s.$$scope)},[void 0,void 0,void 0,i,n]}class Kn extends ee{constructor(e){super(),te(this,e,pa,da,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function fa(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let et={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function ei(r){et=r}const is=/[&<>"']/,ga=new RegExp(is.source,"g"),ss=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,$a=new RegExp(ss.source,"g"),ha={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ti=r=>ha[r];function Oe(r,e){if(e){if(is.test(r))return r.replace(ga,ti)}else if(ss.test(r))return r.replace($a,ti);return r}const ma=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Da(r){return r.replace(ma,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const Fa=/(^|[^\[])\^/g;function me(r,e){let t=typeof r=="string"?r:r.source;e=e||"";const n={replace:(i,s)=>{let o=typeof s=="string"?s:s.source;return o=o.replace(Fa,"$1"),t=t.replace(i,o),n},getRegex:()=>new RegExp(t,e)};return n}function ni(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const nt={exec:()=>null};function ii(r,e){const t=r.replace(/\|/g,(i,s,o)=>{let l=!1,a=s;for(;--a>=0&&o[a]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function ht(r,e,t){const n=r.length;if(n===0)return"";let i=0;for(;i<n;){const s=r.charAt(n-i-1);if(s!==e||t){if(s===e||!t)break;i++}else i++}return r.slice(0,n-i)}function si(r,e,t,n){const i=e.href,s=e.title?Oe(e.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:t,href:i,title:s,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:t,href:i,title:s,text:Oe(o)}}class kt{constructor(e){fe(this,"options");fe(this,"rules");fe(this,"lexer");this.options=e||et}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:ht(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],i=function(s,o){const l=s.match(/^(\s+)(?:```)/);if(l===null)return o;const a=l[1];return o.split(`
`).map(u=>{const c=u.match(/^\s+/);if(c===null)return u;const[d]=c;return d.length>=a.length?u.slice(a.length):u}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:i}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const i=ht(n,"#");this.options.pedantic?n=i.trim():i&&!/ $/.test(i)||(n=i.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=ht(t[0].replace(/^ *>[ \t]?/gm,""),`
`),i=this.lexer.state.top;this.lexer.state.top=!0;const s=this.lexer.blockTokens(n);return this.lexer.state.top=i,{type:"blockquote",raw:t[0],tokens:s,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const i=n.length>1,s={type:"list",raw:"",ordered:i,start:i?+n.slice(0,-1):"",loose:!1,items:[]};n=i?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=i?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",a="",u=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let d=t[2].split(`
`,1)[0].replace(/^\t+/,E=>" ".repeat(3*E.length)),p=e.split(`
`,1)[0],F=0;this.options.pedantic?(F=2,a=d.trimStart()):(F=t[2].search(/[^ ]/),F=F>4?1:F,a=d.slice(F),F+=t[1].length);let y=!1;if(!d&&/^ *$/.test(p)&&(l+=p+`
`,e=e.substring(p.length+1),c=!0),!c){const E=new RegExp(`^ {0,${Math.min(3,F-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),z=new RegExp(`^ {0,${Math.min(3,F-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),m=new RegExp(`^ {0,${Math.min(3,F-1)}}(?:\`\`\`|~~~)`),D=new RegExp(`^ {0,${Math.min(3,F-1)}}#`);for(;e;){const C=e.split(`
`,1)[0];if(p=C,this.options.pedantic&&(p=p.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),m.test(p)||D.test(p)||E.test(p)||z.test(e))break;if(p.search(/[^ ]/)>=F||!p.trim())a+=`
`+p.slice(F);else{if(y||d.search(/[^ ]/)>=4||m.test(d)||D.test(d)||z.test(d))break;a+=`
`+p}y||p.trim()||(y=!0),l+=C+`
`,e=e.substring(C.length+1),d=p.slice(F)}}s.loose||(u?s.loose=!0:/\n *\n *$/.test(l)&&(u=!0));let w,_=null;this.options.gfm&&(_=/^\[[ xX]\] /.exec(a),_&&(w=_[0]!=="[ ] ",a=a.replace(/^\[[ xX]\] +/,""))),s.items.push({type:"list_item",raw:l,task:!!_,checked:w,loose:!1,text:a,tokens:[]}),s.raw+=l}s.items[s.items.length-1].raw=l.trimEnd(),s.items[s.items.length-1].text=a.trimEnd(),s.raw=s.raw.trimEnd();for(let c=0;c<s.items.length;c++)if(this.lexer.state.top=!1,s.items[c].tokens=this.lexer.blockTokens(s.items[c].text,[]),!s.loose){const d=s.items[c].tokens.filter(F=>F.type==="space"),p=d.length>0&&d.some(F=>/\n.*\n/.test(F.raw));s.loose=p}if(s.loose)for(let c=0;c<s.items.length;c++)s.items[c].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),i=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:i,title:s}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=ii(t[1]),i=t[2].replace(/^\||\| *$/g,"").split("|"),s=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===i.length){for(const l of i)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of s)o.rows.push(ii(l,o.header.length).map(a=>({text:a,tokens:this.lexer.inline(a)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:Oe(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=ht(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,a){if(l.indexOf(a[1])===-1)return-1;let u=0;for(let c=0;c<l.length;c++)if(l[c]==="\\")c++;else if(l[c]===a[0])u++;else if(l[c]===a[1]&&(u--,u<0))return c;return-1}(t[2],"()");if(o>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let i=t[2],s="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);o&&(i=o[1],s=o[3])}else s=t[3]?t[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(i=this.options.pedantic&&!/>$/.test(n)?i.slice(1):i.slice(1,-1)),si(t,{href:i&&i.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const i=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!i){const s=n[0].charAt(0);return{type:"text",raw:s,text:s}}return si(n,i,n[0],this.lexer)}}emStrong(e,t,n=""){let i=this.rules.inline.emStrongLDelim.exec(e);if(i&&!(i[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(i[1]||i[2])||!n||this.rules.inline.punctuation.exec(n))){const s=[...i[0]].length-1;let o,l,a=s,u=0;const c=i[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+s);(i=c.exec(t))!=null;){if(o=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!o)continue;if(l=[...o].length,i[3]||i[4]){a+=l;continue}if((i[5]||i[6])&&s%3&&!((s+l)%3)){u+=l;continue}if(a-=l,a>0)continue;l=Math.min(l,l+a+u);const d=[...i[0]][0].length,p=e.slice(0,s+i.index+d+l);if(Math.min(s,l)%2){const y=p.slice(1,-1);return{type:"em",raw:p,text:y,tokens:this.lexer.inlineTokens(y)}}const F=p.slice(2,-2);return{type:"strong",raw:p,text:F,tokens:this.lexer.inlineTokens(F)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const i=/[^ ]/.test(n),s=/^ /.test(n)&&/ $/.test(n);return i&&s&&(n=n.substring(1,n.length-1)),n=Oe(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,i;return t[2]==="@"?(n=Oe(t[1]),i="mailto:"+n):(n=Oe(t[1]),i=n),{type:"link",raw:t[0],text:n,href:i,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let i,s;if(t[2]==="@")i=Oe(t[0]),s="mailto:"+i;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);i=Oe(t[0]),s=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:i,href:s,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:Oe(t[0]),{type:"text",raw:t[0],text:n}}}}const dt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,rs=/(?:[*+-]|\d{1,9}[.)])/,os=me(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,rs).getRegex(),jt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Vt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,xa=me(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Vt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Ca=me(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,rs).getRegex(),Et="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Ht=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,ka=me("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Ht).replace("tag",Et).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ri=me(jt).replace("hr",dt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex(),Ut={blockquote:me(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ri).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:xa,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:dt,html:ka,lheading:os,list:Ca,newline:/^(?: *(?:\n|$))+/,paragraph:ri,table:nt,text:/^[^\n]+/},oi=me("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",dt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex(),wa={...Ut,table:oi,paragraph:me(jt).replace("hr",dt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",oi).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Et).getRegex()},va={...Ut,html:me(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ht).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:nt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:me(jt).replace("hr",dt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",os).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ls=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,as=/^( {2,}|\\)\n(?!\s*$)/,pt="\\p{P}$+<=>`^|~",ya=me(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,pt).getRegex(),Aa=me(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,pt).getRegex(),ba=me("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,pt).getRegex(),Ea=me("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,pt).getRegex(),_a=me(/\\([punct])/,"gu").replace(/punct/g,pt).getRegex(),Ba=me(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),za=me(Ht).replace("(?:-->|$)","-->").getRegex(),La=me("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",za).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),wt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ma=me(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",wt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),li=me(/^!?\[(label)\]\[(ref)\]/).replace("label",wt).replace("ref",Vt).getRegex(),ai=me(/^!?\[(ref)\](?:\[\])?/).replace("ref",Vt).getRegex(),Zt={_backpedal:nt,anyPunctuation:_a,autolink:Ba,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:as,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:nt,emStrongLDelim:Aa,emStrongRDelimAst:ba,emStrongRDelimUnd:Ea,escape:ls,link:Ma,nolink:ai,punctuation:ya,reflink:li,reflinkSearch:me("reflink|nolink(?!\\()","g").replace("reflink",li).replace("nolink",ai).getRegex(),tag:La,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:nt},Ra={...Zt,link:me(/^!?\[(label)\]\((.*?)\)/).replace("label",wt).getRegex(),reflink:me(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",wt).getRegex()},qt={...Zt,escape:me(ls).replace("])","~|])").getRegex(),url:me(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},qa={...qt,br:me(as).replace("{2,}","*").getRegex(),text:me(qt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},mt={normal:Ut,gfm:wa,pedantic:va},tt={normal:Zt,gfm:qt,breaks:qa,pedantic:Ra};class Pe{constructor(e){fe(this,"tokens");fe(this,"options");fe(this,"state");fe(this,"tokenizer");fe(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||et,this.options.tokenizer=this.options.tokenizer||new kt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:mt.normal,inline:tt.normal};this.options.pedantic?(t.block=mt.pedantic,t.inline=tt.pedantic):this.options.gfm&&(t.block=mt.gfm,this.options.breaks?t.inline=tt.breaks:t.inline=tt.gfm),this.tokenizer.rules=t}static get rules(){return{block:mt,inline:tt}}static lex(e,t){return new Pe(t).lex(e)}static lexInline(e,t){return new Pe(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,i,s,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(l,a,u)=>a+"    ".repeat(u.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),i=t[t.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?t.push(n):(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),i=t[t.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(i.raw+=`
`+n.raw,i.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(s=e,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const a=e.slice(1);let u;this.options.extensions.startBlock.forEach(c=>{u=c.call({lexer:this},a),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(s=e.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(s)))i=t[t.length-1],o&&i.type==="paragraph"?(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n),o=s.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),i=t[t.length-1],i&&i.type==="text"?(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n);else if(e){const l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,i,s,o,l,a,u=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(u))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(u))!=null;)u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(u))!=null;)u=u.slice(0,o.index)+"++"+u.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),i=t[t.length-1],i&&n.type==="text"&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),i=t[t.length-1],i&&n.type==="text"&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,u,a))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(s=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=e.slice(1);let p;this.options.extensions.startInline.forEach(F=>{p=F.call({lexer:this},d),typeof p=="number"&&p>=0&&(c=Math.min(c,p))}),c<1/0&&c>=0&&(s=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(s))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(a=n.raw.slice(-1)),l=!0,i=t[t.length-1],i&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class vt{constructor(e){fe(this,"options");this.options=e||et}code(e,t,n){var s;const i=(s=(t||"").match(/^\S*/))==null?void 0:s[0];return e=e.replace(/\n$/,"")+`
`,i?'<pre><code class="language-'+Oe(i)+'">'+(n?e:Oe(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:Oe(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const i=t?"ol":"ul";return"<"+i+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+i+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const i=ni(e);if(i===null)return n;let s='<a href="'+(e=i)+'"';return t&&(s+=' title="'+t+'"'),s+=">"+n+"</a>",s}image(e,t,n){const i=ni(e);if(i===null)return n;let s=`<img src="${e=i}" alt="${n}"`;return t&&(s+=` title="${t}"`),s+=">",s}text(e){return e}}class Wt{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class je{constructor(e){fe(this,"options");fe(this,"renderer");fe(this,"textRenderer");this.options=e||et,this.options.renderer=this.options.renderer||new vt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Wt}static parse(e,t){return new je(t).parse(e)}static parseInline(e,t){return new je(t).parseInline(e)}parse(e,t=!0){let n="";for(let i=0;i<e.length;i++){const s=e[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=s,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(s.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=s;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Da(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=s;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=s;let l="",a="";for(let c=0;c<o.header.length;c++)a+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});l+=this.renderer.tablerow(a);let u="";for(let c=0;c<o.rows.length;c++){const d=o.rows[c];a="";for(let p=0;p<d.length;p++)a+=this.renderer.tablecell(this.parseInline(d[p].tokens),{header:!1,align:o.align[p]});u+=this.renderer.tablerow(a)}n+=this.renderer.table(l,u);continue}case"blockquote":{const o=s,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=s,l=o.ordered,a=o.start,u=o.loose;let c="";for(let d=0;d<o.items.length;d++){const p=o.items[d],F=p.checked,y=p.task;let w="";if(p.task){const _=this.renderer.checkbox(!!F);u?p.tokens.length>0&&p.tokens[0].type==="paragraph"?(p.tokens[0].text=_+" "+p.tokens[0].text,p.tokens[0].tokens&&p.tokens[0].tokens.length>0&&p.tokens[0].tokens[0].type==="text"&&(p.tokens[0].tokens[0].text=_+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:_+" "}):w+=_+" "}w+=this.parse(p.tokens,u),c+=this.renderer.listitem(w,y,!!F)}n+=this.renderer.list(c,l,a);continue}case"html":{const o=s;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=s;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=s,l=o.tokens?this.parseInline(o.tokens):o.text;for(;i+1<e.length&&e[i+1].type==="text";)o=e[++i],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let i=0;i<e.length;i++){const s=e[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){n+=o||"";continue}}switch(s.type){case"escape":{const o=s;n+=t.text(o.text);break}case"html":{const o=s;n+=t.html(o.text);break}case"link":{const o=s;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=s;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=s;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=s;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=s;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=s;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=s;n+=t.text(o.text);break}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class it{constructor(e){fe(this,"options");this.options=e||et}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}fe(it,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var Ye,Nt,us,Bi;const We=new(Bi=class{constructor(...r){Gt(this,Ye);fe(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});fe(this,"options",this.setOptions);fe(this,"parse",gt(this,Ye,Nt).call(this,Pe.lex,je.parse));fe(this,"parseInline",gt(this,Ye,Nt).call(this,Pe.lexInline,je.parseInline));fe(this,"Parser",je);fe(this,"Renderer",vt);fe(this,"TextRenderer",Wt);fe(this,"Lexer",Pe);fe(this,"Tokenizer",kt);fe(this,"Hooks",it);this.use(...r)}walkTokens(r,e){var n,i;let t=[];for(const s of r)switch(t=t.concat(e.call(this,s)),s.type){case"table":{const o=s;for(const l of o.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of o.rows)for(const a of l)t=t.concat(this.walkTokens(a.tokens,e));break}case"list":{const o=s;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=s;(i=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&i[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const a=o[l].flat(1/0);t=t.concat(this.walkTokens(a,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){const s=e.renderers[i.name];e.renderers[i.name]=s?function(...o){let l=i.renderer.apply(this,o);return l===!1&&(l=s.apply(this,o)),l}:i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=e[i.level];s?s.unshift(i.tokenizer):e[i.level]=[i.tokenizer],i.start&&(i.level==="block"?e.startBlock?e.startBlock.push(i.start):e.startBlock=[i.start]:i.level==="inline"&&(e.startInline?e.startInline.push(i.start):e.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(e.childTokens[i.name]=i.childTokens)}),n.extensions=e),t.renderer){const i=this.defaults.renderer||new vt(this.defaults);for(const s in t.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if(s==="options")continue;const o=s,l=t.renderer[o],a=i[o];i[o]=(...u)=>{let c=l.apply(i,u);return c===!1&&(c=a.apply(i,u)),c||""}}n.renderer=i}if(t.tokenizer){const i=this.defaults.tokenizer||new kt(this.defaults);for(const s in t.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const o=s,l=t.tokenizer[o],a=i[o];i[o]=(...u)=>{let c=l.apply(i,u);return c===!1&&(c=a.apply(i,u)),c}}n.tokenizer=i}if(t.hooks){const i=this.defaults.hooks||new it;for(const s in t.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if(s==="options")continue;const o=s,l=t.hooks[o],a=i[o];it.passThroughHooks.has(s)?i[o]=u=>{if(this.defaults.async)return Promise.resolve(l.call(i,u)).then(d=>a.call(i,d));const c=l.call(i,u);return a.call(i,c)}:i[o]=(...u)=>{let c=l.apply(i,u);return c===!1&&(c=a.apply(i,u)),c}}n.hooks=i}if(t.walkTokens){const i=this.defaults.walkTokens,s=t.walkTokens;n.walkTokens=function(o){let l=[];return l.push(s.call(this,o)),i&&(l=l.concat(i.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return Pe.lex(r,e??this.defaults)}parser(r,e){return je.parse(r,e??this.defaults)}},Ye=new WeakSet,Nt=function(r,e){return(t,n)=>{const i={...n},s={...this.defaults,...i};this.defaults.async===!0&&i.async===!1&&(s.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),s.async=!0);const o=gt(this,Ye,us).call(this,!!s.silent,!!s.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(s.hooks&&(s.hooks.options=s),s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then(l=>r(l,s)).then(l=>s.hooks?s.hooks.processAllTokens(l):l).then(l=>s.walkTokens?Promise.all(this.walkTokens(l,s.walkTokens)).then(()=>l):l).then(l=>e(l,s)).then(l=>s.hooks?s.hooks.postprocess(l):l).catch(o);try{s.hooks&&(t=s.hooks.preprocess(t));let l=r(t,s);s.hooks&&(l=s.hooks.processAllTokens(l)),s.walkTokens&&this.walkTokens(l,s.walkTokens);let a=e(l,s);return s.hooks&&(a=s.hooks.postprocess(a)),a}catch(l){return o(l)}}},us=function(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+Oe(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},Bi);function he(r,e){return We.parse(r,e)}he.options=he.setOptions=function(r){return We.setOptions(r),he.defaults=We.defaults,ei(he.defaults),he},he.getDefaults=fa,he.defaults=et,he.use=function(...r){return We.use(...r),he.defaults=We.defaults,ei(he.defaults),he},he.walkTokens=function(r,e){return We.walkTokens(r,e)},he.parseInline=We.parseInline,he.Parser=je,he.parser=je.parse,he.Renderer=vt,he.TextRenderer=Wt,he.Lexer=Pe,he.lexer=Pe.lex,he.Tokenizer=kt,he.Hooks=it,he.parse=he,he.options,he.setOptions,he.use,he.walkTokens,he.parseInline,je.parse,Pe.lex;const Na=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,Ta=Object.hasOwnProperty;class Oa{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let i=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(Na,"").replace(/ /g,"-"))}(e,t===!0);const s=i;for(;Ta.call(n.occurrences,i);)n.occurrences[s]++,i=s+"-"+n.occurrences[s];return n.occurrences[i]=0,i}reset(){this.occurrences=Object.create(null)}}function Sa(r){let e,t;return e=new bt({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.tokens=n[0]),2&i&&(s.renderers=n[1]),4&i&&(s.options=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Pa(r,e,t){(function(){const u=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||u(c)},Ke(()=>{console.warn=u})})();let n,i,s,{source:o}=e,{options:l={}}=e,{renderers:a={}}=e;return r.$$set=u=>{"source"in u&&t(3,o=u.source),"options"in u&&t(4,l=u.options),"renderers"in u&&t(5,a=u.renderers)},r.$$.update=()=>{var u;56&r.$$.dirty&&(t(0,(u=o,n=new Pe().lex(u))),t(1,i={heading:ol,blockquote:ul,list:Fl,list_item:kl,br:yl,code:El,codespan:zl,table:Rl,html:Tl,paragraph:Pl,link:Vl,text:Zl,def:Gl,del:Yl,em:ta,hr:sa,strong:la,image:ca,space:Kn,escape:Kn,...a}),t(2,s={baseUrl:"/",slugger:new Oa,...l}))},[n,i,s,o,l,a]}class Ia extends ee{constructor(e){super(),te(this,e,Pa,Sa,X,{source:3,options:4,renderers:5})}}const ja=r=>({}),ui=r=>({}),Va=r=>({}),ci=r=>({}),Ha=r=>({}),di=r=>({});function Ua(r){let e,t,n,i,s,o,l,a,u,c,d,p;const F=r[13].topBarLeft,y=Ee(F,r,r[12],di),w=r[13].topBarRight,_=Ee(w,r,r[12],ci);function E(C){r[16](C)}let z={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(z.editorInstance=r[0]),o=new Es({props:z}),qe.push(()=>Ge(o,"editorInstance",E));const m=r[13].actionsBar,D=Ee(m,r,r[12],ui);return{c(){e=b("div"),t=b("div"),n=b("div"),y&&y.c(),i=I(),_&&_.c(),s=I(),L(o.$$.fragment),a=I(),u=b("div"),D&&D.c(),x(n,"class","c-codeblock__top-bar-left svelte-1jljgam"),x(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-1jljgam"),x(u,"class","c-codeblock__actions-bar-anchor svelte-1jljgam"),x(e,"class","c-codeblock svelte-1jljgam"),x(e,"role","button"),x(e,"tabindex","0")},m(C,A){g(C,e,A),q(e,t),q(t,n),y&&y.m(n,null),q(t,i),_&&_.m(t,null),q(e,s),M(o,e,null),q(e,a),q(e,u),D&&D.m(u,null),r[17](e),c=!0,d||(p=[rt(window,"focus",r[15]),rt(e,"mouseenter",r[14])],d=!0)},p(C,[A]){y&&y.p&&(!c||4096&A)&&_e(y,F,C,C[12],c?ze(F,C[12],A,Ha):Be(C[12]),di),_&&_.p&&(!c||4096&A)&&_e(_,w,C,C[12],c?ze(w,C[12],A,Va):Be(C[12]),ci);const B={};36&A&&(B.options={lineNumbers:"off",wrappingIndent:"same",padding:C[5],wordWrap:C[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&A&&(B.text=C[3].text),24&A&&(B.lang=C[4]||C[3].lang),64&A&&(B.height=C[6]),!l&&1&A&&(l=!0,B.editorInstance=C[0],Je(()=>l=!1)),o.$set(B),D&&D.p&&(!c||4096&A)&&_e(D,m,C,C[12],c?ze(m,C[12],A,ja):Be(C[12]),ui)},i(C){c||(f(y,C),f(_,C),f(o.$$.fragment,C),f(D,C),c=!0)},o(C){h(y,C),h(_,C),h(o.$$.fragment,C),h(D,C),c=!1},d(C){C&&$(e),y&&y.d(C),_&&_.d(C),R(o),D&&D.d(C),r[17](null),d=!1,Ri(p)}}}function Za(r,e,t){let n,{$$slots:i={},$$scope:s}=e,{scroll:o=!1}=e,{token:l}=e,{language:a}=e,{padding:u={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:d}=e,{height:p}=e;const F=Ot.getContext().monaco;Me(r,F,E=>t(18,n=E));const y=St(),w=()=>{if(!c)return;const E=c.getSelections();if(!(E!=null&&E.length))return;const z=c.getModel();if(E.map(m=>(z==null?void 0:z.getValueLengthInRange(m))||0).reduce((m,D)=>m+D,0)!==0)return E.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(m=>(z==null?void 0:z.getValueInRange(m))||"").join(`
`)},_=()=>{if(c)return c.getValue()||""};return r.$$set=E=>{"scroll"in E&&t(2,o=E.scroll),"token"in E&&t(3,l=E.token),"language"in E&&t(4,a=E.language),"padding"in E&&t(5,u=E.padding),"editorInstance"in E&&t(0,c=E.editorInstance),"element"in E&&t(1,d=E.element),"height"in E&&t(6,p=E.height),"$$scope"in E&&t(12,s=E.$$scope)},r.$$.update=()=>{var E;32&r.$$.dirty&&(E=u,c==null||c.updateOptions({padding:E})),65&r.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:p!==void 0?"auto":"hidden"}}))},[c,d,o,l,a,u,p,F,y,()=>c&&(w()||_())||"",w,_,s,i,function(E){ms.call(this,r,E)},()=>y.requestLayout(),function(E){c=E,t(0,c)},function(E){qe[E?"unshift":"push"](()=>{d=E,t(1,d)})}]}class pi extends ee{constructor(e){super(),te(this,e,Za,Ua,X,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const Wa=r=>({codespanContents:2&r}),fi=r=>({codespanContents:r[1]});function Qa(r){let e,t,n;const i=r[4].default,s=Ee(i,r,r[3],fi),o=s||function(l){let a;return{c(){a=T(l[1])},m(u,c){g(u,a,c)},p(u,c){2&c&&le(a,u[1])},d(u){u&&$(a)}}}(r);return{c(){e=b("span"),t=b("code"),o&&o.c(),x(t,"class","markdown-codespan svelte-1ufogiu")},m(l,a){g(l,e,a),q(e,t),o&&o.m(t,null),r[5](e),n=!0},p(l,[a]){s?s.p&&(!n||10&a)&&_e(s,i,l,l[3],n?ze(i,l[3],a,Wa):Be(l[3]),fi):o&&o.p&&(!n||2&a)&&o.p(l,n?a:-1)},i(l){n||(f(o,l),n=!0)},o(l){h(o,l),n=!1},d(l){l&&$(e),o&&o.d(l),r[5](null)}}}function Ga(r,e,t){let n,{$$slots:i={},$$scope:s}=e,{token:o}=e,{element:l}=e;return r.$$set=a=>{"token"in a&&t(2,o=a.token),"element"in a&&t(0,l=a.element),"$$scope"in a&&t(3,s=a.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,s,i,function(a){qe[a?"unshift":"push"](()=>{l=a,t(0,l)})}]}class gi extends ee{constructor(e){super(),te(this,e,Ga,Qa,X,{token:2,element:0})}}function Ja(r){let e,t,n,i,s=r[0].text+"";return{c(){e=b("span"),t=T("~"),n=T(s),i=T("~")},m(o,l){g(o,e,l),q(e,t),q(e,n),q(e,i)},p(o,[l]){1&l&&s!==(s=o[0].text+"")&&le(n,s)},i:Y,o:Y,d(o){o&&$(e)}}}function Xa(r,e,t){let{token:n}=e;return r.$$set=i=>{"token"in i&&t(0,n=i.token)},[n]}class $i extends ee{constructor(e){super(),te(this,e,Xa,Ja,X,{token:0})}}function Ya(r){let e,t;const n=r[1].default,i=Ee(n,r,r[0],null);return{c(){e=b("p"),i&&i.c(),x(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(s,o){g(s,e,o),i&&i.m(e,null),t=!0},p(s,[o]){i&&i.p&&(!t||1&o)&&_e(i,n,s,s[0],t?ze(n,s[0],o,null):Be(s[0]),null)},i(s){t||(f(i,s),t=!0)},o(s){h(i,s),t=!1},d(s){s&&$(e),i&&i.d(s)}}}function Ka(r,e,t){let{$$slots:n={},$$scope:i}=e;return r.$$set=s=>{"$$scope"in s&&t(0,i=s.$$scope)},[i,n]}class hi extends ee{constructor(e){super(),te(this,e,Ka,Ya,X,{})}}function eu(r){let e,t,n;return t=new Ia({props:{source:r[0],renderers:{codespan:gi,code:pi,paragraph:hi,del:$i,...r[1]}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","c-markdown svelte-n6ddeo")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,[s]){const o={};1&s&&(o.source=i[0]),2&s&&(o.renderers={codespan:gi,code:pi,paragraph:hi,del:$i,...i[1]}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function tu(r,e,t){let{markdown:n}=e,{renderers:i={}}=e;return r.$$set=s=>{"markdown"in s&&t(0,n=s.markdown),"renderers"in s&&t(1,i=s.renderers)},[n,i]}class nu extends ee{constructor(e){super(),te(this,e,tu,eu,X,{markdown:0,renderers:1})}}function iu(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function su(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n&&le(e,t[1])},d(t){t&&$(e)}}}function ru(r){let e,t,n;function i(l,a){return l[2]?su:iu}let s=i(r),o=s(r);return{c(){e=b("span"),t=b("code"),o.c(),x(t,"class","markdown-codespan svelte-164mxpf"),x(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ae(t,"markdown-string",r[4])},m(l,a){g(l,e,a),q(e,t),o.m(t,null),r[6](e)},p(l,[a]){s===(s=i(l))&&o?o.p(l,a):(o.d(1),o=s(l),o&&(o.c(),o.m(t,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(t,"style",n),16&a&&Ae(t,"markdown-string",l[4])},i:Y,o:Y,d(l){l&&$(e),o.d(),r[6](null)}}}function ou(r,e,t){let n,i,s,o,{token:l}=e,{element:a}=e;return r.$$set=u=>{"token"in u&&t(5,l=u.token),"element"in u&&t(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,i=n.startsWith('"')),2&r.$$.dirty&&t(2,s=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=s&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),p=parseInt(u.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),p=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[a,n,s,o,i,l,function(u){qe[u?"unshift":"push"](()=>{a=u,t(0,a)})}]}class lu extends ee{constructor(e){super(),te(this,e,ou,ru,X,{token:5,element:0})}}function au(r){let e,t;return e=new nu({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,[i]){const s={};1&i&&(s.markdown=n[1](n[0])),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function uu(r,e,t){let{markdown:n}=e;const i={codespan:lu};return r.$$set=s=>{"markdown"in s&&t(0,n=s.markdown)},[n,s=>s.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),i]}class cu extends ee{constructor(e){super(),te(this,e,uu,au,X,{markdown:0})}}const{Boolean:cs,Map:du}=Ms;function mi(r,e,t){const n=r.slice();return n[48]=e[t],n[49]=e,n[50]=t,n}function Di(r,e,t){const n=r.slice();return n[51]=e[t],n[52]=e,n[53]=t,n}function Fi(r,e,t){const n=r.slice();return n[54]=e[t],n[55]=e,n[56]=t,n}function xi(r){let e,t,n,i,s,o,l,a;t=new Zi({}),o=new Ve({props:{variant:"ghost",size:1,$$slots:{default:[pu]},$$scope:{ctx:r}}}),o.$on("click",r[29]);let u=r[4]&&Ci(r);return{c(){e=b("div"),L(t.$$.fragment),n=I(),i=T(r[18]),s=I(),L(o.$$.fragment),l=I(),u&&u.c(),x(e,"class","c-diff-view__error svelte-ibi4q5")},m(c,d){g(c,e,d),M(t,e,null),q(e,n),q(e,i),q(e,s),M(o,e,null),q(e,l),u&&u.m(e,null),a=!0},p(c,d){(!a||262144&d[0])&&le(i,c[18]);const p={};67108864&d[1]&&(p.$$scope={dirty:d,ctx:c}),o.$set(p),c[4]?u?(u.p(c,d),16&d[0]&&f(u,1)):(u=Ci(c),u.c(),f(u,1),u.m(e,null)):u&&(H(),h(u,1,1,()=>{u=null}),U())},i(c){a||(f(t.$$.fragment,c),f(o.$$.fragment,c),f(u),a=!0)},o(c){h(t.$$.fragment,c),h(o.$$.fragment,c),h(u),a=!1},d(c){c&&$(e),R(t),R(o),u&&u.d()}}}function pu(r){let e;return{c(){e=T("Retry")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Ci(r){let e,t;return e=new Ve({props:{variant:"ghost",size:1,$$slots:{default:[fu]},$$scope:{ctx:r}}}),e.$on("click",function(){Ds(r[4])&&r[4].apply(this,arguments)}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){r=n;const s={};67108864&i[1]&&(s.$$scope={dirty:i,ctx:r}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function fu(r){let e;return{c(){e=T("Render as list")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function gu(r){let e,t,n,i,s,o,l,a,u,c,d,p,F,y=r[1]&&r[2]!==r[1]&&ki(r),w=r[2]&&wi(r);o=new ge({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[xu]},$$scope:{ctx:r}}}),a=new es({props:{changedFiles:r[0]}});const _=[ku,Cu],E=[];function z(m,D){return m[16]&&m[15].length===0?0:m[6]&&m[6].length>0?1:-1}return~(d=z(r))&&(p=E[d]=_[d](r)),{c(){e=b("div"),t=b("div"),n=b("div"),y&&y.c(),i=I(),w&&w.c(),s=I(),L(o.$$.fragment),l=I(),L(a.$$.fragment),u=I(),c=b("div"),p&&p.c(),x(n,"class","c-diff-view__tree__header svelte-ibi4q5"),x(t,"class","c-diff-view__tree svelte-ibi4q5"),x(c,"class","c-diff-view__explanation svelte-ibi4q5"),x(e,"class","c-diff-view__layout svelte-ibi4q5")},m(m,D){g(m,e,D),q(e,t),q(t,n),y&&y.m(n,null),q(n,i),w&&w.m(n,null),q(n,s),M(o,n,null),q(n,l),M(a,n,null),q(e,u),q(e,c),~d&&E[d].m(c,null),F=!0},p(m,D){m[1]&&m[2]!==m[1]?y?(y.p(m,D),6&D[0]&&f(y,1)):(y=ki(m),y.c(),f(y,1),y.m(n,i)):y&&(H(),h(y,1,1,()=>{y=null}),U()),m[2]?w?(w.p(m,D),4&D[0]&&f(w,1)):(w=wi(m),w.c(),f(w,1),w.m(n,s)):w&&(H(),h(w,1,1,()=>{w=null}),U());const C={};67108864&D[1]&&(C.$$scope={dirty:D,ctx:m}),o.$set(C);const A={};1&D[0]&&(A.changedFiles=m[0]),a.$set(A);let B=d;d=z(m),d===B?~d&&E[d].p(m,D):(p&&(H(),h(E[B],1,1,()=>{E[B]=null}),U()),~d?(p=E[d],p?p.p(m,D):(p=E[d]=_[d](m),p.c()),f(p,1),p.m(c,null)):p=null)},i(m){F||(f(y),f(w),f(o.$$.fragment,m),f(a.$$.fragment,m),f(p),F=!0)},o(m){h(y),h(w),h(o.$$.fragment,m),h(a.$$.fragment,m),h(p),F=!1},d(m){m&&$(e),y&&y.d(),w&&w.d(),R(o),R(a),~d&&E[d].d()}}}function $u(r){let e,t,n;return t=new ge({props:{size:2,color:"secondary",$$slots:{default:[Zu]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","c-diff-view__empty svelte-ibi4q5")},m(i,s){g(i,e,s),M(t,e,null),n=!0},p(i,s){const o={};67108864&s[1]&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function ki(r){let e,t,n,i;return e=new ge({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[hu]},$$scope:{ctx:r}}}),n=new ge({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[mu]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=I(),L(n.$$.fragment)},m(s,o){M(e,s,o),g(s,t,o),M(n,s,o),i=!0},p(s,o){const l={};67108864&o[1]&&(l.$$scope={dirty:o,ctx:s}),e.$set(l);const a={};2&o[0]|67108864&o[1]&&(a.$$scope={dirty:o,ctx:s}),n.$set(a)},i(s){i||(f(e.$$.fragment,s),f(n.$$.fragment,s),i=!0)},o(s){h(e.$$.fragment,s),h(n.$$.fragment,s),i=!1},d(s){s&&$(t),R(e,s),R(n,s)}}}function hu(r){let e;return{c(){e=T("Changes from agent")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function mu(r){let e;return{c(){e=T(r[1])},m(t,n){g(t,e,n)},p(t,n){2&n[0]&&le(e,t[1])},d(t){t&&$(e)}}}function wi(r){let e,t,n,i;return e=new ge({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Du]},$$scope:{ctx:r}}}),n=new ge({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[Fu]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment),t=I(),L(n.$$.fragment)},m(s,o){M(e,s,o),g(s,t,o),M(n,s,o),i=!0},p(s,o){const l={};67108864&o[1]&&(l.$$scope={dirty:o,ctx:s}),e.$set(l);const a={};4&o[0]|67108864&o[1]&&(a.$$scope={dirty:o,ctx:s}),n.$set(a)},i(s){i||(f(e.$$.fragment,s),f(n.$$.fragment,s),i=!0)},o(s){h(e.$$.fragment,s),h(n.$$.fragment,s),i=!1},d(s){s&&$(t),R(e,s),R(n,s)}}}function Du(r){let e;return{c(){e=T("Last user prompt")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Fu(r){let e;return{c(){e=T(r[2])},m(t,n){g(t,e,n)},p(t,n){4&n[0]&&le(e,t[2])},d(t){t&&$(e)}}}function xu(r){let e;return{c(){e=T("Changed files")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Cu(r){let e,t,n=pe(r[6]),i=[];for(let o=0;o<n.length;o+=1)i[o]=bi(mi(r,n,o));const s=o=>h(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=xe()},m(o,l){for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(o,l);g(o,e,l),t=!0},p(o,l){if(515532776&l[0]){let a;for(n=pe(o[6]),a=0;a<n.length;a+=1){const u=mi(o,n,a);i[a]?(i[a].p(u,l),f(i[a],1)):(i[a]=bi(u),i[a].c(),f(i[a],1),i[a].m(e.parentNode,e))}for(H(),a=n.length;a<i.length;a+=1)s(a);U()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)f(i[l]);t=!0}},o(o){i=i.filter(cs);for(let l=0;l<i.length;l+=1)h(i[l]);t=!1},d(o){o&&$(e),Re(i,o)}}}function ku(r){let e,t,n,i,s;return t=new He({props:{content:r[9]?"Applying changes...":r[10]?"All changes applied":r[11]?"Apply all changes":"No changes to apply",$$slots:{default:[Uu]},$$scope:{ctx:r}}}),i=new il({props:{count:2}}),{c(){e=b("div"),L(t.$$.fragment),n=I(),L(i.$$.fragment),x(e,"class","c-diff-view__controls svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),g(o,n,l),M(i,o,l),s=!0},p(o,l){const a={};3584&l[0]&&(a.content=o[9]?"Applying changes...":o[10]?"All changes applied":o[11]?"Apply all changes":"No changes to apply"),7680&l[0]|67108864&l[1]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)},i(o){s||(f(t.$$.fragment,o),f(i.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),h(i.$$.fragment,o),s=!1},d(o){o&&($(e),$(n)),R(t),R(i,o)}}}function wu(r){let e,t=r[48].title+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){64&i[0]&&t!==(t=n[48].title+"")&&le(e,t)},d(n){n&&$(e)}}}function vu(r){let e;return{c(){e=b("div"),x(e,"class","c-diff-view__skeleton-title svelte-ibi4q5")},m(t,n){g(t,e,n)},p:Y,d(t){t&&$(e)}}}function yu(r){let e,t;return e=new cu({props:{markdown:r[48].description}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};64&i[0]&&(s.markdown=n[48].description),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Au(r){let e,t,n;return{c(){e=b("div"),t=I(),n=b("div"),x(e,"class","c-diff-view__skeleton-text svelte-ibi4q5"),x(n,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(i,s){g(i,e,s),g(i,t,s),g(i,n,s)},p:Y,i:Y,o:Y,d(i){i&&($(e),$(t),$(n))}}}function bu(r){let e,t,n;return e=new Rs({}),{c(){L(e.$$.fragment),t=T(`
                        Expand All`)},m(i,s){M(e,i,s),g(i,t,s),n=!0},i(i){n||(f(e.$$.fragment,i),n=!0)},o(i){h(e.$$.fragment,i),n=!1},d(i){i&&$(t),R(e,i)}}}function Eu(r){let e,t,n;return e=new bs({}),{c(){L(e.$$.fragment),t=T(`
                        Collapse All`)},m(i,s){M(e,i,s),g(i,t,s),n=!0},i(i){n||(f(e.$$.fragment,i),n=!0)},o(i){h(e.$$.fragment,i),n=!1},d(i){i&&$(t),R(e,i)}}}function _u(r){let e,t,n,i;const s=[Eu,bu],o=[];function l(a,u){return a[23]?1:0}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),h(o[c],1,1,()=>{o[c]=null}),U(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function Bu(r){let e,t,n,i;return n=new ut({}),{c(){e=T(`Apply all
                          `),t=b("div"),L(n.$$.fragment),x(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(s,o){g(s,e,o),g(s,t,o),M(n,t,null),i=!0},i(s){i||(f(n.$$.fragment,s),i=!0)},o(s){h(n.$$.fragment,s),i=!1},d(s){s&&($(e),$(t)),R(n)}}}function zu(r){let e,t,n;return t=new ge({props:{size:2,$$slots:{default:[Mu]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","c-diff-view__applied svelte-ibi4q5")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Lu(r){let e,t,n,i,s;return t=new Tt({props:{size:1,useCurrentColor:!0}}),i=new ge({props:{size:2,$$slots:{default:[Ru]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),n=I(),L(i.$$.fragment),x(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),q(e,n),M(i,e,null),s=!0},i(o){s||(f(t.$$.fragment,o),f(i.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),h(i.$$.fragment,o),s=!1},d(o){o&&$(e),R(t),R(i)}}}function Mu(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=T(`Applied
                              `),L(t.$$.fragment)},m(i,s){g(i,e,s),M(t,i,s),n=!0},p:Y,i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t,i)}}}function Ru(r){let e;return{c(){e=T("Applying...")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function qu(r){let e,t,n,i;const s=[Lu,zu,Bu],o=[];function l(a,u){return a[9]?0:a[10]?1:2}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),h(o[c],1,1,()=>{o[c]=null}),U(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function Nu(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[13],$$slots:{default:[qu]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};8192&i[0]&&(s.disabled=n[13]),1536&i[0]|67108864&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Tu(r){let e,t=r[51].title+"";return{c(){e=T(t)},m(n,i){g(n,e,i)},p(n,i){64&i[0]&&t!==(t=n[51].title+"")&&le(e,t)},d(n){n&&$(e)}}}function Ou(r){let e;return{c(){e=b("div"),x(e,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(t,n){g(t,e,n)},p:Y,d(t){t&&$(e)}}}function vi(r){let e,t,n,i,s,o=r[51].warning+"";return t=new Zi({}),{c(){e=b("div"),L(t.$$.fragment),n=I(),i=T(o),x(e,"class","c-diff-view__warning svelte-ibi4q5")},m(l,a){g(l,e,a),M(t,e,null),q(e,n),q(e,i),s=!0},p(l,a){(!s||64&a[0])&&o!==(o=l[51].warning+"")&&le(i,o)},i(l){s||(f(t.$$.fragment,l),s=!0)},o(l){h(t.$$.fragment,l),s=!1},d(l){l&&$(e),R(t)}}}function yi(r,e){let t,n,i,s,o,l=e[50],a=e[53],u=e[54];function c(...z){return e[37](e[54],...z)}function d(){return e[38](e[54])}function p(){return e[39](e[54])}function F(z){e[40](z,e[54])}const y=()=>e[41](n,l,a,u),w=()=>e[41](null,l,a,u);function _(z){e[42](z)}let E={path:e[54].path,change:e[54],descriptions:e[51].descriptions,isExpandedDefault:e[8][e[54].path]!==void 0?!e[8][e[54].path]:e[7],isApplying:e[14][e[54].path]==="pending",hasApplied:e[14][e[54].path]==="applied",onCodeChange:c,onApplyChanges:d,onOpenFile:e[3]?p:void 0,isAgentFromDifferentRepo:e[5]};return e[8][e[54].path]!==void 0&&(E.isCollapsed=e[8][e[54].path]),e[20]!==void 0&&(E.areDescriptionsVisible=e[20]),n=new Jo({props:E}),qe.push(()=>Ge(n,"isCollapsed",F)),y(),qe.push(()=>Ge(n,"areDescriptionsVisible",_)),{key:r,first:null,c(){t=b("div"),L(n.$$.fragment),x(t,"class","c-diff-view__changes-item svelte-ibi4q5"),this.first=t},m(z,m){g(z,t,m),M(n,t,null),o=!0},p(z,m){l===(e=z)[50]&&a===e[53]&&u===e[54]||(w(),l=e[50],a=e[53],u=e[54],y());const D={};64&m[0]&&(D.path=e[54].path),64&m[0]&&(D.change=e[54]),64&m[0]&&(D.descriptions=e[51].descriptions),448&m[0]&&(D.isExpandedDefault=e[8][e[54].path]!==void 0?!e[8][e[54].path]:e[7]),16448&m[0]&&(D.isApplying=e[14][e[54].path]==="pending"),16448&m[0]&&(D.hasApplied=e[14][e[54].path]==="applied"),64&m[0]&&(D.onCodeChange=c),64&m[0]&&(D.onApplyChanges=d),72&m[0]&&(D.onOpenFile=e[3]?p:void 0),32&m[0]&&(D.isAgentFromDifferentRepo=e[5]),!i&&320&m[0]&&(i=!0,D.isCollapsed=e[8][e[54].path],Je(()=>i=!1)),!s&&1048576&m[0]&&(s=!0,D.areDescriptionsVisible=e[20],Je(()=>s=!1)),n.$set(D)},i(z){o||(f(n.$$.fragment,z),o=!0)},o(z){h(n.$$.fragment,z),o=!1},d(z){z&&$(t),w(),R(n)}}}function Ai(r){let e,t,n,i,s,o,l,a,u,c,d,p=[],F=new du;function y(D,C){return D[17]&&D[51].descriptions.length===0?Ou:Tu}s=new so({props:{type:r[51].type}});let w=y(r),_=w(r),E=!r[17]&&r[51].warning&&vi(r),z=pe(r[51].changes);const m=D=>D[54].id;for(let D=0;D<z.length;D+=1){let C=Fi(r,z,D),A=m(C);F.set(A,p[D]=yi(A,C))}return{c(){e=b("div"),t=b("div"),n=b("div"),i=b("div"),L(s.$$.fragment),o=I(),l=b("h5"),_.c(),a=I(),E&&E.c(),u=I(),c=b("div");for(let D=0;D<p.length;D+=1)p[D].c();x(i,"class","c-diff-view__icon svelte-ibi4q5"),x(l,"class","c-diff-view__title svelte-ibi4q5"),x(n,"class","c-diff-view__content svelte-ibi4q5"),x(t,"class","c-diff-view__header svelte-ibi4q5"),x(c,"class","c-diff-view__changes svelte-ibi4q5"),x(e,"class","c-diff-view__subsection svelte-ibi4q5"),x(e,"id",`subsection-${r[50]}-${r[53]}`)},m(D,C){g(D,e,C),q(e,t),q(t,n),q(n,i),M(s,i,null),q(n,o),q(n,l),_.m(l,null),q(n,a),E&&E.m(n,null),q(e,u),q(e,c);for(let A=0;A<p.length;A+=1)p[A]&&p[A].m(c,null);d=!0},p(D,C){const A={};64&C[0]&&(A.type=D[51].type),s.$set(A),w===(w=y(D))&&_?_.p(D,C):(_.d(1),_=w(D),_&&(_.c(),_.m(l,null))),!D[17]&&D[51].warning?E?(E.p(D,C),131136&C[0]&&f(E,1)):(E=vi(D),E.c(),f(E,1),E.m(n,null)):E&&(H(),h(E,1,1,()=>{E=null}),U()),202916328&C[0]&&(z=pe(D[51].changes),H(),p=Oi(p,C,m,1,D,z,F,c,Si,yi,null,Fi),U())},i(D){if(!d){f(s.$$.fragment,D),f(E);for(let C=0;C<z.length;C+=1)f(p[C]);d=!0}},o(D){h(s.$$.fragment,D),h(E);for(let C=0;C<p.length;C+=1)h(p[C]);d=!1},d(D){D&&$(e),R(s),_.d(),E&&E.d();for(let C=0;C<p.length;C+=1)p[C].d()}}}function bi(r){let e,t,n,i,s,o,l,a,u,c,d,p;function F(B,O){return B[17]&&B[48].title==="Loading..."?vu:wu}let y=F(r),w=y(r);const _=[Au,yu],E=[];function z(B,O){return B[17]&&B[48].description===""?0:1}l=z(r),a=E[l]=_[l](r);let m=r[50]===0&&function(B){let O,V,W,Q,re;return V=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[_u]},$$scope:{ctx:B}}}),V.$on("click",B[25]),Q=new He({props:{content:B[21],$$slots:{default:[Nu]},$$scope:{ctx:B}}}),{c(){O=b("div"),L(V.$$.fragment),W=I(),L(Q.$$.fragment),x(O,"class","c-diff-view__controls svelte-ibi4q5")},m(ae,ce){g(ae,O,ce),M(V,O,null),q(O,W),M(Q,O,null),re=!0},p(ae,ce){const de={};8388608&ce[0]|67108864&ce[1]&&(de.$$scope={dirty:ce,ctx:ae}),V.$set(de);const G={};2097152&ce[0]&&(G.content=ae[21]),9728&ce[0]|67108864&ce[1]&&(G.$$scope={dirty:ce,ctx:ae}),Q.$set(G)},i(ae){re||(f(V.$$.fragment,ae),f(Q.$$.fragment,ae),re=!0)},o(ae){h(V.$$.fragment,ae),h(Q.$$.fragment,ae),re=!1},d(ae){ae&&$(O),R(V),R(Q)}}}(r),D=pe(r[48].sections||[]),C=[];for(let B=0;B<D.length;B+=1)C[B]=Ai(Di(r,D,B));const A=B=>h(C[B],1,1,()=>{C[B]=null});return{c(){e=b("div"),t=b("div"),n=b("div"),i=b("h5"),w.c(),s=I(),o=b("div"),a.c(),u=I(),m&&m.c(),c=I();for(let B=0;B<C.length;B+=1)C[B].c();d=I(),x(i,"class","c-diff-view__title svelte-ibi4q5"),x(o,"class","c-diff-view__description svelte-ibi4q5"),x(n,"class","c-diff-view__content svelte-ibi4q5"),x(t,"class","c-diff-view__header svelte-ibi4q5"),x(e,"class","c-diff-view__section svelte-ibi4q5"),x(e,"id",`section-${r[50]}`)},m(B,O){g(B,e,O),q(e,t),q(t,n),q(n,i),w.m(i,null),q(n,s),q(n,o),E[l].m(o,null),q(t,u),m&&m.m(t,null),q(e,c);for(let V=0;V<C.length;V+=1)C[V]&&C[V].m(e,null);q(e,d),p=!0},p(B,O){y===(y=F(B))&&w?w.p(B,O):(w.d(1),w=y(B),w&&(w.c(),w.m(i,null)));let V=l;if(l=z(B),l===V?E[l].p(B,O):(H(),h(E[V],1,1,()=>{E[V]=null}),U(),a=E[l],a?a.p(B,O):(a=E[l]=_[l](B),a.c()),f(a,1),a.m(o,null)),B[50]===0&&m.p(B,O),203047400&O[0]){let W;for(D=pe(B[48].sections||[]),W=0;W<D.length;W+=1){const Q=Di(B,D,W);C[W]?(C[W].p(Q,O),f(C[W],1)):(C[W]=Ai(Q),C[W].c(),f(C[W],1),C[W].m(e,d))}for(H(),W=D.length;W<C.length;W+=1)A(W);U()}},i(B){if(!p){f(a),f(m);for(let O=0;O<D.length;O+=1)f(C[O]);p=!0}},o(B){h(a),h(m),C=C.filter(cs);for(let O=0;O<C.length;O+=1)h(C[O]);p=!1},d(B){B&&$(e),w.d(),E[l].d(),m&&m.d(),Re(C,B)}}}function Su(r){let e,t,n,i;return n=new ut({}),{c(){e=T(`Apply all
                  `),t=b("div"),L(n.$$.fragment),x(t,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(s,o){g(s,e,o),g(s,t,o),M(n,t,null),i=!0},i(s){i||(f(n.$$.fragment,s),i=!0)},o(s){h(n.$$.fragment,s),i=!1},d(s){s&&($(e),$(t)),R(n)}}}function Pu(r){let e,t,n;return t=new ge({props:{size:2,$$slots:{default:[ju]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),x(e,"class","c-diff-view__applied svelte-ibi4q5")},m(i,s){g(i,e,s),M(t,e,null),n=!0},i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t)}}}function Iu(r){let e,t,n,i,s;return t=new Tt({props:{size:1,useCurrentColor:!0}}),i=new ge({props:{size:2,$$slots:{default:[Vu]},$$scope:{ctx:r}}}),{c(){e=b("div"),L(t.$$.fragment),n=I(),L(i.$$.fragment),x(e,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){g(o,e,l),M(t,e,null),q(e,n),M(i,e,null),s=!0},i(o){s||(f(t.$$.fragment,o),f(i.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),h(i.$$.fragment,o),s=!1},d(o){o&&$(e),R(t),R(i)}}}function ju(r){let e,t,n;return t=new At({props:{iconName:"check"}}),{c(){e=T(`Applied
                      `),L(t.$$.fragment)},m(i,s){g(i,e,s),M(t,i,s),n=!0},p:Y,i(i){n||(f(t.$$.fragment,i),n=!0)},o(i){h(t.$$.fragment,i),n=!1},d(i){i&&$(e),R(t,i)}}}function Vu(r){let e;return{c(){e=T("Applying...")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Hu(r){let e,t,n,i;const s=[Iu,Pu,Su],o=[];function l(a,u){return a[9]?0:a[10]?1:2}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e!==c&&(H(),h(o[c],1,1,()=>{o[c]=null}),U(),t=o[e],t||(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function Uu(r){let e,t;return e=new Ve({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[9]||r[10]||r[12].length>0||!r[11],$$slots:{default:[Hu]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};7680&i[0]&&(s.disabled=n[9]||n[10]||n[12].length>0||!n[11]),1536&i[0]|67108864&i[1]&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function Zu(r){let e;return{c(){e=T("No files changed")},m(t,n){g(t,e,n)},d(t){t&&$(e)}}}function Wu(r){let e,t,n,i,s,o=r[18]&&xi(r);const l=[$u,gu],a=[];function u(c,d){return c[22]?0:1}return n=u(r),i=a[n]=l[n](r),{c(){e=b("div"),o&&o.c(),t=I(),i.c(),x(e,"class","c-diff-view svelte-ibi4q5")},m(c,d){g(c,e,d),o&&o.m(e,null),q(e,t),a[n].m(e,null),s=!0},p(c,d){c[18]?o?(o.p(c,d),262144&d[0]&&f(o,1)):(o=xi(c),o.c(),f(o,1),o.m(e,t)):o&&(H(),h(o,1,1,()=>{o=null}),U());let p=n;n=u(c),n===p?a[n].p(c,d):(H(),h(a[p],1,1,()=>{a[p]=null}),U(),i=a[n],i?i.p(c,d):(i=a[n]=l[n](c),i.c()),f(i,1),i.m(e,null))},i(c){s||(f(o),f(i),s=!0)},o(c){h(o),h(i),s=!1},d(c){c&&$(e),o&&o.d(),a[n].d()}}}function Qu(r,e,t){let n,i,s,o,l,a,u,c,{changedFiles:d}=e,{agentLabel:p}=e,{latestUserPrompt:F}=e,{onApplyChanges:y}=e,{onOpenFile:w}=e,{onRenderBackup:_}=e,{preloadedExplanation:E}=e,{isAgentFromDifferentRepo:z=!1}=e;const m=st(ot.key);let D="",C=!1,A=[],B=[],O=!1,V=!1,W=null,Q=!0,re={},ae=[],ce=!1,de=!1,G=!0;const Fe=Ue({});Me(r,Fe,v=>t(14,c=v));let $e={};function ie(v,J){t(33,$e[v]=J,$e)}async function we(v,J,oe){if(y)return Fe.update(k=>(k[v]="pending",k)),new Promise(k=>{y==null||y(v,J,oe).then(()=>{Fe.update(N=>(N[v]="applied",N)),k()})})}function ve(v){const J={title:"Changed Files",description:`${v.length} files were changed`,sections:[]},oe=[],k=[],N=[];return v.forEach(S=>{S.old_path?S.new_path?k.push(S):N.push(S):oe.push(S)}),oe.length>0&&J.sections.push(Ce("Added files","feature",oe)),k.length>0&&J.sections.push(Ce("Modified files","fix",k)),N.length>0&&J.sections.push(Ce("Deleted files","chore",N)),[J]}function Ce(v,J,oe){const k=[];return oe.forEach(N=>{const S=N.new_path||N.old_path,j=N.old_contents||"",P=N.new_contents||"",Z=N.old_path?N.old_path:"",K=xt(Z,N.new_path||"/dev/null",j,P,"","",{context:3}),ne=`${Ie(S)}-${Ie(j+P)}`;k.push({id:ne,path:S,diff:K,originalCode:j,modifiedCode:P})}),{title:v,descriptions:[],type:J,changes:k}}async function ue(){if(!C)return;if(t(16,O=!0),t(17,V=!1),t(18,W=null),t(15,B=[]),t(6,A=[]),l)return void t(16,O=!1);const v=102400;let J=0;if(d.forEach(oe=>{var k,N;J+=(((k=oe.old_contents)==null?void 0:k.length)||0)+(((N=oe.new_contents)==null?void 0:N.length)||0)}),d.length>12||J>512e3){try{t(6,A=ve(d))}catch(oe){console.error("Failed to create simple explanation:",oe),t(18,W="Failed to create explanation for large changes.")}t(16,O=!1)}else try{const oe=new Bs(S=>Pi.postMessage(S)),k=new Map,N=d.map(S=>{const j=S.new_path||S.old_path,P=S.old_contents||"",Z=S.new_contents||"",K=`${Ie(j)}-${Ie(P+Z)}`;return k.set(K,{old_path:S.old_path,new_path:S.new_path,old_contents:P,new_contents:Z,change_type:S.change_type}),{id:K,old_path:S.old_path,new_path:S.new_path,change_type:S.change_type}});try{const S=N.length===1;let j=[];S?j=N.map(P=>({path:P.new_path||P.old_path,changes:[{id:P.id,path:P.new_path||P.old_path,diff:`File: ${P.new_path||P.old_path}
Change type: ${P.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):j=(await oe.send({type:"get-diff-group-changes-request",data:{changedFiles:N,changesById:!0,apikey:D}},3e4)).data.groupedChanges,t(15,B=j.map(P=>({path:P.path,changes:P.changes.map(Z=>{if(Z.id&&k.has(Z.id)){const K=k.get(Z.id);let ne=Z.diff;return ne&&!ne.startsWith("File:")||(ne=xt(K.old_path||"",K.new_path||"",K.old_contents||"",K.new_contents||"")),{...Z,diff:ne,old_path:K.old_path,new_path:K.new_path,old_contents:K.old_contents,new_contents:K.new_contents,change_type:K.change_type,originalCode:K.old_contents||"",modifiedCode:K.new_contents||""}}return Z})})))}catch(S){console.error("Failed to group changes with LLM, falling back to simple grouping:",S);try{const j=N.map(P=>{if(P.id&&k.has(P.id)){const Z=k.get(P.id);return{...P,old_path:Z.old_path,new_path:Z.new_path,old_contents:Z.old_contents||"",new_contents:Z.new_contents||"",change_type:Z.change_type}}return P});t(6,A=ve(j)),t(15,B=A[0].sections.map(P=>({path:P.title,changes:P.changes}))),t(17,V=!1)}catch(j){console.error("Failed to create simple explanation:",j),t(18,W="Failed to group changes. Please try again.")}}if(t(16,O=!1),!B||B.length===0)throw new Error("Failed to group changes");if(!A||A.length===0){t(6,A=function(j){const P={title:"Loading...",description:"",sections:[]};return j.forEach(Z=>{const K=Z.changes.map(De=>{if(De.id)return De;const Le=Ie(De.path),Se=Ie(De.originalCode+De.modifiedCode);return{...De,id:`${Le}-${Se}`}}),ne={title:Z.path,descriptions:[],type:"other",changes:K};P.sections.push(ne)}),[P]}(B));const S=A[0].sections.map(j=>({path:j.title,changes:j.changes.map(P=>{var De,Le,Se;const Z=((De=P.originalCode)==null?void 0:De.length)||0,K=((Le=P.modifiedCode)==null?void 0:Le.length)||0,ne=((Se=P.diff)==null?void 0:Se.length)||0;return Z>v||K>v||ne>v?{id:P.id,path:P.path,diff:`File: ${P.path}
Content too large to include in explanation request (${Math.max(Z,K,ne)} bytes)`,originalCode:Z>v?`[File content too large: ${Z} bytes]`:P.originalCode,modifiedCode:K>v?`[File content too large: ${K} bytes]`:P.modifiedCode}:{id:P.id,path:P.path,diff:P.diff,originalCode:P.originalCode,modifiedCode:P.modifiedCode}})}));t(17,V=!0);try{const{explanation:j,error:P}=await m.getDescriptions(S,D);if(P==="Token limit exceeded")return t(6,A=ve(d)),t(16,O=!1),void t(17,V=!1);j&&j.length>0&&j.forEach((Z,K)=>{Z.sections&&Z.sections.forEach((ne,De)=>{ne.changes&&ne.changes.forEach(Le=>{const Se=A[K];if(Se&&Se.sections){const Te=Se.sections[De];if(Te&&Te.changes){const ft=Te.changes.find(ds=>ds.id===Le.id);ft&&(Le.originalCode=ft.originalCode,Le.modifiedCode=ft.modifiedCode,Le.diff=ft.diff)}}})})}),t(6,A=j)}catch(j){console.error("Failed to get descriptions, using skeleton explanation:",j)}}A.length===0&&t(18,W="Failed to generate explanation.")}catch(oe){console.error("Failed to get explanation:",oe),t(18,W=oe instanceof Error?oe.message:"An error occurred while generating the explanation.")}finally{t(16,O=!1),t(17,V=!1)}}Ke(()=>{const v=localStorage.getItem("anthropic_apikey");v&&(D=v),t(32,C=!0)});let ke="",ye="Apply all changes locally";return r.$$set=v=>{"changedFiles"in v&&t(0,d=v.changedFiles),"agentLabel"in v&&t(1,p=v.agentLabel),"latestUserPrompt"in v&&t(2,F=v.latestUserPrompt),"onApplyChanges"in v&&t(30,y=v.onApplyChanges),"onOpenFile"in v&&t(3,w=v.onOpenFile),"onRenderBackup"in v&&t(4,_=v.onRenderBackup),"preloadedExplanation"in v&&t(31,E=v.preloadedExplanation),"isAgentFromDifferentRepo"in v&&t(5,z=v.isAgentFromDifferentRepo)},r.$$.update=()=>{if(16385&r.$$.dirty[0]&&d&&Fe.set(d.reduce((v,J)=>{const oe=J.new_path||J.old_path;return v[oe]=c[oe]??"none",v},{})),1&r.$$.dirty[0]&&t(36,a=JSON.stringify(d)),43&r.$$.dirty[1]&&C&&a&&a!==ke&&(t(34,ke=a),E&&E.length>0?(t(6,A=E),t(16,O=!1),t(17,V=!1)):ue(),t(9,ce=!1),t(10,de=!1),t(33,$e={})),448&r.$$.dirty[0]&&A&&A.length>0){const v=$t(A);Array.from(v).forEach(k=>{re[k]===void 0&&t(8,re[k]=!Q,re)});const J=Object.keys(re).filter(k=>re[k]),oe=Array.from(v);oe.length>0&&t(7,Q=!oe.some(k=>J.includes(k)))}if(256&r.$$.dirty[0]&&t(23,n=Object.values(re).some(Boolean)),64&r.$$.dirty[0]|4&r.$$.dirty[1]&&A&&A.length>0&&A.flatMap(v=>v.sections||[]).flatMap(v=>v.changes).forEach(v=>{$e[v.path]||t(33,$e[v.path]=v.modifiedCode,$e)}),64&r.$$.dirty[0]&&t(35,i=JSON.stringify(A)),16448&r.$$.dirty[0]|16&r.$$.dirty[1]&&t(11,s=(()=>{if(i&&c){const v=$t(A);return v.size!==0&&Array.from(v).some(J=>c[J]!=="applied")}return!1})()),16384&r.$$.dirty[0]&&t(10,de=Object.keys(c).every(v=>c[v]==="applied")),16384&r.$$.dirty[0]&&t(12,o=Object.keys(c).filter(v=>c[v]==="pending")),1&r.$$.dirty[0]&&t(22,l=d.length===0),17472&r.$$.dirty[0]|16&r.$$.dirty[1]&&i&&de){const v=$t(A);Array.from(v).every(J=>c[J]==="applied")||t(10,de=!1)}7712&r.$$.dirty[0]&&t(13,u=z||ce||de||o.length>0||!s),15904&r.$$.dirty[0]&&(u?z?t(21,ye="Cannot apply changes from a different repository locally"):ce?t(21,ye="Applying changes..."):de?t(21,ye="All changes applied"):o.length>0?t(21,ye="Waiting for changes to apply"):s||t(21,ye="No changes to apply"):t(21,ye="Apply all changes locally"))},[d,p,F,w,_,z,A,Q,re,ce,de,s,o,u,c,B,O,V,W,ae,G,ye,l,n,Fe,function(){const v=$t(A),J=Object.values(re).some(Boolean);t(7,Q=J),Array.from(v).forEach(oe=>{t(8,re[oe]=!Q,re)})},ie,we,function(){if(!y)return;t(9,ce=!0),t(10,de=!1);const{filesToApply:v,areAllPathsApplied:J}=Fs(A,d,$e);J||v.length===0?t(10,de=J):xs(v,we).then(()=>{t(9,ce=!1),t(10,de=!0)})},ue,y,E,C,$e,ke,i,a,(v,J)=>{ie(v.path,J)},v=>{we(v.path,v.originalCode,v.modifiedCode)},v=>w(v.path),function(v,J){r.$$.not_equal(re[J.path],v)&&(re[J.path]=v,t(8,re),t(6,A),t(7,Q),t(32,C),t(36,a),t(34,ke),t(31,E),t(0,d))},function(v,J,oe,k){qe[v?"unshift":"push"](()=>{ae[100*J+10*oe+k.path.length%10]=v,t(19,ae)})},function(v){G=v,t(20,G)}]}class Gu extends ee{constructor(e){super(),te(this,e,Qu,Wu,X,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:30,onOpenFile:3,onRenderBackup:4,preloadedExplanation:31,isAgentFromDifferentRepo:5},null,[-1,-1])}}function Ei(r){let e,t,n=r[7].opts,i=_i(r);return{c(){e=b("div"),i.c(),x(e,"class","file-explorer-contents svelte-5tfpo4")},m(s,o){g(s,e,o),i.m(e,null),t=!0},p(s,o){128&o&&X(n,n=s[7].opts)?(H(),h(i,1,1,Y),U(),i=_i(s),i.c(),f(i,1),i.m(e,null)):i.p(s,o)},i(s){t||(f(i),t=!0)},o(s){h(i),t=!1},d(s){s&&$(e),i.d(s)}}}function Ju(r){var n,i;let e,t;return e=new Gu({props:{changedFiles:r[0],onApplyChanges:r[9],onOpenFile:r[10],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[11],preloadedExplanation:(i=(n=r[7])==null?void 0:n.opts)==null?void 0:i.preloadedExplanation,isAgentFromDifferentRepo:r[5]}}),{c(){L(e.$$.fragment)},m(s,o){M(e,s,o),t=!0},p(s,o){var a,u;const l={};1&o&&(l.changedFiles=s[0]),8&o&&(l.agentLabel=s[3]),16&o&&(l.latestUserPrompt=s[4]),64&o&&(l.onRenderBackup=s[11]),128&o&&(l.preloadedExplanation=(u=(a=s[7])==null?void 0:a.opts)==null?void 0:u.preloadedExplanation),32&o&&(l.isAgentFromDifferentRepo=s[5]),e.$set(l)},i(s){t||(f(e.$$.fragment,s),t=!0)},o(s){h(e.$$.fragment,s),t=!1},d(s){R(e,s)}}}function Xu(r){let e,t;return e=new eo({props:{changedFiles:r[0],onApplyChanges:r[9],onOpenFile:r[10],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.changedFiles=n[0]),2&i&&(s.pendingFiles=n[1]),4&i&&(s.appliedFiles=n[2]),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function _i(r){let e,t,n,i;const s=[Xu,Ju],o=[];function l(a,u){return a[6]==="changedFiles"?0:1}return e=l(r),t=o[e]=s[e](r),{c(){t.c(),n=xe()},m(a,u){o[e].m(a,u),g(a,n,u),i=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(H(),h(o[c],1,1,()=>{o[c]=null}),U(),t=o[e],t?t.p(a,u):(t=o[e]=s[e](a),t.c()),f(t,1),t.m(n.parentNode,n))},i(a){i||(f(t),i=!0)},o(a){h(t),i=!1},d(a){a&&$(n),o[e].d(a)}}}function Yu(r){let e,t,n,i=r[0]&&Ei(r);return{c(){e=b("div"),t=b("div"),i&&i.c(),x(t,"class","file-explorer-main svelte-5tfpo4"),x(e,"class","diff-page svelte-5tfpo4")},m(s,o){g(s,e,o),q(e,t),i&&i.m(t,null),n=!0},p(s,[o]){s[0]?i?(i.p(s,o),1&o&&f(i,1)):(i=Ei(s),i.c(),f(i,1),i.m(t,null)):i&&(H(),h(i,1,1,()=>{i=null}),U())},i(s){n||(f(i),n=!0)},o(s){h(i),n=!1},d(s){s&&$(e),i&&i.d()}}}function Ku(r,e,t){let n,{changedFiles:i=[]}=e,{pendingFiles:s=[]}=e,{appliedFiles:o=[]}=e,{agentLabel:l}=e,{latestUserPrompt:a}=e,{isAgentFromDifferentRepo:u=!1}=e;const c=st(ot.key),d=st(Ct.key);Me(r,d,F=>t(7,n=F));let p="summary";return function(F){F.subscribe(y=>{if(y){const w=document.getElementById(Mt(y));w&&w.scrollIntoView({behavior:"smooth",block:"center"})}})}(function(F=null){const y=Ue(F);return zt(Xi,y),y}(null)),r.$$set=F=>{"changedFiles"in F&&t(0,i=F.changedFiles),"pendingFiles"in F&&t(1,s=F.pendingFiles),"appliedFiles"in F&&t(2,o=F.appliedFiles),"agentLabel"in F&&t(3,l=F.agentLabel),"latestUserPrompt"in F&&t(4,a=F.latestUserPrompt),"isAgentFromDifferentRepo"in F&&t(5,u=F.isAgentFromDifferentRepo)},[i,s,o,l,a,u,p,n,d,async(F,y,w)=>{await c.applyChanges(F,y,w)},F=>c.openFile(F),()=>{t(6,p="changedFiles")}]}class ec extends ee{constructor(e){super(),te(this,e,Ku,Yu,X,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function tc(r){let e,t,n,i,s;return t=new Tt({props:{size:1}}),{c(){e=b("div"),L(t.$$.fragment),n=I(),i=b("p"),i.textContent="Loading diff view...",x(e,"class","l-center svelte-ccste2")},m(o,l){g(o,e,l),M(t,e,null),q(e,n),q(e,i),s=!0},p:Y,i(o){s||(f(t.$$.fragment,o),s=!0)},o(o){h(t.$$.fragment,o),s=!1},d(o){o&&$(e),R(t)}}}function nc(r){let e,t;return e=new ec({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[],isAgentFromDifferentRepo:r[0].isAgentFromDifferentRepo||!1}}),{c(){L(e.$$.fragment)},m(n,i){M(e,n,i),t=!0},p(n,i){const s={};1&i&&(s.changedFiles=n[0].changedFiles),1&i&&(s.agentLabel=n[0].sessionSummary),1&i&&(s.latestUserPrompt=n[0].userPrompt),1&i&&(s.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),e.$set(s)},i(n){t||(f(e.$$.fragment,n),t=!0)},o(n){h(e.$$.fragment,n),t=!1},d(n){R(e,n)}}}function ic(r){let e,t,n,i;const s=[nc,tc],o=[];function l(a,u){return a[0]?0:1}return t=l(r),n=o[t]=s[t](r),{c(){e=b("div"),n.c(),x(e,"class","l-main svelte-ccste2")},m(a,u){g(a,e,u),o[t].m(e,null),i=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(H(),h(o[c],1,1,()=>{o[c]=null}),U(),n=o[t],n?n.p(a,u):(n=o[t]=s[t](a),n.c()),f(n,1),n.m(e,null))},i(a){i||(f(n),i=!0)},o(a){h(n),i=!1},d(a){a&&$(e),o[t].d()}}}function sc(r){let e,t,n,i;return e=new _s.Root({props:{$$slots:{default:[ic]},$$scope:{ctx:r}}}),{c(){L(e.$$.fragment)},m(s,o){M(e,s,o),t=!0,n||(i=rt(window,"message",r[1].onMessageFromExtension),n=!0)},p(s,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:s}),e.$set(l)},i(s){t||(f(e.$$.fragment,s),t=!0)},o(s){h(e.$$.fragment,s),t=!1},d(s){R(e,s),n=!1,i()}}}function rc(r,e,t){let n,i,s=new Cs(Pi),o=new Ct(s);Me(r,o,a=>t(4,i=a)),s.registerConsumer(o);let l=new ot(s);return zt(ot.key,l),zt(Ct.key,o),Ke(()=>(o.onPanelLoaded(),()=>{s.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&t(0,n=i.opts)},[n,s,o,l,i]}new class extends ee{constructor(r){super(),te(this,r,rc,sc,X,{})}}({target:document.getElementById("app")});

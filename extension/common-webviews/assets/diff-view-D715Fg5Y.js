var lg=Object.defineProperty;var Eh=o=>{throw TypeError(o)};var hg=(o,t,n)=>t in o?lg(o,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[t]=n;var v=(o,t,n)=>hg(o,typeof t!="symbol"?t+"":t,n),jc=(o,t,n)=>t.has(o)||Eh("Cannot "+n);var x=(o,t,n)=>(jc(o,t,"read from private field"),n?n.call(o):t.get(o)),Et=(o,t,n)=>t.has(o)?Eh("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,n),ut=(o,t,n,r)=>(jc(o,t,"write to private field"),r?r.call(o,n):t.set(o,n),n),Y=(o,t,n)=>(jc(o,t,"access private method"),n);var ea=(o,t,n,r)=>({set _(s){ut(o,t,s,n)},get _(){return x(o,t,r)}});import{I as an,J as Cf,ai as ae,ak as le,az as Ah,al as Kc,S as pi,i as gi,s as mi,ac as pr,V as Vt,D as Nt,y as V,c as qt,a3 as vn,ab as Pi,e as rt,f as Gt,z as G,a6 as Sf,a5 as Ur,u as F,q as $e,t as z,r as ke,h as it,B as Z,a7 as vu,ag as fi,G as Ce,w as Ui,x as fg,A as dg,E as _r,n as Bt,C as dr,am as If,H as gr,aj as qc,aA as Qi,T as Th,af as pg,a0 as yu,F as Mf,an as gg,a1 as Ef,ah as mg,U as _g}from"./SpinnerAugment-BGEGncoZ.js";import"./design-system-init-BFmnHByk.js";import{g as vg}from"./globals-D0QH3NT1.js";import{S as yg,W as oe,h as Ns,D as Pc,e as fa}from"./BaseButton-Bbk8_XKh.js";import{A as bg,I as xg}from"./IconButtonAugment-DR78svzs.js";import{i as hr,a as Wi,b as Xe,c as Ds,S as Fs,d as Rs,e as $a,C as wg,E as $g,D as kg,f as Cg,g as Sg,s as Uc,h as bu,j as da,k as pa,l as xu,m as ga,n as wu,o as ma,A as Ig,p as _a,q as Wc,r as Mg,t as Eg,u as Ps,v as Af,U as Ag,w as Tg,x as Fg,y as Rg}from"./chat-flags-model-B_v7LnHp.js";import{F as Tf,C as Og,a as Lg,b as Ff,P as Ng}from"./folder-opened-CVKdq1wC.js";import{C as Yc,i as Dg,j as Fh,A as Rh,a as Oh}from"./rules-parser-D8-cU5vK.js";import{P as Ae,C as zs,a as On,I as As,E as zg}from"./chat-types-NgqNgjwU.js";import{a as jg}from"./types-BSMhNRWH.js";import{M as Rf,T as qg}from"./TextTooltipAugment-Cor0M5Er.js";import{K as _i,A as Lh,R as Pg,B as Ug,P as Wg,T as Bg,a as Of,b as Hg,C as Vg,c as Gg,G as Zg,d as Qg,M as Xc,e as Lf,f as Kg,g as Yg}from"./Keybindings-BaohEaHR.js";import{B as Wr}from"./ButtonAugment-DZMhbPz9.js";import{T as Xg}from"./Content-CTqTUTf_.js";import{F as Pe}from"./Filespan-CprfUNtE.js";import{D as Bc}from"./index-BDfvUmRL.js";import{M as Ki}from"./MaterialIcon-MCURCZji.js";import{E as Jg}from"./exclamation-triangle-DimRUUrg.js";import{a as tm,M as em,g as nm,C as rm}from"./index-D3MPLNkT.js";import"./index-BS_CDetd.js";import"./file-paths-BcSg4gks.js";import"./pen-to-square-TjBwLyJp.js";import"./CardAugment-oII5PndH.js";import"./augment-logo-iIh97Yc5.js";var Nh=NaN,im="[object Symbol]",sm=/^\s+|\s+$/g,om=/^[-+]0x[0-9a-f]+$/i,am=/^0b[01]+$/i,cm=/^0o[0-7]+$/i,um=parseInt,lm=typeof an=="object"&&an&&an.Object===Object&&an,hm=typeof self=="object"&&self&&self.Object===Object&&self,fm=lm||hm||Function("return this")(),dm=Object.prototype.toString,pm=Math.max,gm=Math.min,Hc=function(){return fm.Date.now()};function Jc(o){var t=typeof o;return!!o&&(t=="object"||t=="function")}function Dh(o){if(typeof o=="number")return o;if(function(r){return typeof r=="symbol"||function(s){return!!s&&typeof s=="object"}(r)&&dm.call(r)==im}(o))return Nh;if(Jc(o)){var t=typeof o.valueOf=="function"?o.valueOf():o;o=Jc(t)?t+"":t}if(typeof o!="string")return o===0?o:+o;o=o.replace(sm,"");var n=am.test(o);return n||cm.test(o)?um(o.slice(2),n?2:8):om.test(o)?Nh:+o}const tu=Cf(function(o,t,n){var r,s,a,u,l,f,m=0,g=!1,y=!1,b=!0;if(typeof o!="function")throw new TypeError("Expected a function");function k(E){var H=r,ot=s;return r=s=void 0,m=E,u=o.apply(ot,H)}function T(E){var H=E-f;return f===void 0||H>=t||H<0||y&&E-m>=a}function j(){var E=Hc();if(T(E))return q(E);l=setTimeout(j,function(H){var ot=t-(H-f);return y?gm(ot,a-(H-m)):ot}(E))}function q(E){return l=void 0,b&&r?k(E):(r=s=void 0,u)}function S(){var E=Hc(),H=T(E);if(r=arguments,s=this,f=E,H){if(l===void 0)return function(ot){return m=ot,l=setTimeout(j,t),g?k(ot):u}(f);if(y)return l=setTimeout(j,t),k(f)}return l===void 0&&(l=setTimeout(j,t)),u}return t=Dh(t)||0,Jc(n)&&(g=!!n.leading,a=(y="maxWait"in n)?pm(Dh(n.maxWait)||0,t):a,b="trailing"in n?!!n.trailing:b),S.cancel=function(){l!==void 0&&clearTimeout(l),m=0,r=f=s=l=void 0},S.flush=function(){return l===void 0?u:q(Hc())},S});var eu={exports:{}};(function(o,t){var n="__lodash_hash_undefined__",r=1,s=2,a=9007199254740991,u="[object Arguments]",l="[object Array]",f="[object AsyncFunction]",m="[object Boolean]",g="[object Date]",y="[object Error]",b="[object Function]",k="[object GeneratorFunction]",T="[object Map]",j="[object Number]",q="[object Null]",S="[object Object]",E="[object Promise]",H="[object Proxy]",ot="[object RegExp]",st="[object Set]",Dt="[object String]",pt="[object Symbol]",St="[object Undefined]",mt="[object WeakMap]",Ft="[object ArrayBuffer]",Ht="[object DataView]",ce=/^\[object .+?Constructor\]$/,Ct=/^(?:0|[1-9]\d*)$/,wt={};wt["[object Float32Array]"]=wt["[object Float64Array]"]=wt["[object Int8Array]"]=wt["[object Int16Array]"]=wt["[object Int32Array]"]=wt["[object Uint8Array]"]=wt["[object Uint8ClampedArray]"]=wt["[object Uint16Array]"]=wt["[object Uint32Array]"]=!0,wt[u]=wt[l]=wt[Ft]=wt[m]=wt[Ht]=wt[g]=wt[y]=wt[b]=wt[T]=wt[j]=wt[S]=wt[ot]=wt[st]=wt[Dt]=wt[mt]=!1;var he=typeof an=="object"&&an&&an.Object===Object&&an,yn=typeof self=="object"&&self&&self.Object===Object&&self,Se=he||yn||Function("return this")(),dt=t&&!t.nodeType&&t,bn=dt&&o&&!o.nodeType&&o,Ln=bn&&bn.exports===dt,Yn=Ln&&he.process,vr=function(){try{return Yn&&Yn.binding&&Yn.binding("util")}catch{}}(),yi=vr&&vr.isTypedArray;function Yi(C,A){for(var W=-1,et=C==null?0:C.length;++W<et;)if(A(C[W],W,C))return!0;return!1}function Ca(C){var A=-1,W=Array(C.size);return C.forEach(function(et,Ut){W[++A]=[Ut,et]}),W}function Sa(C){var A=-1,W=Array(C.size);return C.forEach(function(et){W[++A]=et}),W}var Vs,Xi,Ji,Ia=Array.prototype,Ma=Function.prototype,bi=Object.prototype,ts=Se["__core-js_shared__"],es=Ma.toString,cn=bi.hasOwnProperty,Gs=(Vs=/[^.]+$/.exec(ts&&ts.keys&&ts.keys.IE_PROTO||""))?"Symbol(src)_1."+Vs:"",Zs=bi.toString,ns=RegExp("^"+es.call(cn).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Qs=Ln?Se.Buffer:void 0,yr=Se.Symbol,Ks=Se.Uint8Array,Ys=bi.propertyIsEnumerable,Ea=Ia.splice,Xn=yr?yr.toStringTag:void 0,Xs=Object.getOwnPropertySymbols,Aa=Qs?Qs.isBuffer:void 0,Ta=(Xi=Object.keys,Ji=Object,function(C){return Xi(Ji(C))}),rs=qn(Se,"DataView"),br=qn(Se,"Map"),is=qn(Se,"Promise"),ss=qn(Se,"Set"),os=qn(Se,"WeakMap"),Hr=qn(Object,"create"),Fa=Jn(rs),Ra=Jn(br),xi=Jn(is),Oa=Jn(ss),wi=Jn(os),as=yr?yr.prototype:void 0,$i=as?as.valueOf:void 0;function Nn(C){var A=-1,W=C==null?0:C.length;for(this.clear();++A<W;){var et=C[A];this.set(et[0],et[1])}}function un(C){var A=-1,W=C==null?0:C.length;for(this.clear();++A<W;){var et=C[A];this.set(et[0],et[1])}}function Dn(C){var A=-1,W=C==null?0:C.length;for(this.clear();++A<W;){var et=C[A];this.set(et[0],et[1])}}function Vr(C){var A=-1,W=C==null?0:C.length;for(this.__data__=new Dn;++A<W;)this.add(C[A])}function zn(C){var A=this.__data__=new un(C);this.size=A.size}function La(C,A){var W=Zr(C),et=!W&&ro(C),Ut=!W&&!et&&Ci(C),J=!W&&!et&&!Ut&&oo(C),ft=W||et||Ut||J,te=ft?function(ee,ln){for(var Wt=-1,fe=Array(ee);++Wt<ee;)fe[Wt]=ln(Wt);return fe}(C.length,String):[],Ge=te.length;for(var re in C)!cn.call(C,re)||ft&&(re=="length"||Ut&&(re=="offset"||re=="parent")||J&&(re=="buffer"||re=="byteLength"||re=="byteOffset")||no(re,Ge))||te.push(re);return te}function Gr(C,A){for(var W=C.length;W--;)if(ls(C[W][0],A))return W;return-1}function jn(C){return C==null?C===void 0?St:q:Xn&&Xn in Object(C)?function(A){var W=cn.call(A,Xn),et=A[Xn];try{A[Xn]=void 0;var Ut=!0}catch{}var J=Zs.call(A);return Ut&&(W?A[Xn]=et:delete A[Xn]),J}(C):function(A){return Zs.call(A)}(C)}function cs(C){return Qr(C)&&jn(C)==u}function Js(C,A,W,et,Ut){return C===A||(C==null||A==null||!Qr(C)&&!Qr(A)?C!=C&&A!=A:function(J,ft,te,Ge,re,ee){var ln=Zr(J),Wt=Zr(ft),fe=ln?l:xn(J),Ie=Wt?l:xn(ft),tr=(fe=fe==u?S:fe)==S,$r=(Ie=Ie==u?S:Ie)==S,de=fe==Ie;if(de&&Ci(J)){if(!Ci(ft))return!1;ln=!0,tr=!1}if(de&&!tr)return ee||(ee=new zn),ln||oo(J)?ki(J,ft,te,Ge,re,ee):function(Rt,At,ye,Pn,Te,Ue,hn){switch(ye){case Ht:if(Rt.byteLength!=At.byteLength||Rt.byteOffset!=At.byteOffset)return!1;Rt=Rt.buffer,At=At.buffer;case Ft:return!(Rt.byteLength!=At.byteLength||!Ue(new Ks(Rt),new Ks(At)));case m:case g:case j:return ls(+Rt,+At);case y:return Rt.name==At.name&&Rt.message==At.message;case ot:case Dt:return Rt==At+"";case T:var Me=Ca;case st:var Un=Pn&r;if(Me||(Me=Sa),Rt.size!=At.size&&!Un)return!1;var Cr=hn.get(Rt);if(Cr)return Cr==At;Pn|=s,hn.set(Rt,At);var zt=ki(Me(Rt),Me(At),Pn,Te,Ue,hn);return hn.delete(Rt),zt;case pt:if($i)return $i.call(Rt)==$i.call(At)}return!1}(J,ft,fe,te,Ge,re,ee);if(!(te&r)){var kr=tr&&cn.call(J,"__wrapped__"),ds=$r&&cn.call(ft,"__wrapped__");if(kr||ds){var ao=kr?J.value():J,co=ds?ft.value():ft;return ee||(ee=new zn),re(ao,co,te,Ge,ee)}}return de?(ee||(ee=new zn),function(Rt,At,ye,Pn,Te,Ue){var hn=ye&r,Me=us(Rt),Un=Me.length,Cr=us(At),zt=Cr.length;if(Un!=zt&&!hn)return!1;for(var Ze=Un;Ze--;){var wn=Me[Ze];if(!(hn?wn in At:cn.call(At,wn)))return!1}var uo=Ue.get(Rt);if(uo&&Ue.get(At))return uo==At;var Sr=!0;Ue.set(Rt,At),Ue.set(At,Rt);for(var ps=hn;++Ze<Un;){var Kr=Rt[wn=Me[Ze]],er=At[wn];if(Pn)var nr=hn?Pn(er,Kr,wn,At,Rt,Ue):Pn(Kr,er,wn,Rt,At,Ue);if(!(nr===void 0?Kr===er||Te(Kr,er,ye,Pn,Ue):nr)){Sr=!1;break}ps||(ps=wn=="constructor")}if(Sr&&!ps){var Si=Rt.constructor,Yr=At.constructor;Si==Yr||!("constructor"in Rt)||!("constructor"in At)||typeof Si=="function"&&Si instanceof Si&&typeof Yr=="function"&&Yr instanceof Yr||(Sr=!1)}return Ue.delete(Rt),Ue.delete(At),Sr}(J,ft,te,Ge,re,ee)):!1}(C,A,W,et,Js,Ut))}function to(C){return!(!so(C)||function(A){return!!Gs&&Gs in A}(C))&&(hs(C)?ns:ce).test(Jn(C))}function eo(C){if(W=(A=C)&&A.constructor,et=typeof W=="function"&&W.prototype||bi,A!==et)return Ta(C);var A,W,et,Ut=[];for(var J in Object(C))cn.call(C,J)&&J!="constructor"&&Ut.push(J);return Ut}function ki(C,A,W,et,Ut,J){var ft=W&r,te=C.length,Ge=A.length;if(te!=Ge&&!(ft&&Ge>te))return!1;var re=J.get(C);if(re&&J.get(A))return re==A;var ee=-1,ln=!0,Wt=W&s?new Vr:void 0;for(J.set(C,A),J.set(A,C);++ee<te;){var fe=C[ee],Ie=A[ee];if(et)var tr=ft?et(Ie,fe,ee,A,C,J):et(fe,Ie,ee,C,A,J);if(tr!==void 0){if(tr)continue;ln=!1;break}if(Wt){if(!Yi(A,function($r,de){if(kr=de,!Wt.has(kr)&&(fe===$r||Ut(fe,$r,W,et,J)))return Wt.push(de);var kr})){ln=!1;break}}else if(fe!==Ie&&!Ut(fe,Ie,W,et,J)){ln=!1;break}}return J.delete(C),J.delete(A),ln}function us(C){return function(A,W,et){var Ut=W(A);return Zr(A)?Ut:function(J,ft){for(var te=-1,Ge=ft.length,re=J.length;++te<Ge;)J[re+te]=ft[te];return J}(Ut,et(A))}(C,fs,wr)}function xr(C,A){var W,et,Ut=C.__data__;return((et=typeof(W=A))=="string"||et=="number"||et=="symbol"||et=="boolean"?W!=="__proto__":W===null)?Ut[typeof A=="string"?"string":"hash"]:Ut.map}function qn(C,A){var W=function(et,Ut){return et==null?void 0:et[Ut]}(C,A);return to(W)?W:void 0}Nn.prototype.clear=function(){this.__data__=Hr?Hr(null):{},this.size=0},Nn.prototype.delete=function(C){var A=this.has(C)&&delete this.__data__[C];return this.size-=A?1:0,A},Nn.prototype.get=function(C){var A=this.__data__;if(Hr){var W=A[C];return W===n?void 0:W}return cn.call(A,C)?A[C]:void 0},Nn.prototype.has=function(C){var A=this.__data__;return Hr?A[C]!==void 0:cn.call(A,C)},Nn.prototype.set=function(C,A){var W=this.__data__;return this.size+=this.has(C)?0:1,W[C]=Hr&&A===void 0?n:A,this},un.prototype.clear=function(){this.__data__=[],this.size=0},un.prototype.delete=function(C){var A=this.__data__,W=Gr(A,C);return!(W<0)&&(W==A.length-1?A.pop():Ea.call(A,W,1),--this.size,!0)},un.prototype.get=function(C){var A=this.__data__,W=Gr(A,C);return W<0?void 0:A[W][1]},un.prototype.has=function(C){return Gr(this.__data__,C)>-1},un.prototype.set=function(C,A){var W=this.__data__,et=Gr(W,C);return et<0?(++this.size,W.push([C,A])):W[et][1]=A,this},Dn.prototype.clear=function(){this.size=0,this.__data__={hash:new Nn,map:new(br||un),string:new Nn}},Dn.prototype.delete=function(C){var A=xr(this,C).delete(C);return this.size-=A?1:0,A},Dn.prototype.get=function(C){return xr(this,C).get(C)},Dn.prototype.has=function(C){return xr(this,C).has(C)},Dn.prototype.set=function(C,A){var W=xr(this,C),et=W.size;return W.set(C,A),this.size+=W.size==et?0:1,this},Vr.prototype.add=Vr.prototype.push=function(C){return this.__data__.set(C,n),this},Vr.prototype.has=function(C){return this.__data__.has(C)},zn.prototype.clear=function(){this.__data__=new un,this.size=0},zn.prototype.delete=function(C){var A=this.__data__,W=A.delete(C);return this.size=A.size,W},zn.prototype.get=function(C){return this.__data__.get(C)},zn.prototype.has=function(C){return this.__data__.has(C)},zn.prototype.set=function(C,A){var W=this.__data__;if(W instanceof un){var et=W.__data__;if(!br||et.length<199)return et.push([C,A]),this.size=++W.size,this;W=this.__data__=new Dn(et)}return W.set(C,A),this.size=W.size,this};var wr=Xs?function(C){return C==null?[]:(C=Object(C),function(A,W){for(var et=-1,Ut=A==null?0:A.length,J=0,ft=[];++et<Ut;){var te=A[et];W(te,et,A)&&(ft[J++]=te)}return ft}(Xs(C),function(A){return Ys.call(C,A)}))}:function(){return[]},xn=jn;function no(C,A){return!!(A=A??a)&&(typeof C=="number"||Ct.test(C))&&C>-1&&C%1==0&&C<A}function Jn(C){if(C!=null){try{return es.call(C)}catch{}try{return C+""}catch{}}return""}function ls(C,A){return C===A||C!=C&&A!=A}(rs&&xn(new rs(new ArrayBuffer(1)))!=Ht||br&&xn(new br)!=T||is&&xn(is.resolve())!=E||ss&&xn(new ss)!=st||os&&xn(new os)!=mt)&&(xn=function(C){var A=jn(C),W=A==S?C.constructor:void 0,et=W?Jn(W):"";if(et)switch(et){case Fa:return Ht;case Ra:return T;case xi:return E;case Oa:return st;case wi:return mt}return A});var ro=cs(function(){return arguments}())?cs:function(C){return Qr(C)&&cn.call(C,"callee")&&!Ys.call(C,"callee")},Zr=Array.isArray,Ci=Aa||function(){return!1};function hs(C){if(!so(C))return!1;var A=jn(C);return A==b||A==k||A==f||A==H}function io(C){return typeof C=="number"&&C>-1&&C%1==0&&C<=a}function so(C){var A=typeof C;return C!=null&&(A=="object"||A=="function")}function Qr(C){return C!=null&&typeof C=="object"}var oo=yi?function(C){return function(A){return C(A)}}(yi):function(C){return Qr(C)&&io(C.length)&&!!wt[jn(C)]};function fs(C){return(A=C)!=null&&io(A.length)&&!hs(A)?La(C):eo(C);var A}o.exports=function(C,A){return Js(C,A)}})(eu,eu.exports);const mm=Cf(eu.exports);function Nf(o){return function(t){return"unitOfCodeWork"in t&&!function(n){return n.children.length>0&&"childIds"in n}(t)}(o)?[o]:o.children.flatMap(Nf)}function Bi(o){var t;return((t=o.extraData)==null?void 0:t.isAgentConversation)===!0}var ve=(o=>(o[o.active=0]="active",o[o.inactive=1]="inactive",o))(ve||{});function _m(o,t,n=1e3){let r=null,s=0;const a=ae(t),u=()=>{const l=(()=>{const f=Date.now();if(r!==null&&f-s<n)return r;const m=o();return r=m,s=f,m})();a.set(l)};return{subscribe:a.subscribe,resetCache:()=>{r=null,u()},updateStore:u}}var Df=(o=>(o[o.unset=0]="unset",o[o.positive=1]="positive",o[o.negative=2]="negative",o))(Df||{}),Os=(o=>(o[o.unknown=0]="unknown",o[o.new=1]="new",o[o.checkingSafety=2]="checkingSafety",o[o.runnable=3]="runnable",o[o.running=4]="running",o[o.completed=5]="completed",o[o.error=6]="error",o[o.cancelling=7]="cancelling",o[o.cancelled=8]="cancelled",o))(Os||{});function Vc(o){return o.requestId+";"+o.toolUseId}function zh(o){const[t,n]=o.split(";");return{requestId:t,toolUseId:n}}function vm(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let vi={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function jh(o){vi=o}const zf=/[&<>"']/,ym=new RegExp(zf.source,"g"),jf=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,bm=new RegExp(jf.source,"g"),xm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},qh=o=>xm[o];function sn(o,t){if(t){if(zf.test(o))return o.replace(ym,qh)}else if(jf.test(o))return o.replace(bm,qh);return o}const wm=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function $m(o){return o.replace(wm,(t,n)=>(n=n.toLowerCase())==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):"")}const km=/(^|[^\[])\^/g;function Pt(o,t){let n=typeof o=="string"?o:o.source;t=t||"";const r={replace:(s,a)=>{let u=typeof a=="string"?a:a.source;return u=u.replace(km,"$1"),n=n.replace(s,u),r},getRegex:()=>new RegExp(n,t)};return r}function Ph(o){try{o=encodeURI(o).replace(/%25/g,"%")}catch{return null}return o}const js={exec:()=>null};function Uh(o,t){const n=o.replace(/\|/g,(s,a,u)=>{let l=!1,f=a;for(;--f>=0&&u[f]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function na(o,t,n){const r=o.length;if(r===0)return"";let s=0;for(;s<r;){const a=o.charAt(r-s-1);if(a!==t||n){if(a===t||!n)break;s++}else s++}return o.slice(0,r-s)}function Wh(o,t,n,r){const s=t.href,a=t.title?sn(t.title):null,u=o[1].replace(/\\([\[\]])/g,"$1");if(o[0].charAt(0)!=="!"){r.state.inLink=!0;const l={type:"link",raw:n,href:s,title:a,text:u,tokens:r.inlineTokens(u)};return r.state.inLink=!1,l}return{type:"image",raw:n,href:s,title:a,text:sn(u)}}class va{constructor(t){v(this,"options");v(this,"rules");v(this,"lexer");this.options=t||vi}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:na(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],s=function(a,u){const l=a.match(/^(\s+)(?:```)/);if(l===null)return u;const f=l[1];return u.split(`
`).map(m=>{const g=m.match(/^\s+/);if(g===null)return m;const[y]=g;return y.length>=f.length?m.slice(f.length):m}).join(`
`)}(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):n[2],text:s}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const s=na(r,"#");this.options.pedantic?r=s.trim():s&&!/ $/.test(s)||(r=s.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=na(n[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const a=this.lexer.blockTokens(r);return this.lexer.state.top=s,{type:"blockquote",raw:n[0],tokens:a,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r=n[1].trim();const s=r.length>1,a={type:"list",raw:"",ordered:s,start:s?+r.slice(0,-1):"",loose:!1,items:[]};r=s?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=s?r:"[*+-]");const u=new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",f="",m=!1;for(;t;){let g=!1;if(!(n=u.exec(t))||this.rules.block.hr.test(t))break;l=n[0],t=t.substring(l.length);let y=n[2].split(`
`,1)[0].replace(/^\t+/,S=>" ".repeat(3*S.length)),b=t.split(`
`,1)[0],k=0;this.options.pedantic?(k=2,f=y.trimStart()):(k=n[2].search(/[^ ]/),k=k>4?1:k,f=y.slice(k),k+=n[1].length);let T=!1;if(!y&&/^ *$/.test(b)&&(l+=b+`
`,t=t.substring(b.length+1),g=!0),!g){const S=new RegExp(`^ {0,${Math.min(3,k-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),E=new RegExp(`^ {0,${Math.min(3,k-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),H=new RegExp(`^ {0,${Math.min(3,k-1)}}(?:\`\`\`|~~~)`),ot=new RegExp(`^ {0,${Math.min(3,k-1)}}#`);for(;t;){const st=t.split(`
`,1)[0];if(b=st,this.options.pedantic&&(b=b.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),H.test(b)||ot.test(b)||S.test(b)||E.test(t))break;if(b.search(/[^ ]/)>=k||!b.trim())f+=`
`+b.slice(k);else{if(T||y.search(/[^ ]/)>=4||H.test(y)||ot.test(y)||E.test(y))break;f+=`
`+b}T||b.trim()||(T=!0),l+=st+`
`,t=t.substring(st.length+1),y=b.slice(k)}}a.loose||(m?a.loose=!0:/\n *\n *$/.test(l)&&(m=!0));let j,q=null;this.options.gfm&&(q=/^\[[ xX]\] /.exec(f),q&&(j=q[0]!=="[ ] ",f=f.replace(/^\[[ xX]\] +/,""))),a.items.push({type:"list_item",raw:l,task:!!q,checked:j,loose:!1,text:f,tokens:[]}),a.raw+=l}a.items[a.items.length-1].raw=l.trimEnd(),a.items[a.items.length-1].text=f.trimEnd(),a.raw=a.raw.trimEnd();for(let g=0;g<a.items.length;g++)if(this.lexer.state.top=!1,a.items[g].tokens=this.lexer.blockTokens(a.items[g].text,[]),!a.loose){const y=a.items[g].tokens.filter(k=>k.type==="space"),b=y.length>0&&y.some(k=>/\n.*\n/.test(k.raw));a.loose=b}if(a.loose)for(let g=0;g<a.items.length;g++)a.items[g].loose=!0;return a}}html(t){const n=this.rules.block.html.exec(t);if(n)return{type:"html",block:!0,raw:n[0],pre:n[1]==="pre"||n[1]==="script"||n[1]==="style",text:n[0]}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),s=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",a=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:s,title:a}}}table(t){const n=this.rules.block.table.exec(t);if(!n||!/[:|]/.test(n[2]))return;const r=Uh(n[1]),s=n[2].replace(/^\||\| *$/g,"").split("|"),a=n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[],u={type:"table",raw:n[0],header:[],align:[],rows:[]};if(r.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?u.align.push("right"):/^ *:-+: *$/.test(l)?u.align.push("center"):/^ *:-+ *$/.test(l)?u.align.push("left"):u.align.push(null);for(const l of r)u.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of a)u.rows.push(Uh(l,u.header.length).map(f=>({text:f,tokens:this.lexer.inline(f)})));return u}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:sn(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const u=na(r.slice(0,-1),"\\");if((r.length-u.length)%2==0)return}else{const u=function(l,f){if(l.indexOf(f[1])===-1)return-1;let m=0;for(let g=0;g<l.length;g++)if(l[g]==="\\")g++;else if(l[g]===f[0])m++;else if(l[g]===f[1]&&(m--,m<0))return g;return-1}(n[2],"()");if(u>-1){const l=(n[0].indexOf("!")===0?5:4)+n[1].length+u;n[2]=n[2].substring(0,u),n[0]=n[0].substring(0,l).trim(),n[3]=""}}let s=n[2],a="";if(this.options.pedantic){const u=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);u&&(s=u[1],a=u[3])}else a=n[3]?n[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(r)?s.slice(1):s.slice(1,-1)),Wh(n,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:a&&a.replace(this.rules.inline.anyPunctuation,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){const s=n[(r[2]||r[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const a=r[0].charAt(0);return{type:"text",raw:a,text:a}}return Wh(r,s,r[0],this.lexer)}}emStrong(t,n,r=""){let s=this.rules.inline.emStrongLDelim.exec(t);if(s&&!(s[3]&&r.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!r||this.rules.inline.punctuation.exec(r))){const a=[...s[0]].length-1;let u,l,f=a,m=0;const g=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(g.lastIndex=0,n=n.slice(-1*t.length+a);(s=g.exec(n))!=null;){if(u=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!u)continue;if(l=[...u].length,s[3]||s[4]){f+=l;continue}if((s[5]||s[6])&&a%3&&!((a+l)%3)){m+=l;continue}if(f-=l,f>0)continue;l=Math.min(l,l+f+m);const y=[...s[0]][0].length,b=t.slice(0,a+s.index+y+l);if(Math.min(a,l)%2){const T=b.slice(1,-1);return{type:"em",raw:b,text:T,tokens:this.lexer.inlineTokens(T)}}const k=b.slice(2,-2);return{type:"strong",raw:b,text:k,tokens:this.lexer.inlineTokens(k)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const s=/[^ ]/.test(r),a=/^ /.test(r)&&/ $/.test(r);return s&&a&&(r=r.substring(1,r.length-1)),r=sn(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t){const n=this.rules.inline.autolink.exec(t);if(n){let r,s;return n[2]==="@"?(r=sn(n[1]),s="mailto:"+r):(r=sn(n[1]),s=r),{type:"link",raw:n[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}url(t){var r;let n;if(n=this.rules.inline.url.exec(t)){let s,a;if(n[2]==="@")s=sn(n[0]),a="mailto:"+s;else{let u;do u=n[0],n[0]=((r=this.rules.inline._backpedal.exec(n[0]))==null?void 0:r[0])??"";while(u!==n[0]);s=sn(n[0]),a=n[1]==="www."?"http://"+n[0]:n[0]}return{type:"link",raw:n[0],text:s,href:a,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(t){const n=this.rules.inline.text.exec(t);if(n){let r;return r=this.lexer.state.inRawBlock?n[0]:sn(n[0]),{type:"text",raw:n[0],text:r}}}}const Bs=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,qf=/(?:[*+-]|\d{1,9}[.)])/,Pf=Pt(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,qf).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),$u=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,ku=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Cm=Pt(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",ku).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Sm=Pt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,qf).getRegex(),ka="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Cu=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Im=Pt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Cu).replace("tag",ka).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Bh=Pt($u).replace("hr",Bs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ka).getRegex(),Su={blockquote:Pt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Bh).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Cm,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:Bs,html:Im,lheading:Pf,list:Sm,newline:/^(?: *(?:\n|$))+/,paragraph:Bh,table:js,text:/^[^\n]+/},Hh=Pt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Bs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ka).getRegex(),Mm={...Su,table:Hh,paragraph:Pt($u).replace("hr",Bs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Hh).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ka).getRegex()},Em={...Su,html:Pt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Cu).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:js,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Pt($u).replace("hr",Bs).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Pf).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Uf=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Wf=/^( {2,}|\\)\n(?!\s*$)/,Hs="\\p{P}\\p{S}",Am=Pt(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,Hs).getRegex(),Tm=Pt(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,Hs).getRegex(),Fm=Pt("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,Hs).getRegex(),Rm=Pt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,Hs).getRegex(),Om=Pt(/\\([punct])/,"gu").replace(/punct/g,Hs).getRegex(),Lm=Pt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Nm=Pt(Cu).replace("(?:-->|$)","-->").getRegex(),Dm=Pt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Nm).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),ya=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,zm=Pt(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",ya).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Vh=Pt(/^!?\[(label)\]\[(ref)\]/).replace("label",ya).replace("ref",ku).getRegex(),Gh=Pt(/^!?\[(ref)\](?:\[\])?/).replace("ref",ku).getRegex(),Iu={_backpedal:js,anyPunctuation:Om,autolink:Lm,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:Wf,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:js,emStrongLDelim:Tm,emStrongRDelimAst:Fm,emStrongRDelimUnd:Rm,escape:Uf,link:zm,nolink:Gh,punctuation:Am,reflink:Vh,reflinkSearch:Pt("reflink|nolink(?!\\()","g").replace("reflink",Vh).replace("nolink",Gh).getRegex(),tag:Dm,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:js},jm={...Iu,link:Pt(/^!?\[(label)\]\((.*?)\)/).replace("label",ya).getRegex(),reflink:Pt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ya).getRegex()},nu={...Iu,escape:Pt(Uf).replace("])","~|])").getRegex(),url:Pt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},qm={...nu,br:Pt(Wf).replace("{2,}","*").getRegex(),text:Pt(nu.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ra={normal:Su,gfm:Mm,pedantic:Em},Ts={normal:Iu,gfm:nu,breaks:qm,pedantic:jm};class Zn{constructor(t){v(this,"tokens");v(this,"options");v(this,"state");v(this,"tokenizer");v(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||vi,this.options.tokenizer=this.options.tokenizer||new va,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:ra.normal,inline:Ts.normal};this.options.pedantic?(n.block=ra.pedantic,n.inline=Ts.pedantic):this.options.gfm&&(n.block=ra.gfm,this.options.breaks?n.inline=Ts.breaks:n.inline=Ts.gfm),this.tokenizer.rules=n}static get rules(){return{block:ra,inline:Ts}}static lex(t,n){return new Zn(n).lex(t)}static lexInline(t,n){return new Zn(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){const r=this.inlineQueue[n];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,n=[]){let r,s,a,u;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(l,f,m)=>f+"    ".repeat(m.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(r=l.call({lexer:this},t,n))&&(t=t.substring(r.raw.length),n.push(r),!0))))if(r=this.tokenizer.space(t))t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);else if(r=this.tokenizer.code(t))t=t.substring(r.raw.length),s=n[n.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?n.push(r):(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(r=this.tokenizer.fences(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.heading(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.hr(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.blockquote(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.list(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.html(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.def(t))t=t.substring(r.raw.length),s=n[n.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title}):(s.raw+=`
`+r.raw,s.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(r=this.tokenizer.table(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.lheading(t))t=t.substring(r.raw.length),n.push(r);else{if(a=t,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const f=t.slice(1);let m;this.options.extensions.startBlock.forEach(g=>{m=g.call({lexer:this},f),typeof m=="number"&&m>=0&&(l=Math.min(l,m))}),l<1/0&&l>=0&&(a=t.substring(0,l+1))}if(this.state.top&&(r=this.tokenizer.paragraph(a)))s=n[n.length-1],u&&s.type==="paragraph"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):n.push(r),u=a.length!==t.length,t=t.substring(r.raw.length);else if(r=this.tokenizer.text(t))t=t.substring(r.raw.length),s=n[n.length-1],s&&s.type==="text"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):n.push(r);else if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,s,a,u,l,f,m=t;if(this.tokens.links){const g=Object.keys(this.tokens.links);if(g.length>0)for(;(u=this.tokenizer.rules.inline.reflinkSearch.exec(m))!=null;)g.includes(u[0].slice(u[0].lastIndexOf("[")+1,-1))&&(m=m.slice(0,u.index)+"["+"a".repeat(u[0].length-2)+"]"+m.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(u=this.tokenizer.rules.inline.blockSkip.exec(m))!=null;)m=m.slice(0,u.index)+"["+"a".repeat(u[0].length-2)+"]"+m.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(u=this.tokenizer.rules.inline.anyPunctuation.exec(m))!=null;)m=m.slice(0,u.index)+"++"+m.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(l||(f=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(g=>!!(r=g.call({lexer:this},t,n))&&(t=t.substring(r.raw.length),n.push(r),!0))))if(r=this.tokenizer.escape(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.tag(t))t=t.substring(r.raw.length),s=n[n.length-1],s&&r.type==="text"&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);else if(r=this.tokenizer.link(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(r.raw.length),s=n[n.length-1],s&&r.type==="text"&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);else if(r=this.tokenizer.emStrong(t,m,f))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.codespan(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.br(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.del(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.autolink(t))t=t.substring(r.raw.length),n.push(r);else if(this.state.inLink||!(r=this.tokenizer.url(t))){if(a=t,this.options.extensions&&this.options.extensions.startInline){let g=1/0;const y=t.slice(1);let b;this.options.extensions.startInline.forEach(k=>{b=k.call({lexer:this},y),typeof b=="number"&&b>=0&&(g=Math.min(g,b))}),g<1/0&&g>=0&&(a=t.substring(0,g+1))}if(r=this.tokenizer.inlineText(a))t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(f=r.raw.slice(-1)),l=!0,s=n[n.length-1],s&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);else if(t){const g="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(g);break}throw new Error(g)}}else t=t.substring(r.raw.length),n.push(r);return n}}class ba{constructor(t){v(this,"options");this.options=t||vi}code(t,n,r){var a;const s=(a=(n||"").match(/^\S*/))==null?void 0:a[0];return t=t.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+sn(s)+'">'+(r?t:sn(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:sn(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,n){return t}heading(t,n,r){return`<h${n}>${t}</h${n}>
`}hr(){return`<hr>
`}list(t,n,r){const s=n?"ol":"ul";return"<"+s+(n&&r!==1?' start="'+r+'"':"")+`>
`+t+"</"+s+`>
`}listitem(t,n,r){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){const s=Ph(t);if(s===null)return r;let a='<a href="'+(t=s)+'"';return n&&(a+=' title="'+n+'"'),a+=">"+r+"</a>",a}image(t,n,r){const s=Ph(t);if(s===null)return r;let a=`<img src="${t=s}" alt="${r}"`;return n&&(a+=` title="${n}"`),a+=">",a}text(t){return t}}class Mu{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class Qn{constructor(t){v(this,"options");v(this,"renderer");v(this,"textRenderer");this.options=t||vi,this.options.renderer=this.options.renderer||new ba,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Mu}static parse(t,n){return new Qn(n).parse(t)}static parseInline(t,n){return new Qn(n).parseInline(t)}parse(t,n=!0){let r="";for(let s=0;s<t.length;s++){const a=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const u=a,l=this.options.extensions.renderers[u.type].call({parser:this},u);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)){r+=l||"";continue}}switch(a.type){case"space":continue;case"hr":r+=this.renderer.hr();continue;case"heading":{const u=a;r+=this.renderer.heading(this.parseInline(u.tokens),u.depth,$m(this.parseInline(u.tokens,this.textRenderer)));continue}case"code":{const u=a;r+=this.renderer.code(u.text,u.lang,!!u.escaped);continue}case"table":{const u=a;let l="",f="";for(let g=0;g<u.header.length;g++)f+=this.renderer.tablecell(this.parseInline(u.header[g].tokens),{header:!0,align:u.align[g]});l+=this.renderer.tablerow(f);let m="";for(let g=0;g<u.rows.length;g++){const y=u.rows[g];f="";for(let b=0;b<y.length;b++)f+=this.renderer.tablecell(this.parseInline(y[b].tokens),{header:!1,align:u.align[b]});m+=this.renderer.tablerow(f)}r+=this.renderer.table(l,m);continue}case"blockquote":{const u=a,l=this.parse(u.tokens);r+=this.renderer.blockquote(l);continue}case"list":{const u=a,l=u.ordered,f=u.start,m=u.loose;let g="";for(let y=0;y<u.items.length;y++){const b=u.items[y],k=b.checked,T=b.task;let j="";if(b.task){const q=this.renderer.checkbox(!!k);m?b.tokens.length>0&&b.tokens[0].type==="paragraph"?(b.tokens[0].text=q+" "+b.tokens[0].text,b.tokens[0].tokens&&b.tokens[0].tokens.length>0&&b.tokens[0].tokens[0].type==="text"&&(b.tokens[0].tokens[0].text=q+" "+b.tokens[0].tokens[0].text)):b.tokens.unshift({type:"text",text:q+" "}):j+=q+" "}j+=this.parse(b.tokens,m),g+=this.renderer.listitem(j,T,!!k)}r+=this.renderer.list(g,l,f);continue}case"html":{const u=a;r+=this.renderer.html(u.text,u.block);continue}case"paragraph":{const u=a;r+=this.renderer.paragraph(this.parseInline(u.tokens));continue}case"text":{let u=a,l=u.tokens?this.parseInline(u.tokens):u.text;for(;s+1<t.length&&t[s+1].type==="text";)u=t[++s],l+=`
`+(u.tokens?this.parseInline(u.tokens):u.text);r+=n?this.renderer.paragraph(l):l;continue}default:{const u='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}parseInline(t,n){n=n||this.renderer;let r="";for(let s=0;s<t.length;s++){const a=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const u=this.options.extensions.renderers[a.type].call({parser:this},a);if(u!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){r+=u||"";continue}}switch(a.type){case"escape":{const u=a;r+=n.text(u.text);break}case"html":{const u=a;r+=n.html(u.text);break}case"link":{const u=a;r+=n.link(u.href,u.title,this.parseInline(u.tokens,n));break}case"image":{const u=a;r+=n.image(u.href,u.title,u.text);break}case"strong":{const u=a;r+=n.strong(this.parseInline(u.tokens,n));break}case"em":{const u=a;r+=n.em(this.parseInline(u.tokens,n));break}case"codespan":{const u=a;r+=n.codespan(u.text);break}case"br":r+=n.br();break;case"del":{const u=a;r+=n.del(this.parseInline(u.tokens,n));break}case"text":{const u=a;r+=n.text(u.text);break}default:{const u='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}}class qs{constructor(t){v(this,"options");this.options=t||vi}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}v(qs,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var di,ru,Bf,xf;const ai=new(xf=class{constructor(...o){Et(this,di);v(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});v(this,"options",this.setOptions);v(this,"parse",Y(this,di,ru).call(this,Zn.lex,Qn.parse));v(this,"parseInline",Y(this,di,ru).call(this,Zn.lexInline,Qn.parseInline));v(this,"Parser",Qn);v(this,"Renderer",ba);v(this,"TextRenderer",Mu);v(this,"Lexer",Zn);v(this,"Tokenizer",va);v(this,"Hooks",qs);this.use(...o)}walkTokens(o,t){var r,s;let n=[];for(const a of o)switch(n=n.concat(t.call(this,a)),a.type){case"table":{const u=a;for(const l of u.header)n=n.concat(this.walkTokens(l.tokens,t));for(const l of u.rows)for(const f of l)n=n.concat(this.walkTokens(f.tokens,t));break}case"list":{const u=a;n=n.concat(this.walkTokens(u.items,t));break}default:{const u=a;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[u.type]?this.defaults.extensions.childTokens[u.type].forEach(l=>{const f=u[l].flat(1/0);n=n.concat(this.walkTokens(f,t))}):u.tokens&&(n=n.concat(this.walkTokens(u.tokens,t)))}}return n}use(...o){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return o.forEach(n=>{const r={...n};if(r.async=this.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=t.renderers[s.name];t.renderers[s.name]=a?function(...u){let l=s.renderer.apply(this,u);return l===!1&&(l=a.apply(this,u)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=t[s.level];a?a.unshift(s.tokenizer):t[s.level]=[s.tokenizer],s.start&&(s.level==="block"?t.startBlock?t.startBlock.push(s.start):t.startBlock=[s.start]:s.level==="inline"&&(t.startInline?t.startInline.push(s.start):t.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(t.childTokens[s.name]=s.childTokens)}),r.extensions=t),n.renderer){const s=this.defaults.renderer||new ba(this.defaults);for(const a in n.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(a==="options")continue;const u=a,l=n.renderer[u],f=s[u];s[u]=(...m)=>{let g=l.apply(s,m);return g===!1&&(g=f.apply(s,m)),g||""}}r.renderer=s}if(n.tokenizer){const s=this.defaults.tokenizer||new va(this.defaults);for(const a in n.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const u=a,l=n.tokenizer[u],f=s[u];s[u]=(...m)=>{let g=l.apply(s,m);return g===!1&&(g=f.apply(s,m)),g}}r.tokenizer=s}if(n.hooks){const s=this.defaults.hooks||new qs;for(const a in n.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(a==="options")continue;const u=a,l=n.hooks[u],f=s[u];qs.passThroughHooks.has(a)?s[u]=m=>{if(this.defaults.async)return Promise.resolve(l.call(s,m)).then(y=>f.call(s,y));const g=l.call(s,m);return f.call(s,g)}:s[u]=(...m)=>{let g=l.apply(s,m);return g===!1&&(g=f.apply(s,m)),g}}r.hooks=s}if(n.walkTokens){const s=this.defaults.walkTokens,a=n.walkTokens;r.walkTokens=function(u){let l=[];return l.push(a.call(this,u)),s&&(l=l.concat(s.call(this,u))),l}}this.defaults={...this.defaults,...r}}),this}setOptions(o){return this.defaults={...this.defaults,...o},this}lexer(o,t){return Zn.lex(o,t??this.defaults)}parser(o,t){return Qn.parse(o,t??this.defaults)}},di=new WeakSet,ru=function(o,t){return(n,r)=>{const s={...r},a={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(a.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),a.async=!0);const u=Y(this,di,Bf).call(this,!!a.silent,!!a.async);if(n==null)return u(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return u(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(a.hooks&&(a.hooks.options=a),a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(n):n).then(l=>o(l,a)).then(l=>a.hooks?a.hooks.processAllTokens(l):l).then(l=>a.walkTokens?Promise.all(this.walkTokens(l,a.walkTokens)).then(()=>l):l).then(l=>t(l,a)).then(l=>a.hooks?a.hooks.postprocess(l):l).catch(u);try{a.hooks&&(n=a.hooks.preprocess(n));let l=o(n,a);a.hooks&&(l=a.hooks.processAllTokens(l)),a.walkTokens&&this.walkTokens(l,a.walkTokens);let f=t(l,a);return a.hooks&&(f=a.hooks.postprocess(f)),f}catch(l){return u(l)}}},Bf=function(o,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,o){const r="<p>An error occurred:</p><pre>"+sn(n.message+"",!0)+"</pre>";return t?Promise.resolve(r):r}if(t)return Promise.reject(n);throw n}},xf);function jt(o,t){return ai.parse(o,t)}jt.options=jt.setOptions=function(o){return ai.setOptions(o),jt.defaults=ai.defaults,jh(jt.defaults),jt},jt.getDefaults=vm,jt.defaults=vi,jt.use=function(...o){return ai.use(...o),jt.defaults=ai.defaults,jh(jt.defaults),jt},jt.walkTokens=function(o,t){return ai.walkTokens(o,t)},jt.parseInline=ai.parseInline,jt.Parser=Qn,jt.parser=Qn.parse,jt.Renderer=ba,jt.TextRenderer=Mu,jt.Lexer=Zn,jt.lexer=Zn.lex,jt.Tokenizer=va,jt.Hooks=qs,jt.parse=jt,jt.options,jt.setOptions,jt.use,jt.walkTokens,jt.parseInline,Qn.parse,Zn.lex;const Zh=o=>hr(o)&&!!o.request_message,Je="__NEW_AGENT__";function Pm(o,t){const n=o.customPersonalityPrompts;if(n)switch(t){case Ae.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case Ae.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case Ae.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case Ae.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return Um[t]}const Um={[Ae.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Ae.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Ae.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Ae.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};class Xt{constructor(t,n,r,s){v(this,"_state");v(this,"_subscribers",new Set);v(this,"_focusModel",new Tf);v(this,"_onSendExchangeListeners",[]);v(this,"_onNewConversationListeners",[]);v(this,"_onHistoryDeleteListeners",[]);v(this,"_onBeforeChangeConversationListeners",[]);v(this,"_totalCharactersCacheThrottleMs",1e3);v(this,"_totalCharactersStore");v(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));v(this,"setConversation",(t,n=!0,r=!0)=>{const s=t.id!==this._state.id;s&&r&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([u,l])=>{if(l.requestId&&l.toolUseId){const{requestId:f,toolUseId:m}=zh(u);return f===l.requestId&&m===l.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",u,"but object has ",Vc(l)),[u,l]}return[u,{...l,...zh(u)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),n&&s&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const a=Xt.isEmpty(t);if(s&&a){const u=this._state.draftExchange;u&&(t.draftExchange=u)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(hr)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(u=>u(this)),this._saveConversation(this._state),s&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(u=>u())),!0});v(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});v(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});v(this,"setName",t=>{this.update({name:t})});v(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});v(this,"updateFeedback",(t,n)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:n}})});v(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Vc(t)]:t}})});v(this,"getToolUseState",(t,n)=>t===void 0||n===void 0||this.toolUseStates===void 0?{phase:Os.unknown,requestId:t??"",toolUseId:n??""}:this.toolUseStates[Vc({requestId:t,toolUseId:n})]||{phase:Os.new});v(this,"getLastToolUseState",()=>{var r,s;const t=this.lastExchange;if(!t)return{phase:Os.unknown};const n=(((r=t==null?void 0:t.structured_output_nodes)==null?void 0:r.filter(a=>a.type===zs.TOOL_USE))??[]).at(-1);return n?this.getToolUseState(t.request_id,(s=n.tool_use)==null?void 0:s.tool_use_id):{phase:Os.unknown}});v(this,"addExchange",t=>{const n=[...this._state.chatHistory,t];let r;hr(t)&&(r=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Df.unset,feedbackNote:""}}:void 0),this.update({chatHistory:n,...r?{feedbackStates:r}:{},lastUrl:void 0})});v(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});v(this,"updateExchangeById",(t,n,r=!1)=>{var l;const s=this.exchangeWithRequestId(n);if(s===null)return console.warn("No exchange with this request ID found."),!1;r&&t.response_text!==void 0&&(t.response_text=(s.response_text??"")+(t.response_text??"")),r&&(t.structured_output_nodes=[...s.structured_output_nodes??[],...t.structured_output_nodes??[]]),r&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...s.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const a=(l=(t.structured_output_nodes||[]).find(f=>f.type===zs.MAIN_TEXT_FINISHED))==null?void 0:l.content;a&&a!==t.response_text&&(t.response_text=a);let u=this._state.isShareable||Wi({...s,...t});return this.update({chatHistory:this.chatHistory.map(f=>f.request_id===n?{...f,...t}:f),isShareable:u}),!0});v(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(n=>!n.request_id||!t.has(n.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});v(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});v(this,"clearHistoryFrom",async(t,n=!0)=>{const r=this.historyFrom(t,n),s=r.map(a=>a.request_id).filter(a=>a!==void 0);this.update({chatHistory:this.historyTo(t,!n)}),this._extensionClient.clearMetadataFor({requestIds:s}),r.forEach(a=>{this._onHistoryDeleteListeners.forEach(u=>u(a))})});v(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});v(this,"historyTo",(t,n=!1)=>{const r=this.chatHistory.findIndex(s=>s.request_id===t);return r===-1?[]:this.chatHistory.slice(0,n?r+1:r)});v(this,"historyFrom",(t,n=!0)=>{const r=this.chatHistory.findIndex(s=>s.request_id===t);return r===-1?[]:this.chatHistory.slice(n?r:r+1)});v(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});v(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:Xe.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));v(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n!==t&&(!t.request_id||n.request_id!==t.request_id))})});v(this,"exchangeWithRequestId",t=>this.chatHistory.find(n=>n.request_id===t)||null);v(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});v(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(r=>r.request_id===t.request_id))return;const n={seen_state:Fs.seen};this.update({chatHistory:this.chatHistory.map(r=>r.request_id===t.request_id?{...r,...n}:r)})});v(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));v(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const n=t.filter(r=>!r.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:n}})});v(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(n=>n.id);this.update({draftActiveContextIds:t})});v(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),n=this._specialContextInputModel.recentItems.filter(s=>t.has(s.id)||s.recentFile||s.selection||s.sourceFolder),r=this._specialContextInputModel.recentItems.filter(s=>!(t.has(s.id)||s.recentFile||s.selection||s.sourceFolder));this._specialContextInputModel.markItemsActive(n.reverse()),this._specialContextInputModel.markItemsInactive(r.reverse())});v(this,"saveDraftExchange",(t,n)=>{var u,l,f;const r=t!==((u=this.draftExchange)==null?void 0:u.request_message),s=n!==((l=this.draftExchange)==null?void 0:l.rich_text_json_repr);if(!r&&!s)return;const a=(f=this.draftExchange)==null?void 0:f.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:n,mentioned_items:a,status:Xe.draft}})});v(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});v(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const n=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:n,model_id:this.selectedModelId??void 0}).then(()=>{var u,l;const r=!this.name&&this.chatHistory.length===1&&((u=this.firstExchange)==null?void 0:u.request_id)===this.chatHistory[0].request_id,s=Bi(this)&&((l=this._state.extraData)==null?void 0:l.hasAgentOnboarded)&&(a=this.chatHistory,a.filter(f=>Zh(f))).length===2;var a;this._chatFlagModel.summaryTitles&&(r||s)&&this.updateConversationTitle()}).finally(()=>{var r;Bi(this)&&this._extensionClient.reportAgentRequestEvent({eventName:jg.sentUserMessage,conversationId:this.id,requestId:((r=this.lastExchange)==null?void 0:r.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});v(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:Xe.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});v(this,"sendInstructionExchange",async(t,n)=>{let r=`temp-fe-${crypto.randomUUID()}`;const s={status:Xe.sent,request_id:r,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:Fs.unseen,timestamp:new Date().toISOString()};this.addExchange(s);for await(const a of this._extensionClient.sendInstructionMessage(s,n)){if(!this.updateExchangeById(a,r,!0))return;r=a.request_id||r}});v(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});v(this,"sendSummaryExchange",()=>{const t={status:Xe.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Rs.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});v(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const n={status:Xe.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:Fs.unseen,chatItemType:Rs.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(n);for await(const r of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(r,t,!0))return;t=r.request_id||t}});v(this,"sendExchange",async(t,n=!1)=>{var u;this.updateLastInteraction();let r=`temp-fe-${crypto.randomUUID()}`,s=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&Xt.isNew(this._state)){const l=crypto.randomUUID(),f=this._state.id;try{await this._extensionClient.migrateConversationId(f,l)}catch(m){console.error("Failed to migrate conversation checkpoints:",m)}this._state={...this._state,id:l},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(l),this._subscribers.forEach(m=>m(this))}t=Qh(t);let a={status:Xe.sent,request_id:r,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:s,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:Fs.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(a),this._loadContextFromExchange(a),this._onSendExchangeListeners.forEach(l=>l(a)),a=await this._addIdeStateNode(a),this.updateExchangeById({structured_request_nodes:a.structured_request_nodes},r,!1);for await(const l of this.sendUserMessage(r,a,n)){if(((u=this.exchangeWithRequestId(r))==null?void 0:u.status)!==Xe.sent||!this.updateExchangeById(l,r,!0))return;r=l.request_id||r}});v(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:Xe.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Yc.chatUseSuggestedQuestion)});v(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});v(this,"recoverExchange",async t=>{var s;if(!t.request_id||t.status!==Xe.sent)return;let n=t.request_id;const r=(s=t.structured_output_nodes)==null?void 0:s.filter(a=>a.type===zs.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:r??[]},n);for await(const a of this.getChatStream(t)){if(!this.updateExchangeById(a,n,!0))return;n=a.request_id||n}});v(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(n=>{hr(n)&&this._loadContextFromExchange(n)})});v(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});v(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(n=>{hr(n)&&this._unloadContextFromExchange(n)})});v(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});v(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});v(this,"_jsonToStructuredRequest",t=>{const n=[],r=a=>{var l;const u=n.at(-1);if((u==null?void 0:u.type)===On.TEXT){const f=((l=u.text_node)==null?void 0:l.content)??"",m={...u,text_node:{content:f+a}};n[n.length-1]=m}else n.push({id:n.length,type:On.TEXT,text_node:{content:a}})},s=a=>{var u,l,f,m;if(a.type==="doc"||a.type==="paragraph")for(const g of a.content??[])s(g);else if(a.type==="hardBreak")r(`
`);else if(a.type==="text")r(a.text??"");else if(a.type==="image"){if(typeof((u=a.attrs)==null?void 0:u.src)!="string")return void console.error("Image source is not a string: ",(l=a.attrs)==null?void 0:l.src);if(a.attrs.isLoading)return;const g=(f=a.attrs)==null?void 0:f.title,y=this._fileNameToImageFormat(g);n.push({id:n.length,type:On.IMAGE_ID,image_id_node:{image_id:a.attrs.src,format:y}})}else if(a.type==="mention"){const g=(m=a.attrs)==null?void 0:m.data;g&&$a(g)?n.push({id:n.length,type:On.TEXT,text_node:{content:Pm(this._chatFlagModel,g.personality.type)}}):r(`@\`${(g==null?void 0:g.name)??(g==null?void 0:g.id)}\``)}};return s(t),n});this._extensionClient=t,this._chatFlagModel=n,this._specialContextInputModel=r,this._saveConversation=s,this._state={...Xt.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return _m(()=>{let t=0;const n=this._state.chatHistory;return this._convertHistoryToExchanges(n).forEach(r=>{t+=JSON.stringify(r).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((r,s)=>r+s,0))||0)<=4?Ae.PROTOTYPER:Ae.DEFAULT}catch(n){return console.error("Error determining persona type:",n),Ae.DEFAULT}}static create(t={}){const n=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:n,lastInteractedAtIso:n,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Ae.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){var a,u;const n=this._filterToExchanges(t);let r;var s;return s=t,r=((a=s.extraData)==null?void 0:a.isAutofix)===!0?"Autofix Chat":Bi(t)?"New Agent":"New Chat",Xt.toSentenceCase(t.name||((u=n[0])==null?void 0:u.request_message)||r)}static _filterToExchanges(t){return t.chatHistory.filter(n=>hr(n))}static isNew(t){return t.id===Je}static isEmpty(t){var n;return t.chatHistory.filter(r=>hr(r)).length===0&&!((n=t.draftExchange)!=null&&n.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,n){return n==="lastMessageTimestamp"?Xt.lastMessageTimestamp(t):n==="lastInteractedAt"?Xt.lastInteractedAt(t):Xt.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){const n=this._filterToExchanges(t);if(n.length===0)return this.createdAt(t);const r=n[n.length-1];return r.timestamp?new Date(r.timestamp):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!Xt.isEmpty(t)||Xt.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(n=>n!==t)}}_notifyBeforeChangeConversation(t,n){let r=n;for(const s of this._onBeforeChangeConversationListeners){const a=s(t,r);a!==void 0&&(r=a)}return r}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return Xt.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Ae.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return Xt.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return Xt.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var r;const t=(((r=this.draftExchange)==null?void 0:r.request_message)??"").trim()!=="",n=this.hasImagesInDraft();return t||n}hasImagesInDraft(){var r;const t=(r=this.draftExchange)==null?void 0:r.rich_text_json_repr;if(!t)return!1;const n=s=>Array.isArray(s)?s.some(n):!!s&&(s.type==="image"||!(!s.content||!Array.isArray(s.content))&&s.content.some(n));return n(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){const t=Xt._filterToExchanges(this);return t.length===0?null:t[0]}get lastExchange(){const t=Xt._filterToExchanges(this);return t.length===0?null:t[t.length-1]}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>hr(t)&&t.status===Xe.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Wi(t)||Ds(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const n=[];for(const r of t)if(Wi(r))n.push(Bm(r));else if(Ds(r)&&r.fromTimestamp!==void 0&&r.toTimestamp!==void 0&&r.revertTarget){const s=Wm(r,1),a={request_message:"",response_text:"",request_id:r.request_id||crypto.randomUUID(),request_nodes:[s],response_nodes:[]};n.push(a)}return n}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===Xe.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const n=crypto.randomUUID();let r,s="";const a=await this._addIdeStateNode(Qh({...t,request_id:n,status:Xe.sent,timestamp:new Date().toISOString()}));for await(const u of this.sendUserMessage(n,a,!0))u.response_text&&(s+=u.response_text),u.request_id&&(r=u.request_id);return{responseText:s,requestId:r}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,n,r){return[]}async*sendUserMessage(t,n,r){var g;const s=this._specialContextInputModel.chatActiveContext;let a;if(n.chatHistory!==void 0)a=n.chatHistory;else{let y=this.successfulMessages;if(n.chatItemType===Rs.summaryTitle){const b=y.findIndex(k=>k.chatItemType!==Rs.agentOnboarding&&Zh(k));b!==-1&&(y=y.slice(b))}a=this._convertHistoryToExchanges(y)}let u=this.personaType;if(n.structured_request_nodes){const y=n.structured_request_nodes.find(b=>b.type===On.CHANGE_PERSONALITY);y&&y.change_personality_node&&(u=y.change_personality_node.personality_type)}const l={text:n.request_message,chatHistory:a,silent:r,modelId:n.model_id,context:s,userSpecifiedFiles:s.userSpecifiedFiles,externalSourceIds:(g=s.externalSources)==null?void 0:g.map(y=>y.id),disableRetrieval:n.disableRetrieval??!1,disableSelectedCodeDetails:n.disableSelectedCodeDetails??!1,nodes:n.structured_request_nodes,memoriesInfo:n.memoriesInfo,personaType:u,conversationId:this.id,createdTimestamp:Date.now()},f=this._createStreamStateHandlers(t,l,{flags:this._chatFlagModel}),m=this._extensionClient.startChatStreamWithRetry(t,l,{flags:this._chatFlagModel});for await(const y of m){let b=y;for(const k of f)b=k.handleChunk(b)??b;yield b}for(const y of f)yield*y.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(n=>n!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(n=>n!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(n=>n!==t)}}updateChatItem(t,n){return this.chatHistory.find(r=>r.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(r=>r.request_id===t?{...r,...n}:r)}),!0)}_fileNameToImageFormat(t){var r;switch((r=t.split(".").at(-1))==null?void 0:r.toLowerCase()){case"jpeg":case"jpg":return As.JPEG;case"png":return As.PNG;case"gif":return As.GIF;case"webp":return As.WEBP;default:return As.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let n=(t.structured_request_nodes??[]).filter(s=>s.type!==On.IDE_STATE);const r=await this._extensionClient.getChatRequestIdeState();return r?(n=[...n,{id:Hf(n)+1,type:On.IDE_STATE,ide_state_node:r}],{...t,structured_request_nodes:n}):t}}function Wm(o,t){const n=(Ds(o),o.fromTimestamp),r=(Ds(o),o.toTimestamp),s=Ds(o)&&o.revertTarget!==void 0;return{id:t,type:On.CHECKPOINT_REF,checkpoint_ref_node:{request_id:o.request_id||"",from_timestamp:n,to_timestamp:r,source:s?zg.CHECKPOINT_REVERT:void 0}}}function Bm(o){const t=(o.structured_output_nodes??[]).filter(n=>n.type===zs.RAW_RESPONSE||n.type===zs.TOOL_USE);return{request_message:o.request_message,response_text:o.response_text??"",request_id:o.request_id||"",request_nodes:o.structured_request_nodes??[],response_nodes:t}}function Hf(o){return o.length>0?Math.max(...o.map(t=>t.id)):0}function Qh(o){var t;if(o.request_message.length>0&&!((t=o.structured_request_nodes)!=null&&t.some(n=>n.type===On.TEXT))){let n=o.structured_request_nodes??[];return n=[...n,{id:Hf(n)+1,type:On.TEXT,text_node:{content:o.request_message}}],{...o,structured_request_nodes:n}}return o}class Hm{constructor(t=!0,n=setTimeout){v(this,"_notify",new Set);v(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});v(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const n=this._setTimeout;t.timeoutId=n(this._handle,t.timeout,t)});v(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});v(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=n}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,n){return this._register(t,n,!0)}interval(t,n){return this._register(t,n,!1)}at(t,n){return this._register(0,n,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,n,r,s){if(!t&&!s)return()=>{};const a={timeout:t,notify:n,once:r,date:s};return this._notify.add(a),this._schedule(a),()=>{this._clearTimeout(a),this._notify.delete(a)}}}class Vm{constructor(t=0,n=0,r=new Hm,s=ae("busy"),a=ae(!1)){v(this,"unsubNotify");v(this,"unsubMessage");v(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});v(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=n,this.idleScheduler=r,this.idleStatus=s,this.focusAfterIdle=a,this.idleNotifyTimeout=t,this.idleMessageTimeout=n}set idleMessageTimeout(t){var n;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(n=this.unsubMessage)==null||n.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var n;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(n=this.unsubNotify)==null||n.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,n;(t=this.unsubNotify)==null||t.call(this),(n=this.unsubMessage)==null||n.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var ca=(o=>(o.send="send",o.addTask="addTask",o))(ca||{});class Gm{constructor(){v(this,"_mode",ae(ca.send));v(this,"_currentMode",ca.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(ca).includes(t)&&this._mode.set(t)}}const ia=ae("idle");class Eu{constructor(t,n,r,s={}){v(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});v(this,"extensionClient");v(this,"_chatFlagsModel");v(this,"_currConversationModel");v(this,"_chatModeModel");v(this,"_sendModeModel");v(this,"_currentChatMode");v(this,"subscribers",new Set);v(this,"idleMessageModel",new Vm);v(this,"isPanelCollapsed");v(this,"agentExecutionMode");v(this,"sortConversationsBy");v(this,"displayedAnnouncements");v(this,"onLoaded",async()=>{var r,s;const t=await this.extensionClient.getChatInitData(),n=!this._chatFlagsModel.doUseNewDraftFunctionality&&t.enableBackgroundAgents;this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??kg,bigSyncThreshold:t.bigSyncThreshold??Cg,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Sg,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??yg.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:t.enableBackgroundAgents??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,clientAnnouncement:t.clientAnnouncement??""}),this._currentChatMode=t.currentChatMode,n&&this.onDoUseNewDraftFunctionalityChanged(),(s=(r=this.options).onLoaded)==null||s.call(r),this.notifySubscribers()});v(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));v(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==Uc&&this.currentConversationId!==Uc||(delete this._state.conversations[Uc],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(n=>{this.idleMessageModel.idleNotifyTimeout=n.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=n.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([n,r])=>n===Je||Xt.isValid(r))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});v(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[n,r]of Object.entries(t)){if(r.isShareable)continue;const s=r.chatHistory.some(a=>Wi(a));t[n]={...r,isShareable:s}}this._state.conversations=t});v(this,"updateChatState",t=>{this._state={...this._state,...t};const n=this._state.conversations,r=new Set;for(const[s,a]of Object.entries(n))a.isPinned&&r.add(s);this.setState(this._state),this.notifySubscribers()});v(this,"saveImmediate",()=>{this._host.setState(this._state)});v(this,"setState",tu(t=>{this._host.setState({...t,isPanelCollapsed:le(this.isPanelCollapsed),agentExecutionMode:le(this.agentExecutionMode),sortConversationsBy:le(this.sortConversationsBy),displayedAnnouncements:le(this.displayedAnnouncements),sendMode:this._sendModeModel.getCurrentMode()})},1e3,{maxWait:15e3}));v(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});v(this,"withWebviewClientEvent",(t,n)=>(...r)=>(this.extensionClient.reportWebviewClientEvent(t),n(...r)));v(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[Je];if(this.currentConversationId&&this.currentConversationId!==Je&&this._state.conversations[this.currentConversationId]&&Xt.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const n={...this._state.conversations[this.currentConversationId],id:Je};this._state.conversations[Je]=n,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=Je,this._currConversationModel.setConversation(n)}});v(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Rs.educateFeatures,request_id:crypto.randomUUID(),seen_state:Fs.seen})});v(this,"popCurrentConversation",async()=>{var n,r;const t=this.currentConversationId;t&&await this.deleteConversation(t,((n=this.nextConversation)==null?void 0:n.id)??((r=this.previousConversation)==null?void 0:r.id))});v(this,"setCurrentConversation",async(t,n=!0,r)=>{if(t===this.currentConversationId&&(r!=null&&r.noopIfSameConversation))return;let s;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=Je),s=this._state.conversations[t]??Xt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:r==null?void 0:r.newTaskUuid}),t===Je&&(s.id=Je),r!=null&&r.newTaskUuid&&(s.rootTaskUuid=r.newTaskUuid)):t===void 0?(this.deleteInvalidConversations(Bi(this._currConversationModel)?"agent":"chat"),s=Xt.create({personaType:await this._currConversationModel.decidePersonaType()})):s=this._state.conversations[t]??Xt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:r==null?void 0:r.newTaskUuid});const a=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(s,!a,n),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});v(this,"saveConversation",(t,n)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),n&&delete this._state.conversations[Je]});v(this,"isConversationShareable",t=>{var n;return((n=this._state.conversations[t])==null?void 0:n.isShareable)??!0});v(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});v(this,"getConversationUrl",async t=>{const n=this._state.conversations[t];if(n.lastUrl)return n.lastUrl;ia.set("copying");const r=n==null?void 0:n.chatHistory,s=r.reduce((l,f)=>(Wi(f)&&l.push({request_id:f.request_id||"",request_message:f.request_message,response_text:f.response_text||""}),l),[]);if(s.length===0)throw new Error("No chat history to share");const a=Xt.getDisplayName(n),u=await this.extensionClient.saveChat(t,s,a);if(u.data){let l=u.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...n,lastUrl:l}}}),l}throw new Error("Failed to create URL")});v(this,"shareConversation",async t=>{if(t!==void 0)try{const n=await this.getConversationUrl(t);if(!n)return void ia.set("idle");navigator.clipboard.writeText(n),ia.set("copied")}catch{ia.set("failed")}});v(this,"deleteConversations",async(t,n=void 0,r=[],s)=>{const a=t.length+r.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${a>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const u=new Set(t);this.deleteConversationIds(u)}if(r.length>0&&s)for(const u of r)try{await s.deleteAgent(u,!0)}catch(l){console.error(`Failed to delete remote agent ${u}:`,l)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(n)}});v(this,"deleteConversation",async(t,n=void 0)=>{await this.deleteConversations([t],n)});v(this,"deleteConversationIds",async t=>{var r;const n=[];for(const s of t){const a=((r=this._state.conversations[s])==null?void 0:r.requestIds)??[];n.push(...a)}for(const s of Object.values(this._state.conversations))if(t.has(s.id)){for(const u of s.chatHistory)hr(u)&&this.deleteImagesInExchange(u);const a=s.draftExchange;a&&this.deleteImagesInExchange(a)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([s])=>!t.has(s)))}),this.extensionClient.clearMetadataFor({requestIds:n,conversationIds:Array.from(t)})});v(this,"deleteImagesInExchange",t=>{const n=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const r of n)this.deleteImage(r)});v(this,"findImagesInJson",t=>{const n=[],r=s=>{var a;if(s.type==="image"&&((a=s.attrs)!=null&&a.src))n.push(s.attrs.src);else if((s.type==="doc"||s.type==="paragraph")&&s.content)for(const u of s.content)r(u)};return r(t),n});v(this,"findImagesInStructuredRequest",t=>t.reduce((n,r)=>(r.type===On.IMAGE_ID&&r.image_id_node&&n.push(r.image_id_node.image_id),n),[]));v(this,"toggleConversationPinned",t=>{const n=this._state.conversations[t],r={...n,isPinned:!n.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:r}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});v(this,"renameConversation",(t,n)=>{const r={...this._state.conversations[t],name:n};this.updateChatState({conversations:{...this._state.conversations,[t]:r}}),t===this.currentConversationId&&this._currConversationModel.setName(n)});v(this,"smartPaste",(t,n,r,s)=>{const a=this._currConversationModel.historyTo(t,!0).filter(u=>Wi(u)).map(u=>({request_message:u.request_message,response_text:u.response_text||"",request_id:u.request_id||""}));this.extensionClient.smartPaste({generatedCode:n,chatHistory:a,targetFile:r??void 0,options:s})});v(this,"saveImage",async t=>await this.extensionClient.saveImage(t));v(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));v(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=n,this._specialContextInputModel=r,this.options=s,this._chatFlagsModel=new wg(s.initialFlags),this.extensionClient=new $g(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new Xt(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new Gm,this.initialize(s.initialConversation);const a=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=ae(a),this._chatFlagsModel.enableAgentAutoMode?this.agentExecutionMode=ae(this._state.agentExecutionMode??"manual"):this.agentExecutionMode=ae("manual"),this.sortConversationsBy=ae(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=ae(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get currentChatMode(){return this._currentChatMode}setCurrentChatMode(t){this._currentChatMode=t,this.extensionClient.setChatMode(t)}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}orderedConversations(t,n="desc",r){const s=t||this._state.sortConversationsBy||"lastMessageTimestamp";let a=Object.values(this._state.conversations);return r&&(a=a.filter(r)),a.sort((u,l)=>{const f=Xt.getTime(u,s).getTime(),m=Xt.getTime(l,s).getTime();return n==="asc"?f-m:m-f})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),n=t.findIndex(r=>r.id===this.currentConversationId);return t.length>n+1?t[n+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),n=t.findIndex(r=>r.id===this.currentConversationId);return n>0?t[n-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const n=Object.keys(this.conversations).filter(r=>{if(r===Je)return!1;const s=!Xt.isValid(this.conversations[r]),a=Bi(this.conversations[r]);return s&&(t==="agent"&&a||t==="chat"&&!a||t==="all")});n.length&&this.deleteConversationIds(new Set(n))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const n=t.data;if(n.type===oe.newThread){if("data"in n&&n.data){const r=n.data.mode;(async()=>(await this.setCurrentConversation(),r&&this._chatModeModel?r.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):r.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",r):r&&console.warn("ChatModeModel not available, cannot set mode:",r)))()}else this.setCurrentConversation();return!0}return!1}}v(Eu,"NEW_AGENT_KEY",Je);function Kh(o,t){let n,r,s=t;const a=()=>s.editor.getModifiedEditor(),u=()=>{const{afterLineNumber:l}=s,f=a();if(l===void 0)return void f.changeViewZones(g=>{n&&f&&r&&g.removeZone(r)});const m={...s,afterLineNumber:l,domNode:o,suppressMouseDown:!0};f==null||f.changeViewZones(g=>{n&&r&&g.removeZone(r),r=g.addZone(m),n=m})};return u(),{update:l=>{s=l,u()},destroy:()=>{const l=a();l.changeViewZones(f=>{if(n&&l&&r)try{f.removeZone(r)}catch(m){if(m instanceof Error){if(m.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${m}`)}})}}}var Br=(o=>(o.edit="edit",o.instruction="instruction",o))(Br||{}),iu=(o=>(o[o.instructionDrawer=0]="instructionDrawer",o[o.chunkActionPanel=1]="chunkActionPanel",o))(iu||{});const ji=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Yh=new Set,su=typeof process=="object"&&process?process:{},Vf=(o,t,n,r)=>{typeof su.emitWarning=="function"?su.emitWarning(o,t,n,r):console.error(`[${n}] ${t}: ${o}`)};let xa=globalThis.AbortController,Xh=globalThis.AbortSignal;var wf;if(xa===void 0){Xh=class{constructor(){v(this,"onabort");v(this,"_onabort",[]);v(this,"reason");v(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},xa=class{constructor(){v(this,"signal",new Xh);t()}abort(n){var r,s;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const a of this.signal._onabort)a(n);(s=(r=this.signal).onabort)==null||s.call(r,n)}}};let o=((wf=su.env)==null?void 0:wf.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{o&&(o=!1,Vf("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const Lr=o=>o&&o===Math.floor(o)&&o>0&&isFinite(o),Gf=o=>Lr(o)?o<=Math.pow(2,8)?Uint8Array:o<=Math.pow(2,16)?Uint16Array:o<=Math.pow(2,32)?Uint32Array:o<=Number.MAX_SAFE_INTEGER?ua:null:null;class ua extends Array{constructor(t){super(t),this.fill(0)}}var Hi;const ui=class ui{constructor(t,n){v(this,"heap");v(this,"length");if(!x(ui,Hi))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(t),this.length=0}static create(t){const n=Gf(t);if(!n)return[];ut(ui,Hi,!0);const r=new ui(t,n);return ut(ui,Hi,!1),r}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};Hi=new WeakMap,Et(ui,Hi,!1);let ou=ui;var $f,kf,Sn,tn,In,Mn,Vi,Gi,_e,En,ue,Yt,bt,je,en,Le,xe,An,we,Tn,Fn,nn,Rn,Pr,qe,B,cu,li,fr,Us,rn,Zf,hi,Zi,Ws,Nr,Dr,uu,la,ha,Kt,lu,Ls,zr,hu;const Fu=class Fu{constructor(t){Et(this,B);Et(this,Sn);Et(this,tn);Et(this,In);Et(this,Mn);Et(this,Vi);Et(this,Gi);v(this,"ttl");v(this,"ttlResolution");v(this,"ttlAutopurge");v(this,"updateAgeOnGet");v(this,"updateAgeOnHas");v(this,"allowStale");v(this,"noDisposeOnSet");v(this,"noUpdateTTL");v(this,"maxEntrySize");v(this,"sizeCalculation");v(this,"noDeleteOnFetchRejection");v(this,"noDeleteOnStaleGet");v(this,"allowStaleOnFetchAbort");v(this,"allowStaleOnFetchRejection");v(this,"ignoreFetchAbort");Et(this,_e);Et(this,En);Et(this,ue);Et(this,Yt);Et(this,bt);Et(this,je);Et(this,en);Et(this,Le);Et(this,xe);Et(this,An);Et(this,we);Et(this,Tn);Et(this,Fn);Et(this,nn);Et(this,Rn);Et(this,Pr);Et(this,qe);Et(this,li,()=>{});Et(this,fr,()=>{});Et(this,Us,()=>{});Et(this,rn,()=>!1);Et(this,hi,t=>{});Et(this,Zi,(t,n,r)=>{});Et(this,Ws,(t,n,r,s)=>{if(r||s)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});v(this,$f,"LRUCache");const{max:n=0,ttl:r,ttlResolution:s=1,ttlAutopurge:a,updateAgeOnGet:u,updateAgeOnHas:l,allowStale:f,dispose:m,disposeAfter:g,noDisposeOnSet:y,noUpdateTTL:b,maxSize:k=0,maxEntrySize:T=0,sizeCalculation:j,fetchMethod:q,memoMethod:S,noDeleteOnFetchRejection:E,noDeleteOnStaleGet:H,allowStaleOnFetchRejection:ot,allowStaleOnFetchAbort:st,ignoreFetchAbort:Dt}=t;if(n!==0&&!Lr(n))throw new TypeError("max option must be a nonnegative integer");const pt=n?Gf(n):Array;if(!pt)throw new Error("invalid max value: "+n);if(ut(this,Sn,n),ut(this,tn,k),this.maxEntrySize=T||x(this,tn),this.sizeCalculation=j,this.sizeCalculation){if(!x(this,tn)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(S!==void 0&&typeof S!="function")throw new TypeError("memoMethod must be a function if defined");if(ut(this,Gi,S),q!==void 0&&typeof q!="function")throw new TypeError("fetchMethod must be a function if specified");if(ut(this,Vi,q),ut(this,Pr,!!q),ut(this,ue,new Map),ut(this,Yt,new Array(n).fill(void 0)),ut(this,bt,new Array(n).fill(void 0)),ut(this,je,new pt(n)),ut(this,en,new pt(n)),ut(this,Le,0),ut(this,xe,0),ut(this,An,ou.create(n)),ut(this,_e,0),ut(this,En,0),typeof m=="function"&&ut(this,In,m),typeof g=="function"?(ut(this,Mn,g),ut(this,we,[])):(ut(this,Mn,void 0),ut(this,we,void 0)),ut(this,Rn,!!x(this,In)),ut(this,qe,!!x(this,Mn)),this.noDisposeOnSet=!!y,this.noUpdateTTL=!!b,this.noDeleteOnFetchRejection=!!E,this.allowStaleOnFetchRejection=!!ot,this.allowStaleOnFetchAbort=!!st,this.ignoreFetchAbort=!!Dt,this.maxEntrySize!==0){if(x(this,tn)!==0&&!Lr(x(this,tn)))throw new TypeError("maxSize must be a positive integer if specified");if(!Lr(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");Y(this,B,Zf).call(this)}if(this.allowStale=!!f,this.noDeleteOnStaleGet=!!H,this.updateAgeOnGet=!!u,this.updateAgeOnHas=!!l,this.ttlResolution=Lr(s)||s===0?s:1,this.ttlAutopurge=!!a,this.ttl=r||0,this.ttl){if(!Lr(this.ttl))throw new TypeError("ttl must be a positive integer if specified");Y(this,B,cu).call(this)}if(x(this,Sn)===0&&this.ttl===0&&x(this,tn)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!x(this,Sn)&&!x(this,tn)){const St="LRU_CACHE_UNBOUNDED";(mt=>!Yh.has(mt))(St)&&(Yh.add(St),Vf("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",St,Fu))}}static unsafeExposeInternals(t){return{starts:x(t,Fn),ttls:x(t,nn),sizes:x(t,Tn),keyMap:x(t,ue),keyList:x(t,Yt),valList:x(t,bt),next:x(t,je),prev:x(t,en),get head(){return x(t,Le)},get tail(){return x(t,xe)},free:x(t,An),isBackgroundFetch:n=>{var r;return Y(r=t,B,Kt).call(r,n)},backgroundFetch:(n,r,s,a)=>{var u;return Y(u=t,B,ha).call(u,n,r,s,a)},moveToTail:n=>{var r;return Y(r=t,B,Ls).call(r,n)},indexes:n=>{var r;return Y(r=t,B,Nr).call(r,n)},rindexes:n=>{var r;return Y(r=t,B,Dr).call(r,n)},isStale:n=>{var r;return x(r=t,rn).call(r,n)}}}get max(){return x(this,Sn)}get maxSize(){return x(this,tn)}get calculatedSize(){return x(this,En)}get size(){return x(this,_e)}get fetchMethod(){return x(this,Vi)}get memoMethod(){return x(this,Gi)}get dispose(){return x(this,In)}get disposeAfter(){return x(this,Mn)}getRemainingTTL(t){return x(this,ue).has(t)?1/0:0}*entries(){for(const t of Y(this,B,Nr).call(this))x(this,bt)[t]===void 0||x(this,Yt)[t]===void 0||Y(this,B,Kt).call(this,x(this,bt)[t])||(yield[x(this,Yt)[t],x(this,bt)[t]])}*rentries(){for(const t of Y(this,B,Dr).call(this))x(this,bt)[t]===void 0||x(this,Yt)[t]===void 0||Y(this,B,Kt).call(this,x(this,bt)[t])||(yield[x(this,Yt)[t],x(this,bt)[t]])}*keys(){for(const t of Y(this,B,Nr).call(this)){const n=x(this,Yt)[t];n===void 0||Y(this,B,Kt).call(this,x(this,bt)[t])||(yield n)}}*rkeys(){for(const t of Y(this,B,Dr).call(this)){const n=x(this,Yt)[t];n===void 0||Y(this,B,Kt).call(this,x(this,bt)[t])||(yield n)}}*values(){for(const t of Y(this,B,Nr).call(this))x(this,bt)[t]===void 0||Y(this,B,Kt).call(this,x(this,bt)[t])||(yield x(this,bt)[t])}*rvalues(){for(const t of Y(this,B,Dr).call(this))x(this,bt)[t]===void 0||Y(this,B,Kt).call(this,x(this,bt)[t])||(yield x(this,bt)[t])}[(kf=Symbol.iterator,$f=Symbol.toStringTag,kf)](){return this.entries()}find(t,n={}){for(const r of Y(this,B,Nr).call(this)){const s=x(this,bt)[r],a=Y(this,B,Kt).call(this,s)?s.__staleWhileFetching:s;if(a!==void 0&&t(a,x(this,Yt)[r],this))return this.get(x(this,Yt)[r],n)}}forEach(t,n=this){for(const r of Y(this,B,Nr).call(this)){const s=x(this,bt)[r],a=Y(this,B,Kt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&t.call(n,a,x(this,Yt)[r],this)}}rforEach(t,n=this){for(const r of Y(this,B,Dr).call(this)){const s=x(this,bt)[r],a=Y(this,B,Kt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&t.call(n,a,x(this,Yt)[r],this)}}purgeStale(){let t=!1;for(const n of Y(this,B,Dr).call(this,{allowStale:!0}))x(this,rn).call(this,n)&&(Y(this,B,zr).call(this,x(this,Yt)[n],"expire"),t=!0);return t}info(t){const n=x(this,ue).get(t);if(n===void 0)return;const r=x(this,bt)[n],s=Y(this,B,Kt).call(this,r)?r.__staleWhileFetching:r;if(s===void 0)return;const a={value:s};if(x(this,nn)&&x(this,Fn)){const u=x(this,nn)[n],l=x(this,Fn)[n];if(u&&l){const f=u-(ji.now()-l);a.ttl=f,a.start=Date.now()}}return x(this,Tn)&&(a.size=x(this,Tn)[n]),a}dump(){const t=[];for(const n of Y(this,B,Nr).call(this,{allowStale:!0})){const r=x(this,Yt)[n],s=x(this,bt)[n],a=Y(this,B,Kt).call(this,s)?s.__staleWhileFetching:s;if(a===void 0||r===void 0)continue;const u={value:a};if(x(this,nn)&&x(this,Fn)){u.ttl=x(this,nn)[n];const l=ji.now()-x(this,Fn)[n];u.start=Math.floor(Date.now()-l)}x(this,Tn)&&(u.size=x(this,Tn)[n]),t.unshift([r,u])}return t}load(t){this.clear();for(const[n,r]of t){if(r.start){const s=Date.now()-r.start;r.start=ji.now()-s}this.set(n,r.value,r)}}set(t,n,r={}){var b,k,T,j,q;if(n===void 0)return this.delete(t),this;const{ttl:s=this.ttl,start:a,noDisposeOnSet:u=this.noDisposeOnSet,sizeCalculation:l=this.sizeCalculation,status:f}=r;let{noUpdateTTL:m=this.noUpdateTTL}=r;const g=x(this,Ws).call(this,t,n,r.size||0,l);if(this.maxEntrySize&&g>this.maxEntrySize)return f&&(f.set="miss",f.maxEntrySizeExceeded=!0),Y(this,B,zr).call(this,t,"set"),this;let y=x(this,_e)===0?void 0:x(this,ue).get(t);if(y===void 0)y=x(this,_e)===0?x(this,xe):x(this,An).length!==0?x(this,An).pop():x(this,_e)===x(this,Sn)?Y(this,B,la).call(this,!1):x(this,_e),x(this,Yt)[y]=t,x(this,bt)[y]=n,x(this,ue).set(t,y),x(this,je)[x(this,xe)]=y,x(this,en)[y]=x(this,xe),ut(this,xe,y),ea(this,_e)._++,x(this,Zi).call(this,y,g,f),f&&(f.set="add"),m=!1;else{Y(this,B,Ls).call(this,y);const S=x(this,bt)[y];if(n!==S){if(x(this,Pr)&&Y(this,B,Kt).call(this,S)){S.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:E}=S;E===void 0||u||(x(this,Rn)&&((b=x(this,In))==null||b.call(this,E,t,"set")),x(this,qe)&&((k=x(this,we))==null||k.push([E,t,"set"])))}else u||(x(this,Rn)&&((T=x(this,In))==null||T.call(this,S,t,"set")),x(this,qe)&&((j=x(this,we))==null||j.push([S,t,"set"])));if(x(this,hi).call(this,y),x(this,Zi).call(this,y,g,f),x(this,bt)[y]=n,f){f.set="replace";const E=S&&Y(this,B,Kt).call(this,S)?S.__staleWhileFetching:S;E!==void 0&&(f.oldValue=E)}}else f&&(f.set="update")}if(s===0||x(this,nn)||Y(this,B,cu).call(this),x(this,nn)&&(m||x(this,Us).call(this,y,s,a),f&&x(this,fr).call(this,f,y)),!u&&x(this,qe)&&x(this,we)){const S=x(this,we);let E;for(;E=S==null?void 0:S.shift();)(q=x(this,Mn))==null||q.call(this,...E)}return this}pop(){var t;try{for(;x(this,_e);){const n=x(this,bt)[x(this,Le)];if(Y(this,B,la).call(this,!0),Y(this,B,Kt).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(x(this,qe)&&x(this,we)){const n=x(this,we);let r;for(;r=n==null?void 0:n.shift();)(t=x(this,Mn))==null||t.call(this,...r)}}}has(t,n={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:s}=n,a=x(this,ue).get(t);if(a!==void 0){const u=x(this,bt)[a];if(Y(this,B,Kt).call(this,u)&&u.__staleWhileFetching===void 0)return!1;if(!x(this,rn).call(this,a))return r&&x(this,li).call(this,a),s&&(s.has="hit",x(this,fr).call(this,s,a)),!0;s&&(s.has="stale",x(this,fr).call(this,s,a))}else s&&(s.has="miss");return!1}peek(t,n={}){const{allowStale:r=this.allowStale}=n,s=x(this,ue).get(t);if(s===void 0||!r&&x(this,rn).call(this,s))return;const a=x(this,bt)[s];return Y(this,B,Kt).call(this,a)?a.__staleWhileFetching:a}async fetch(t,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,ttl:u=this.ttl,noDisposeOnSet:l=this.noDisposeOnSet,size:f=0,sizeCalculation:m=this.sizeCalculation,noUpdateTTL:g=this.noUpdateTTL,noDeleteOnFetchRejection:y=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:b=this.allowStaleOnFetchRejection,ignoreFetchAbort:k=this.ignoreFetchAbort,allowStaleOnFetchAbort:T=this.allowStaleOnFetchAbort,context:j,forceRefresh:q=!1,status:S,signal:E}=n;if(!x(this,Pr))return S&&(S.fetch="get"),this.get(t,{allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,status:S});const H={allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,ttl:u,noDisposeOnSet:l,size:f,sizeCalculation:m,noUpdateTTL:g,noDeleteOnFetchRejection:y,allowStaleOnFetchRejection:b,allowStaleOnFetchAbort:T,ignoreFetchAbort:k,status:S,signal:E};let ot=x(this,ue).get(t);if(ot===void 0){S&&(S.fetch="miss");const st=Y(this,B,ha).call(this,t,ot,H,j);return st.__returned=st}{const st=x(this,bt)[ot];if(Y(this,B,Kt).call(this,st)){const mt=r&&st.__staleWhileFetching!==void 0;return S&&(S.fetch="inflight",mt&&(S.returnedStale=!0)),mt?st.__staleWhileFetching:st.__returned=st}const Dt=x(this,rn).call(this,ot);if(!q&&!Dt)return S&&(S.fetch="hit"),Y(this,B,Ls).call(this,ot),s&&x(this,li).call(this,ot),S&&x(this,fr).call(this,S,ot),st;const pt=Y(this,B,ha).call(this,t,ot,H,j),St=pt.__staleWhileFetching!==void 0&&r;return S&&(S.fetch=Dt?"stale":"refresh",St&&Dt&&(S.returnedStale=!0)),St?pt.__staleWhileFetching:pt.__returned=pt}}async forceFetch(t,n={}){const r=await this.fetch(t,n);if(r===void 0)throw new Error("fetch() returned undefined");return r}memo(t,n={}){const r=x(this,Gi);if(!r)throw new Error("no memoMethod provided to constructor");const{context:s,forceRefresh:a,...u}=n,l=this.get(t,u);if(!a&&l!==void 0)return l;const f=r(t,l,{options:u,context:s});return this.set(t,f,u),f}get(t,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,status:u}=n,l=x(this,ue).get(t);if(l!==void 0){const f=x(this,bt)[l],m=Y(this,B,Kt).call(this,f);return u&&x(this,fr).call(this,u,l),x(this,rn).call(this,l)?(u&&(u.get="stale"),m?(u&&r&&f.__staleWhileFetching!==void 0&&(u.returnedStale=!0),r?f.__staleWhileFetching:void 0):(a||Y(this,B,zr).call(this,t,"expire"),u&&r&&(u.returnedStale=!0),r?f:void 0)):(u&&(u.get="hit"),m?f.__staleWhileFetching:(Y(this,B,Ls).call(this,l),s&&x(this,li).call(this,l),f))}u&&(u.get="miss")}delete(t){return Y(this,B,zr).call(this,t,"delete")}clear(){return Y(this,B,hu).call(this,"delete")}};Sn=new WeakMap,tn=new WeakMap,In=new WeakMap,Mn=new WeakMap,Vi=new WeakMap,Gi=new WeakMap,_e=new WeakMap,En=new WeakMap,ue=new WeakMap,Yt=new WeakMap,bt=new WeakMap,je=new WeakMap,en=new WeakMap,Le=new WeakMap,xe=new WeakMap,An=new WeakMap,we=new WeakMap,Tn=new WeakMap,Fn=new WeakMap,nn=new WeakMap,Rn=new WeakMap,Pr=new WeakMap,qe=new WeakMap,B=new WeakSet,cu=function(){const t=new ua(x(this,Sn)),n=new ua(x(this,Sn));ut(this,nn,t),ut(this,Fn,n),ut(this,Us,(a,u,l=ji.now())=>{if(n[a]=u!==0?l:0,t[a]=u,u!==0&&this.ttlAutopurge){const f=setTimeout(()=>{x(this,rn).call(this,a)&&Y(this,B,zr).call(this,x(this,Yt)[a],"expire")},u+1);f.unref&&f.unref()}}),ut(this,li,a=>{n[a]=t[a]!==0?ji.now():0}),ut(this,fr,(a,u)=>{if(t[u]){const l=t[u],f=n[u];if(!l||!f)return;a.ttl=l,a.start=f,a.now=r||s();const m=a.now-f;a.remainingTTL=l-m}});let r=0;const s=()=>{const a=ji.now();if(this.ttlResolution>0){r=a;const u=setTimeout(()=>r=0,this.ttlResolution);u.unref&&u.unref()}return a};this.getRemainingTTL=a=>{const u=x(this,ue).get(a);if(u===void 0)return 0;const l=t[u],f=n[u];return!l||!f?1/0:l-((r||s())-f)},ut(this,rn,a=>{const u=n[a],l=t[a];return!!l&&!!u&&(r||s())-u>l})},li=new WeakMap,fr=new WeakMap,Us=new WeakMap,rn=new WeakMap,Zf=function(){const t=new ua(x(this,Sn));ut(this,En,0),ut(this,Tn,t),ut(this,hi,n=>{ut(this,En,x(this,En)-t[n]),t[n]=0}),ut(this,Ws,(n,r,s,a)=>{if(Y(this,B,Kt).call(this,r))return 0;if(!Lr(s)){if(!a)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof a!="function")throw new TypeError("sizeCalculation must be a function");if(s=a(r,n),!Lr(s))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return s}),ut(this,Zi,(n,r,s)=>{if(t[n]=r,x(this,tn)){const a=x(this,tn)-t[n];for(;x(this,En)>a;)Y(this,B,la).call(this,!0)}ut(this,En,x(this,En)+t[n]),s&&(s.entrySize=r,s.totalCalculatedSize=x(this,En))})},hi=new WeakMap,Zi=new WeakMap,Ws=new WeakMap,Nr=function*({allowStale:t=this.allowStale}={}){if(x(this,_e))for(let n=x(this,xe);Y(this,B,uu).call(this,n)&&(!t&&x(this,rn).call(this,n)||(yield n),n!==x(this,Le));)n=x(this,en)[n]},Dr=function*({allowStale:t=this.allowStale}={}){if(x(this,_e))for(let n=x(this,Le);Y(this,B,uu).call(this,n)&&(!t&&x(this,rn).call(this,n)||(yield n),n!==x(this,xe));)n=x(this,je)[n]},uu=function(t){return t!==void 0&&x(this,ue).get(x(this,Yt)[t])===t},la=function(t){var a,u;const n=x(this,Le),r=x(this,Yt)[n],s=x(this,bt)[n];return x(this,Pr)&&Y(this,B,Kt).call(this,s)?s.__abortController.abort(new Error("evicted")):(x(this,Rn)||x(this,qe))&&(x(this,Rn)&&((a=x(this,In))==null||a.call(this,s,r,"evict")),x(this,qe)&&((u=x(this,we))==null||u.push([s,r,"evict"]))),x(this,hi).call(this,n),t&&(x(this,Yt)[n]=void 0,x(this,bt)[n]=void 0,x(this,An).push(n)),x(this,_e)===1?(ut(this,Le,ut(this,xe,0)),x(this,An).length=0):ut(this,Le,x(this,je)[n]),x(this,ue).delete(r),ea(this,_e)._--,n},ha=function(t,n,r,s){const a=n===void 0?void 0:x(this,bt)[n];if(Y(this,B,Kt).call(this,a))return a;const u=new xa,{signal:l}=r;l==null||l.addEventListener("abort",()=>u.abort(l.reason),{signal:u.signal});const f={signal:u.signal,options:r,context:s},m=(k,T=!1)=>{const{aborted:j}=u.signal,q=r.ignoreFetchAbort&&k!==void 0;if(r.status&&(j&&!T?(r.status.fetchAborted=!0,r.status.fetchError=u.signal.reason,q&&(r.status.fetchAbortIgnored=!0)):r.status.fetchResolved=!0),j&&!q&&!T)return g(u.signal.reason);const S=y;return x(this,bt)[n]===y&&(k===void 0?S.__staleWhileFetching?x(this,bt)[n]=S.__staleWhileFetching:Y(this,B,zr).call(this,t,"fetch"):(r.status&&(r.status.fetchUpdated=!0),this.set(t,k,f.options))),k},g=k=>{const{aborted:T}=u.signal,j=T&&r.allowStaleOnFetchAbort,q=j||r.allowStaleOnFetchRejection,S=q||r.noDeleteOnFetchRejection,E=y;if(x(this,bt)[n]===y&&(!S||E.__staleWhileFetching===void 0?Y(this,B,zr).call(this,t,"fetch"):j||(x(this,bt)[n]=E.__staleWhileFetching)),q)return r.status&&E.__staleWhileFetching!==void 0&&(r.status.returnedStale=!0),E.__staleWhileFetching;if(E.__returned===E)throw k};r.status&&(r.status.fetchDispatched=!0);const y=new Promise((k,T)=>{var q;const j=(q=x(this,Vi))==null?void 0:q.call(this,t,a,f);j&&j instanceof Promise&&j.then(S=>k(S===void 0?void 0:S),T),u.signal.addEventListener("abort",()=>{r.ignoreFetchAbort&&!r.allowStaleOnFetchAbort||(k(void 0),r.allowStaleOnFetchAbort&&(k=S=>m(S,!0)))})}).then(m,k=>(r.status&&(r.status.fetchRejected=!0,r.status.fetchError=k),g(k))),b=Object.assign(y,{__abortController:u,__staleWhileFetching:a,__returned:void 0});return n===void 0?(this.set(t,b,{...f.options,status:void 0}),n=x(this,ue).get(t)):x(this,bt)[n]=b,b},Kt=function(t){if(!x(this,Pr))return!1;const n=t;return!!n&&n instanceof Promise&&n.hasOwnProperty("__staleWhileFetching")&&n.__abortController instanceof xa},lu=function(t,n){x(this,en)[n]=t,x(this,je)[t]=n},Ls=function(t){t!==x(this,xe)&&(t===x(this,Le)?ut(this,Le,x(this,je)[t]):Y(this,B,lu).call(this,x(this,en)[t],x(this,je)[t]),Y(this,B,lu).call(this,x(this,xe),t),ut(this,xe,t))},zr=function(t,n){var s,a,u,l;let r=!1;if(x(this,_e)!==0){const f=x(this,ue).get(t);if(f!==void 0)if(r=!0,x(this,_e)===1)Y(this,B,hu).call(this,n);else{x(this,hi).call(this,f);const m=x(this,bt)[f];if(Y(this,B,Kt).call(this,m)?m.__abortController.abort(new Error("deleted")):(x(this,Rn)||x(this,qe))&&(x(this,Rn)&&((s=x(this,In))==null||s.call(this,m,t,n)),x(this,qe)&&((a=x(this,we))==null||a.push([m,t,n]))),x(this,ue).delete(t),x(this,Yt)[f]=void 0,x(this,bt)[f]=void 0,f===x(this,xe))ut(this,xe,x(this,en)[f]);else if(f===x(this,Le))ut(this,Le,x(this,je)[f]);else{const g=x(this,en)[f];x(this,je)[g]=x(this,je)[f];const y=x(this,je)[f];x(this,en)[y]=x(this,en)[f]}ea(this,_e)._--,x(this,An).push(f)}}if(x(this,qe)&&((u=x(this,we))!=null&&u.length)){const f=x(this,we);let m;for(;m=f==null?void 0:f.shift();)(l=x(this,Mn))==null||l.call(this,...m)}return r},hu=function(t){var n,r,s;for(const a of Y(this,B,Dr).call(this,{allowStale:!0})){const u=x(this,bt)[a];if(Y(this,B,Kt).call(this,u))u.__abortController.abort(new Error("deleted"));else{const l=x(this,Yt)[a];x(this,Rn)&&((n=x(this,In))==null||n.call(this,u,l,t)),x(this,qe)&&((r=x(this,we))==null||r.push([u,l,t]))}}if(x(this,ue).clear(),x(this,bt).fill(void 0),x(this,Yt).fill(void 0),x(this,nn)&&x(this,Fn)&&(x(this,nn).fill(0),x(this,Fn).fill(0)),x(this,Tn)&&x(this,Tn).fill(0),ut(this,Le,0),ut(this,xe,0),x(this,An).length=0,ut(this,En,0),ut(this,_e,0),x(this,qe)&&x(this,we)){const a=x(this,we);let u;for(;u=a==null?void 0:a.shift();)(s=x(this,Mn))==null||s.call(this,...u)}};let au=Fu;class Qf{constructor(){v(this,"_syncStatus",{status:Dg.done,foldersProgress:[]});v(this,"_syncEnabledState",Fh.initializing);v(this,"_workspaceGuidelines",[]);v(this,"_openUserGuidelinesInput",!1);v(this,"_userGuidelines");v(this,"_contextStore",new Zm);v(this,"_prevOpenFiles",[]);v(this,"_disableContext",!1);v(this,"_enableAgentMemories",!1);v(this,"subscribers",new Set);v(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));v(this,"handleMessageFromExtension",t=>{const n=t.data;switch(n.type){case oe.sourceFoldersUpdated:this.onSourceFoldersUpdated(n.data.sourceFolders);break;case oe.sourceFoldersSyncStatus:this.onSyncStatusUpdated(n.data);break;case oe.fileRangesSelected:this.updateSelections(n.data);break;case oe.currentlyOpenFiles:this.setCurrentlyOpenFiles(n.data);break;case oe.syncEnabledState:this.onSyncEnabledStateUpdate(n.data);break;case oe.updateGuidelinesState:this.onGuidelinesStateUpdate(n.data);break;default:return!1}return!0});v(this,"onSourceFoldersUpdated",t=>{const n=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(r=>({sourceFolder:r,status:ve.active,label:r.folderRoot,showWarning:r.guidelinesOverLimit,id:r.folderRoot+String(r.guidelinesEnabled)+String(r.guidelinesOverLimit)})),n,r=>r.id),this.notifySubscribers()});v(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});v(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});v(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});v(this,"addFile",t=>{this.addFiles([t])});v(this,"addFiles",t=>{this.updateFiles(t,[])});v(this,"removeFile",t=>{this.removeFiles([t])});v(this,"removeFiles",t=>{this.updateFiles([],t)});v(this,"updateItems",(t,n)=>{this.updateItemsInplace(t,n),this.notifySubscribers()});v(this,"updateItemsInplace",(t,n)=>{this._contextStore.update(t,n,r=>r.id)});v(this,"updateFiles",(t,n)=>{const r=u=>({file:u,...Wc(u)}),s=t.map(r),a=n.map(r);this._contextStore.update(s,a,u=>u.id),this.notifySubscribers()});v(this,"updateRules",(t,n)=>{const r=u=>({rule:u,...Mg(u)}),s=t.map(r),a=n.map(r);this._contextStore.update(s,a,u=>u.id),this.notifySubscribers()});v(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});v(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});v(this,"setCurrentlyOpenFiles",t=>{const n=t.map(s=>({recentFile:s,...Wc(s)})),r=this._prevOpenFiles;this._prevOpenFiles=n,this._contextStore.update(n,r,s=>s.id),r.forEach(s=>{const a=this._contextStore.peekKey(s.id);a!=null&&a.recentFile&&(a.file=a.recentFile,delete a.recentFile)}),n.forEach(s=>{const a=this._contextStore.peekKey(s.id);a!=null&&a.file&&(a.recentFile=a.file,delete a.file)}),this.notifySubscribers()});v(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});v(this,"updateUserGuidelines",t=>{const n=this.userGuidelines,r={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:ve.active,referenceCount:1,showWarning:t.overLimit};this._contextStore.update([r],n,s=>{var a,u;return s.id+String((a=s.userGuidelines)==null?void 0:a.enabled)+String((u=s.userGuidelines)==null?void 0:u.overLimit)}),this.notifySubscribers()});v(this,"onGuidelinesStateUpdate",t=>{this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const n=t.userGuidelines;n&&this.updateUserGuidelines(n),this.onSourceFoldersUpdated(this.sourceFolders.map(r=>r.sourceFolder))});v(this,"updateSourceFoldersWithGuidelines",t=>t.map(n=>{const r=this._workspaceGuidelines.find(s=>s.workspaceFolder===n.folderRoot);return{...n,guidelinesEnabled:(r==null?void 0:r.enabled)??!1,guidelinesOverLimit:(r==null?void 0:r.overLimit)??!1,guidelinesLengthLimit:(r==null?void 0:r.lengthLimit)??2e3}}));v(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});v(this,"updateExternalSources",(t,n)=>{this._contextStore.update(t,n,r=>r.id),this.notifySubscribers()});v(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});v(this,"updateSelections",t=>{const n=this._contextStore.values.filter(pa);this._contextStore.update(t.map(r=>({selection:r,...Wc(r)})),n,r=>r.id),this.notifySubscribers()});v(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const n=this.recentActiveItems[0];return this.markInactive(n),!0}return!1});v(this,"markInactive",t=>{this.markItemsInactive([t])});v(this,"markItemsInactive",t=>{t.forEach(n=>{this._contextStore.setStatus(n.id,ve.inactive)}),this.notifySubscribers()});v(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});v(this,"markActive",t=>{this.markItemsActive([t])});v(this,"markItemsActive",t=>{t.forEach(n=>{this._contextStore.setStatus(n.id,ve.active)}),this.notifySubscribers()});v(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});v(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});v(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});v(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>bu(t)&&!da(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(da)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(pa)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(xu)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(ga)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(wu)}get userGuidelines(){return this._contextStore.values.filter(ma)}get agentMemories(){return[{...Ig,status:this._enableAgentMemories?ve.active:ve.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>_a(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===ve.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===ve.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===ve.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===ve.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===ve.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===ve.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var f;if(this.syncEnabledState===Fh.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(m=>m.progress!==void 0);if(t.length===0)return;const n=t.reduce((m,g)=>{var y;return m+(((y=g==null?void 0:g.progress)==null?void 0:y.trackedFiles)??0)},0),r=t.reduce((m,g)=>{var y;return m+(((y=g==null?void 0:g.progress)==null?void 0:y.backlogSize)??0)},0),s=Math.max(n,0),a=Math.min(Math.max(r,0),s),u=s-a,l=[];for(const m of t)(f=m==null?void 0:m.progress)!=null&&f.newlyTracked&&l.push(m.folderRoot);return{status:this._syncStatus.status,totalFiles:s,syncedCount:u,backlogSize:a,newlyTrackedFolders:l}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(ga(t)||ma(t)||$a(t)||_a(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===ve.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===ve.inactive)}get isContextDisabled(){return this._disableContext}}class Zm{constructor(){v(this,"_cache",new au({max:1e3}));v(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));v(this,"clear",()=>{this._cache.clear()});v(this,"update",(t,n,r)=>{t.forEach(s=>this.addInPlace(s,r)),n.forEach(s=>this.removeInPlace(s,r))});v(this,"removeFromStore",(t,n)=>{const r=n(t);this._cache.delete(r)});v(this,"addInPlace",(t,n)=>{const r=n(t),s=t.referenceCount??1,a=this._cache.get(r),u=t.status??(a==null?void 0:a.status)??ve.active;a?(a.referenceCount+=s,a.status=u,a.pinned=t.pinned??a.pinned,a.showWarning=t.showWarning??a.showWarning):this._cache.set(r,{...t,pinned:void 0,referenceCount:s,status:u})});v(this,"removeInPlace",(t,n)=>{const r=n(t),s=this._cache.get(r);s&&(s.referenceCount-=1,s.referenceCount===0&&this._cache.delete(r))});v(this,"setStatus",(t,n)=>{const r=this._cache.get(t);r&&(r.status=n)});v(this,"togglePinned",t=>{const n=this._cache.peek(t);n&&(n.pinned?this.unpin(t):this.pin(t))});v(this,"pin",t=>{const n=this._cache.peek(t);n&&!n.pinned&&(n.pinned=!0,n.referenceCount+=1)});v(this,"unpin",t=>{const n=this._cache.peek(t);n&&n.pinned&&(n.pinned=!1,n.referenceCount-=1,n.referenceCount===0&&this._cache.delete(t))});v(this,"toggleStatus",t=>{const n=this._cache.get(t);n&&(n.status=n.status===ve.active?ve.inactive:ve.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Qm{constructor(t,n,r){v(this,"_originalModel");v(this,"_modifiedModel");v(this,"_fullEdits",[]);v(this,"_currEdit");v(this,"_currOriginalEdit");v(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(n=>{this._modifiedModel.applyEdits([n])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});v(this,"finish",()=>this._completeCurrEdit());v(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);v(this,"_completeCurrEdit",t=>{const n={resetOriginal:[],original:[],modified:[]};if(!t)return n;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const r=this._nextModifiedInsertPosition(),s=t.stagedEndLine-t.stagedStartLine,a={range:new this._monaco.Range(r.lineNumber,0,r.lineNumber+s,0),text:""};n.modified.push(a),this._modifiedModel.applyEdits([a]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return n});v(this,"_startNewEdit",t=>{const n={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},n.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),n});v(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const n=this._nextModifiedInsertPosition(),r={...this._currEdit,text:t.newText,range:new this._monaco.Range(n.lineNumber,n.column,n.lineNumber,n.column)};return this._modifiedModel.applyEdits([r]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[r]:[]}});v(this,"_nextModifiedInsertPosition",()=>{var n;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((n=this._currEdit.text)==null?void 0:n.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=n,this._monaco=r,this._originalModel=this._monaco.editor.createModel(n),this._modifiedModel=this._monaco.editor.createModel(n)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class Km{constructor(t,n,r){v(this,"_asyncMsgSender");v(this,"_editor");v(this,"_chatModel");v(this,"_focusModel",new Tf);v(this,"_hasScrolledOnInit",!1);v(this,"_markHasScrolledOnInit",tu(()=>{this._hasScrolledOnInit=!0},200));v(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});v(this,"_subscribers",new Set);v(this,"_disposables",[]);v(this,"_rootChunk");v(this,"_keybindings",ae({}));v(this,"_requestId",ae(void 0));v(this,"requestId",this._requestId);v(this,"_disableResolution",ae(!1));v(this,"disableResolution",Ah(this._disableResolution));v(this,"_disableApply",ae(!1));v(this,"disableApply",Ah(this._disableApply));v(this,"_currStream");v(this,"_isLoadingDiffChunks",ae(!1));v(this,"_selectionLines",ae(void 0));v(this,"_mode",ae(Br.edit));v(this,"initializeEditor",t=>{var n,r,s,a,u,l,f,m,g,y,b,k;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new Eu(new Rf(Ns),Ns,new Qf),(r=(n=this._monaco.editor).registerCommand)==null||r.call(n,"acceptFocusedChunk",this.acceptFocusedChunk),(a=(s=this._monaco.editor).registerCommand)==null||a.call(s,"rejectFocusedChunk",this.rejectFocusedChunk),(l=(u=this._monaco.editor).registerCommand)==null||l.call(u,"acceptAllChunks",this.acceptAllChunks),(m=(f=this._monaco.editor).registerCommand)==null||m.call(f,"rejectAllChunks",this.rejectAllChunks),(y=(g=this._monaco.editor).registerCommand)==null||y.call(g,"focusNextChunk",this.focusNextChunk),(k=(b=this._monaco.editor).registerCommand)==null||k.call(b,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(T=>this.notifySubscribers())}),this.initialize()});v(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));v(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});v(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});v(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const n=this.leaves[0];this.revealChunk(n)}this.notifyDiffViewUpdated(),this.notifySubscribers()});v(this,"onMouseMoveModified",t=>{var s,a,u,l,f,m;if(((s=t.target.position)==null?void 0:s.lineNumber)===void 0||this.leaves===void 0)return;const n=this.editorOffset,r=(a=t.target.position)==null?void 0:a.lineNumber;for(let g=0;g<this.leaves.length;g++){const y=this.leaves[g],b=(u=y.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].modifiedStart,k=(l=y.unitOfCodeWork.lineChanges)==null?void 0:l.lineChanges[0].modifiedEnd,T=(f=y.unitOfCodeWork.lineChanges)==null?void 0:f.lineChanges[0].originalStart,j=(m=y.unitOfCodeWork.lineChanges)==null?void 0:m.lineChanges[0].originalEnd;if(b!==void 0&&k!==void 0&&T!==void 0&&j!==void 0){if(b!==k||r!==b){if(b<=r&&r<k){this.setCurrFocusedChunkIdx(g,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const q=this._editor.getOriginalEditor(),S=q.getOption(this._monaco.editor.EditorOption.lineHeight),E=q.getScrolledVisiblePosition({lineNumber:T,column:0}),H=q.getScrolledVisiblePosition({lineNumber:j+1,column:0});if(E===null||H===null)continue;const ot=E.top-S/2+n,st=H.top-S/2+n;if(t.event.posy>=ot&&t.event.posy<=st){this.setCurrFocusedChunkIdx(g,!1);break}break}}}});v(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:oe.diffViewWindowFocusChange,data:t})});v(this,"setCurrFocusedChunkIdx",(t,n=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),n&&this.revealCurrFocusedChunk(),this.notifySubscribers())});v(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});v(this,"revealChunk",t=>{var s;const n=(s=t.unitOfCodeWork.lineChanges)==null?void 0:s.lineChanges[0],r=n==null?void 0:n.modifiedStart;r!==void 0&&this._editor.revealLineNearTop(r-1)});v(this,"renderCentralOverlayWidget",t=>{const n=()=>({editor:this._editor,id:"central-overlay-widget"}),r=function(s,a,u){let l,f=a;const m=()=>f.editor.getModifiedEditor(),g=()=>{const y=m();if(!y)return;const b={getDomNode:()=>s,getId:()=>f.id,getPosition:()=>({preference:u.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER})};l&&y.removeOverlayWidget(l),y.addOverlayWidget(b),l=b};return g(),{update:y=>{f=y,g()},destroy:()=>{const y=m();y&&l&&y.removeOverlayWidget(l)}}}(t,n(),{monaco:this._monaco});return{update:()=>{r.update(n())},destroy:r.destroy}});v(this,"renderInstructionsDrawerViewZone",(t,n)=>{let r=!1,s=n;const a=n.autoFocus??!0,u=g=>{a&&!r&&(this._editor.revealLineNearTop(g),r=!0)},l=g=>({...g,ordinal:iu.instructionDrawer,editor:this._editor,afterLineNumber:g.line}),f=Kh(t,l(n)),m=[];return a&&m.push(this._editor.onDidUpdateDiff(()=>{u(s.line)})),{update:g=>{const y={...s,...g};mm(y,s)||(f.update(l(y)),s=y,u(y.line))},destroy:()=>{f.destroy(),m.forEach(g=>g.dispose())}}});v(this,"renderActionsViewZone",(t,n)=>{const r=a=>{var l;let u;return u=a.chunk?(l=a.chunk.unitOfCodeWork.lineChanges)==null?void 0:l.lineChanges[0].modifiedStart:1,{...a,ordinal:iu.chunkActionPanel,editor:this._editor,afterLineNumber:u?u-1:void 0}},s=Kh(t,r(n));return{update:a=>{s.update(r(a))},destroy:s.destroy}});v(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});v(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});v(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});v(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});v(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});v(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});v(this,"initialize",async()=>{var f;const t=await this._asyncMsgSender.send({type:oe.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:n,instruction:r,keybindings:s,editable:a}=t.data;this._editor.updateOptions({readOnly:!a});const u=le(this._keybindings);this._keybindings.set(s??u);const l=r==null?void 0:r.selection;l&&(l.start.line===l.end.line&&l.start.character===l.end.character&&this._mode.set(Br.instruction),le(this.selectionLines)===void 0&&this._selectionLines.set({start:l.start.line,end:l.end.line})),this.updateModels(n.originalCode??"",n.modifiedCode??"",{rootPath:n.repoRoot,relPath:n.pathName}),(f=this._currStream)==null||f.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});v(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:oe.disposeDiffView})});v(this,"_tryFetchStream",async()=>{var n,r,s;const t=this._asyncMsgSender.stream({type:oe.diffViewFetchPendingStream},15e3,6e4);for await(const a of t)switch(a.type){case oe.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(a.data.requestId);const u=this._editor.getOriginalEditor().getValue();this._currStream=new Qm(a.data.streamId,u,this._monaco),this._syncStreamToModels();break}case oe.diffViewDiffStreamEnded:if(((n=this._currStream)==null?void 0:n.id)!==a.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case oe.diffViewDiffStreamChunk:{if(((r=this._currStream)==null?void 0:r.id)!==a.data.streamId)return;const u=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!u)return this.setLoading(!1),void this._cleanupStream();const l=(s=this._currStream)==null?void 0:s.onReceiveChunk(a);l&&(this._applyDeltaDiff(l),le(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});v(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case oe.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case oe.diffViewAcceptAllChunks:this.acceptAllChunks();break;case oe.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case oe.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case oe.diffViewFocusPrevChunk:this.focusPrevChunk();break;case oe.diffViewFocusNextChunk:this.focusNextChunk()}});v(this,"_applyDeltaDiff",t=>{const n=this._editor.getOriginalEditor().getModel(),r=this._editor.getModifiedEditor().getModel();n&&r&&(n.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(s=>{n.pushEditOperations([],[s],()=>[])}),t.modified.forEach(s=>{r.pushEditOperations([],[s],()=>[])}))});v(this,"_cleanupStream",()=>{var t;if(this._currStream){const n=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(n),this._currStream=void 0,this._resetScrollOnInit()}});v(this,"_syncStreamToModels",()=>{var r,s;const t=(r=this._currStream)==null?void 0:r.originalValue,n=(s=this._currStream)==null?void 0:s.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),n&&n!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(n)});v(this,"acceptChunk",async t=>{le(this._disableApply)||this.acceptChunks([t])});v(this,"acceptChunks",async(t,n=!1)=>{le(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,Pc.accept,n),await Kc(),this.areModelsEqual()&&!le(this.isLoading)&&this.disposeDiffViewPanel())});v(this,"areModelsEqual",()=>{var r,s;const t=(r=this._editor.getModel())==null?void 0:r.original,n=(s=this._editor.getModel())==null?void 0:s.modified;return(t==null?void 0:t.getValue())===(n==null?void 0:n.getValue())});v(this,"rejectChunk",async t=>{this.rejectChunks([t])});v(this,"rejectChunks",async(t,n=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,Pc.reject,n),await Kc(),this.areModelsEqual()&&!le(this.isLoading)&&this.disposeDiffViewPanel()});v(this,"notifyDiffViewUpdated",tu(()=>{this.notifyResolvedChunks([],Pc.accept)},1e3));v(this,"notifyResolvedChunks",async(t,n,r=!1)=>{var a;const s=(a=this._editor.getModel())==null?void 0:a.original.uri.path;s&&await this._asyncMsgSender.send({type:oe.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:s,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(u=>u.unitOfCodeWork),resolveType:n,shouldApplyToAll:r}},2e3)});v(this,"executeDiffChunks",(t,n)=>{var g,y,b;if(le(this._disableResolution)||n&&le(this._disableApply))return;const r=(g=this._editor.getModel())==null?void 0:g.original,s=(y=this._editor.getModel())==null?void 0:y.modified;if(!r||!s||this._currStream!==void 0)return;const a=[],u=[];for(const k of t){const T=(b=k.unitOfCodeWork.lineChanges)==null?void 0:b.lineChanges[0];if(!T||k.unitOfCodeWork.originalCode===void 0||k.unitOfCodeWork.modifiedCode===void 0)continue;let j={startLineNumber:T.originalStart,startColumn:1,endLineNumber:T.originalEnd,endColumn:1},q={startLineNumber:T.modifiedStart,startColumn:1,endLineNumber:T.modifiedEnd,endColumn:1};const S=n?k.unitOfCodeWork.modifiedCode:k.unitOfCodeWork.originalCode;S!==void 0&&(a.push({range:j,text:S}),u.push({range:q,text:S}))}r.pushEditOperations([],a,()=>[]),s.pushEditOperations([],u,()=>[]);const l=this._focusModel.nextIdx({nowrap:!0});if(l===void 0)return;const f=l===this._focusModel.focusedItemIdx?l-1:l,m=this._focusModel.items[f];m&&this.revealChunk(m)});v(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});v(this,"handleInstructionSubmit",t=>{const n=this._editor.getModifiedEditor(),r=this.getSelectedCodeDetails(n);if(!r)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,r)});v(this,"updateModels",(t,n,r)=>{var u,l;const s=(l=(u=this._editor.getModel())==null?void 0:u.original)==null?void 0:l.uri,a=(r&&this._monaco.Uri.file(r.relPath))??s;if(a)if((s==null?void 0:s.fsPath)!==a.fsPath||(s==null?void 0:s.authority)!==a.authority){const f=a.with({fragment:crypto.randomUUID()}),m=a.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,f),modified:this._monaco.editor.createModel(n??"",void 0,m)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==n&&this.getModifiedEditor().setValue(n??"");else console.warn("No URI found for diff view. Not updating models.")});v(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=r,this._asyncMsgSender=new bg(s=>Ns.postMessage(s)),this.initializeEditor(n)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Nf(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var a,u;const t=[],n=this._editor.getLineChanges(),r=(a=this._editor.getModel())==null?void 0:a.original,s=(u=this._editor.getModel())==null?void 0:u.modified;if(n&&r&&s){for(const l of n){const f=Jh({startLineNumber:l.originalStartLineNumber,startColumn:1,endLineNumber:l.originalEndLineNumber,endColumn:1}),m=Jh({startLineNumber:l.modifiedStartLineNumber,startColumn:1,endLineNumber:l.modifiedEndLineNumber,endColumn:1}),g=Ym(this._editor,f,m);t.push(g)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(l=>l.id)}}}getSelectedCodeDetails(t){const n=t.getModel();if(!n)return null;const r=n.getLanguageId(),s=1,a=1,u={lineNumber:n.getLineCount(),column:n.getLineMaxColumn(n.getLineCount())},l=le(this._selectionLines);if(!l)throw new Error("No selection lines found");const f=Math.min(l.end+1,u.lineNumber),m=new this._monaco.Range(l.start+1,1,f,n.getLineMaxColumn(f));let g=n.getValueInRange(m);f<n.getLineCount()&&(g+=n.getEOL());const y=new this._monaco.Range(s,a,m.startLineNumber,m.startColumn),b=Math.min(m.endLineNumber+1,u.lineNumber),k=new this._monaco.Range(b,1,u.lineNumber,u.column);return{selectedCode:g,prefix:n.getValueInRange(y),suffix:n.getValueInRange(k),path:n.uri.path,language:r,prefixBegin:y.startLineNumber-1,suffixEnd:k.endLineNumber-1}}}function Ym(o,t,n){var a,u;const r=(a=o.getModel())==null?void 0:a.original,s=(u=o.getModel())==null?void 0:u.modified;if(!r||!s)throw new Error("No models found");return function(l,f,m,g){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:l,modifiedCode:f,lineChanges:{lineChanges:[{originalStart:m.startLineNumber,originalEnd:m.endLineNumber,modifiedStart:g.startLineNumber,modifiedEnd:g.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(r.getValueInRange(t),s.getValueInRange(n),t,n)}function Jh(o){return o.endLineNumber===0?{startLineNumber:o.startLineNumber+1,startColumn:1,endLineNumber:o.startLineNumber+1,endColumn:1}:{startLineNumber:o.startLineNumber,startColumn:1,endLineNumber:o.endLineNumber+1,endColumn:1}}function tf(o){let t,n;return t=new Wr({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Xm]},$$scope:{ctx:o}}}),t.$on("click",function(){pr(o[4])&&o[4].apply(this,arguments)}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){o=r;const a={};132096&s&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function Xm(o){let t,n,r;return t=new _i({props:{keybinding:o[10].acceptFocusedChunk}}),{c(){V(t.$$.fragment),n=Ce(`
        Accept`)},m(s,a){G(t,s,a),rt(s,n,a),r=!0},p(s,a){const u={};1024&a&&(u.keybinding=s[10].acceptFocusedChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&it(n),Z(t,s)}}}function Jm(o){let t,n,r;return t=new _i({props:{keybinding:o[10].rejectFocusedChunk}}),{c(){V(t.$$.fragment),n=Ce(`
      Reject`)},m(s,a){G(t,s,a),rt(s,n,a),r=!0},p(s,a){const u={};1024&a&&(u.keybinding=s[10].rejectFocusedChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&it(n),Z(t,s)}}}function t_(o){let t,n,r,s,a,u,l,f,m,g,y=!o[3]&&tf(o);return l=new Wr({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[Jm]},$$scope:{ctx:o}}}),l.$on("click",function(){pr(o[5])&&o[5].apply(this,arguments)}),{c(){t=Vt("div"),r=Nt(),s=Vt("div"),a=Vt("div"),y&&y.c(),u=Nt(),V(l.$$.fragment),qt(t,"class","svelte-zm1705"),vn(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),qt(a,"class","c-button-container svelte-zm1705"),vn(a,"c-button-container--focused",o[1]),vn(a,"c-button-container--transparent",o[9]),qt(s,"class","c-chunk-action-panel-anchor svelte-zm1705"),Pi(s,"top",o[8]+"px"),vn(s,"c-chunk-action-panel-anchor--left",o[0]==="left"),vn(s,"c-chunk-action-panel-anchor--right",o[0]==="right"),vn(s,"c-chunk-action-panel-anchor--focused",o[1])},m(b,k){rt(b,t,k),rt(b,r,k),rt(b,s,k),Gt(s,a),y&&y.m(a,null),Gt(a,u),G(l,a,null),f=!0,m||(g=[Sf(n=o[6].renderActionsViewZone(t,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]})),Ur(s,"mouseenter",o[13]),Ur(s,"mousemove",o[13]),Ur(s,"mouseleave",o[13])],m=!0)},p(b,[k]){o=b,n&&pr(n.update)&&132&k&&n.update.call(null,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]}),(!f||130&k)&&vn(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),o[3]?y&&($e(),z(y,1,1,()=>{y=null}),ke()):y?(y.p(o,k),8&k&&F(y,1)):(y=tf(o),y.c(),F(y,1),y.m(a,u));const T={};132096&k&&(T.$$scope={dirty:k,ctx:o}),l.$set(T),(!f||2&k)&&vn(a,"c-button-container--focused",o[1]),(!f||512&k)&&vn(a,"c-button-container--transparent",o[9]),(!f||256&k)&&Pi(s,"top",o[8]+"px"),(!f||1&k)&&vn(s,"c-chunk-action-panel-anchor--left",o[0]==="left"),(!f||1&k)&&vn(s,"c-chunk-action-panel-anchor--right",o[0]==="right"),(!f||2&k)&&vn(s,"c-chunk-action-panel-anchor--focused",o[1])},i(b){f||(F(y),F(l.$$.fragment,b),f=!0)},o(b){z(y),z(l.$$.fragment,b),f=!1},d(b){b&&(it(t),it(r),it(s)),y&&y.d(),Z(l),m=!1,vu(g)}}}function e_(o,t,n){let r,{align:s="right"}=t,{isFocused:a}=t,{heightInPx:u=1}=t,{disableApply:l=!1}=t,{onAccept:f}=t,{onReject:m}=t,{diffViewModel:g}=t,{leaf:y}=t;const b=g.keybindings;fi(o,b,S=>n(10,r=S));let k=0,T,j=!1;function q(){T&&(clearTimeout(T),T=void 0),n(9,j=!1)}return o.$$set=S=>{"align"in S&&n(0,s=S.align),"isFocused"in S&&n(1,a=S.isFocused),"heightInPx"in S&&n(2,u=S.heightInPx),"disableApply"in S&&n(3,l=S.disableApply),"onAccept"in S&&n(4,f=S.onAccept),"onReject"in S&&n(5,m=S.onReject),"diffViewModel"in S&&n(6,g=S.diffViewModel),"leaf"in S&&n(7,y=S.leaf)},[s,a,u,l,f,m,g,y,k,j,r,b,S=>{n(8,k=S)},function(S){S.target.closest(".c-button-container")?q():S.type==="mouseenter"||S.type==="mousemove"?(q(),T=setTimeout(()=>{n(9,j=!0)},400)):S.type==="mouseleave"&&q()}]}class n_ extends pi{constructor(t){super(),gi(this,t,e_,t_,mi,{align:0,isFocused:1,heightInPx:2,disableApply:3,onAccept:4,onReject:5,diffViewModel:6,leaf:7})}}function ef(o){let t,n,r;function s(u){o[18](u)}let a={onOpenChange:o[16],content:o[3],triggerOn:[Xg.Hover],$$slots:{default:[i_]},$$scope:{ctx:o}};return o[4]!==void 0&&(a.requestClose=o[4]),t=new qg({props:a}),Ui.push(()=>fg(t,"requestClose",s)),{c(){V(t.$$.fragment)},m(u,l){G(t,u,l),r=!0},p(u,l){const f={};8&l&&(f.content=u[3]),1048576&l&&(f.$$scope={dirty:l,ctx:u}),!n&&16&l&&(n=!0,f.requestClose=u[4],dg(()=>n=!1)),t.$set(f)},i(u){r||(F(t.$$.fragment,u),r=!0)},o(u){z(t.$$.fragment,u),r=!1},d(u){Z(t,u)}}}function r_(o){let t,n;return t=new Og({}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function i_(o){let t,n;return t=new xg({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[r_]},$$scope:{ctx:o}}}),t.$on("click",o[17]),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1048576&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function s_(o){let t;return{c(){t=Vt("span"),t.textContent="No changes",qt(t,"class","c-diff-page-counter svelte-4zjwll")},m(n,r){rt(n,t,r)},p:Bt,i:Bt,o:Bt,d(n){n&&it(t)}}}function o_(o){var m,g;let t,n,r,s,a,u,l,f=((g=(m=o[1])==null?void 0:m.leaves)==null?void 0:g.length)+"";return u=new If({props:{size:1,loading:o[10]}}),{c(){t=Vt("span"),n=Ce(o[2]),r=Ce(" of "),s=Ce(f),a=Nt(),V(u.$$.fragment),qt(t,"class","c-diff-page-counter svelte-4zjwll")},m(y,b){rt(y,t,b),Gt(t,n),Gt(t,r),Gt(t,s),Gt(t,a),G(u,t,null),l=!0},p(y,b){var T,j;(!l||4&b)&&gr(n,y[2]),(!l||2&b)&&f!==(f=((j=(T=y[1])==null?void 0:T.leaves)==null?void 0:j.length)+"")&&gr(s,f);const k={};1024&b&&(k.loading=y[10]),u.$set(k)},i(y){l||(F(u.$$.fragment,y),l=!0)},o(y){z(u.$$.fragment,y),l=!1},d(y){y&&it(t),Z(u)}}}function a_(o){let t,n,r,s;return r=new If({props:{size:1,loading:o[10]}}),{c(){t=Vt("span"),n=Ce(`Generating changes
        `),V(r.$$.fragment),qt(t,"class","c-diff-page-counter svelte-4zjwll")},m(a,u){rt(a,t,u),Gt(t,n),G(r,t,null),s=!0},p(a,u){const l={};1024&u&&(l.loading=a[10]),r.$set(l)},i(a){s||(F(r.$$.fragment,a),s=!0)},o(a){z(r.$$.fragment,a),s=!1},d(a){a&&it(t),Z(r)}}}function nf(o){let t,n,r,s,a,u;t=new Wr({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[c_]},$$scope:{ctx:o}}}),t.$on("click",function(){pr(o[0].focusPrevChunk)&&o[0].focusPrevChunk.apply(this,arguments)}),r=new Wr({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[u_]},$$scope:{ctx:o}}}),r.$on("click",function(){pr(o[0].focusNextChunk)&&o[0].focusNextChunk.apply(this,arguments)});let l=!o[12]&&rf(o);return{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment),s=Nt(),l&&l.c(),a=_r()},m(f,m){G(t,f,m),rt(f,n,m),G(r,f,m),rt(f,s,m),l&&l.m(f,m),rt(f,a,m),u=!0},p(f,m){o=f;const g={};1050624&m&&(g.$$scope={dirty:m,ctx:o}),t.$set(g);const y={};1050624&m&&(y.$$scope={dirty:m,ctx:o}),r.$set(y),o[12]?l&&($e(),z(l,1,1,()=>{l=null}),ke()):l?(l.p(o,m),4096&m&&F(l,1)):(l=rf(o),l.c(),F(l,1),l.m(a.parentNode,a))},i(f){u||(F(t.$$.fragment,f),F(r.$$.fragment,f),F(l),u=!0)},o(f){z(t.$$.fragment,f),z(r.$$.fragment,f),z(l),u=!1},d(f){f&&(it(n),it(s),it(a)),Z(t,f),Z(r,f),l&&l.d(f)}}}function c_(o){let t,n,r;return t=new _i({props:{keybinding:o[11].focusPrevChunk}}),{c(){V(t.$$.fragment),n=Ce(`
        Back`)},m(s,a){G(t,s,a),rt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].focusPrevChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&it(n),Z(t,s)}}}function u_(o){let t,n,r;return t=new _i({props:{keybinding:o[11].focusNextChunk}}),{c(){V(t.$$.fragment),n=Ce(`
        Next`)},m(s,a){G(t,s,a),rt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].focusNextChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&it(n),Z(t,s)}}}function rf(o){let t,n,r,s=!o[13]&&sf(o);return n=new Wr({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[h_]},$$scope:{ctx:o}}}),n.$on("click",function(){pr(o[0].rejectAllChunks)&&o[0].rejectAllChunks.apply(this,arguments)}),{c(){s&&s.c(),t=Nt(),V(n.$$.fragment)},m(a,u){s&&s.m(a,u),rt(a,t,u),G(n,a,u),r=!0},p(a,u){(o=a)[13]?s&&($e(),z(s,1,1,()=>{s=null}),ke()):s?(s.p(o,u),8192&u&&F(s,1)):(s=sf(o),s.c(),F(s,1),s.m(t.parentNode,t));const l={};1050624&u&&(l.$$scope={dirty:u,ctx:o}),n.$set(l)},i(a){r||(F(s),F(n.$$.fragment,a),r=!0)},o(a){z(s),z(n.$$.fragment,a),r=!1},d(a){a&&it(t),s&&s.d(a),Z(n,a)}}}function sf(o){let t,n;return t=new Wr({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[l_]},$$scope:{ctx:o}}}),t.$on("click",function(){pr(o[0].acceptAllChunks)&&o[0].acceptAllChunks.apply(this,arguments)}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){o=r;const a={};1050624&s&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function l_(o){let t,n,r;return t=new _i({props:{keybinding:o[11].acceptAllChunks}}),{c(){V(t.$$.fragment),n=Ce(`
            Accept All`)},m(s,a){G(t,s,a),rt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].acceptAllChunks),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&it(n),Z(t,s)}}}function h_(o){let t,n,r;return t=new _i({props:{keybinding:o[11].rejectAllChunks}}),{c(){V(t.$$.fragment),n=Ce(`
          Reject All`)},m(s,a){G(t,s,a),rt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].rejectAllChunks),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&it(n),Z(t,s)}}}function f_(o){let t,n,r,s,a,u,l,f=o[9]&&ef(o);const m=[a_,o_,s_],g=[];function y(k,T){return!k[5]&&k[10]?0:k[5]?1:2}s=y(o),a=g[s]=m[s](o);let b=o[5]&&nf(o);return{c(){t=Vt("div"),n=Vt("div"),f&&f.c(),r=Nt(),a.c(),u=Nt(),b&&b.c(),qt(n,"class","c-button-container svelte-4zjwll"),qt(t,"class","c-top-action-panel-anchor svelte-4zjwll")},m(k,T){rt(k,t,T),Gt(t,n),f&&f.m(n,null),Gt(n,r),g[s].m(n,null),Gt(n,u),b&&b.m(n,null),l=!0},p(k,[T]){k[9]?f?(f.p(k,T),512&T&&F(f,1)):(f=ef(k),f.c(),F(f,1),f.m(n,r)):f&&($e(),z(f,1,1,()=>{f=null}),ke());let j=s;s=y(k),s===j?g[s].p(k,T):($e(),z(g[j],1,1,()=>{g[j]=null}),ke(),a=g[s],a?a.p(k,T):(a=g[s]=m[s](k),a.c()),F(a,1),a.m(n,u)),k[5]?b?(b.p(k,T),32&T&&F(b,1)):(b=nf(k),b.c(),F(b,1),b.m(n,null)):b&&($e(),z(b,1,1,()=>{b=null}),ke())},i(k){l||(F(f),F(a),F(b),l=!0)},o(k){z(f),z(a),z(b),l=!1},d(k){k&&it(t),f&&f.d(),g[s].d(),b&&b.d()}}}function d_(o,t,n){let r,s,a,u,l,f,m,g,y,b,k=Bt,T=()=>(k(),k=dr(E,mt=>n(1,f=mt)),E),j=Bt,q=Bt,S=Bt;o.$$.on_destroy.push(()=>k()),o.$$.on_destroy.push(()=>j()),o.$$.on_destroy.push(()=>q()),o.$$.on_destroy.push(()=>S());let{diffViewModel:E}=t;T();const H=E.keybindings;fi(o,H,mt=>n(11,g=mt));const ot=E.requestId;fi(o,ot,mt=>n(9,l=mt));let st,Dt="x",pt="Copy request ID",St=()=>{};return o.$$set=mt=>{"diffViewModel"in mt&&T(n(0,E=mt.diffViewModel))},o.$$.update=()=>{var mt;2&o.$$.dirty&&(n(8,r=f.disableResolution),q(),q=dr(r,Ft=>n(12,y=Ft))),2&o.$$.dirty&&(n(7,s=f.disableApply),S(),S=dr(s,Ft=>n(13,b=Ft))),2&o.$$.dirty&&(f.currFocusedChunkIdx!==void 0?n(2,Dt=(f.currFocusedChunkIdx+1).toString()):n(2,Dt="x")),2&o.$$.dirty&&(n(6,a=f.isLoading),j(),j=dr(a,Ft=>n(10,m=Ft))),2&o.$$.dirty&&n(5,u=!!((mt=f.leaves)!=null&&mt.length))},[E,f,Dt,pt,St,u,a,s,r,l,m,g,y,b,H,ot,function(mt){mt||(clearTimeout(st),st=void 0,n(3,pt="Copy request ID"))},async function(){l&&(await navigator.clipboard.writeText(l),n(3,pt="Copied!"),clearTimeout(st),st=setTimeout(St,1500))},function(mt){St=mt,n(4,St)}]}class p_ extends pi{constructor(t){super(),gi(this,t,d_,f_,mi,{diffViewModel:0})}}var sa,oa,fu={exports:{}};sa=fu,oa=fu.exports,(function(){var o,t="Expected a function",n="__lodash_hash_undefined__",r="__lodash_placeholder__",s=16,a=32,u=64,l=128,f=256,m=1/0,g=9007199254740991,y=NaN,b=4294967295,k=[["ary",l],["bind",1],["bindKey",2],["curry",8],["curryRight",s],["flip",512],["partial",a],["partialRight",u],["rearg",f]],T="[object Arguments]",j="[object Array]",q="[object Boolean]",S="[object Date]",E="[object Error]",H="[object Function]",ot="[object GeneratorFunction]",st="[object Map]",Dt="[object Number]",pt="[object Object]",St="[object Promise]",mt="[object RegExp]",Ft="[object Set]",Ht="[object String]",ce="[object Symbol]",Ct="[object WeakMap]",wt="[object ArrayBuffer]",he="[object DataView]",yn="[object Float32Array]",Se="[object Float64Array]",dt="[object Int8Array]",bn="[object Int16Array]",Ln="[object Int32Array]",Yn="[object Uint8Array]",vr="[object Uint8ClampedArray]",yi="[object Uint16Array]",Yi="[object Uint32Array]",Ca=/\b__p \+= '';/g,Sa=/\b(__p \+=) '' \+/g,Vs=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Xi=/&(?:amp|lt|gt|quot|#39);/g,Ji=/[&<>"']/g,Ia=RegExp(Xi.source),Ma=RegExp(Ji.source),bi=/<%-([\s\S]+?)%>/g,ts=/<%([\s\S]+?)%>/g,es=/<%=([\s\S]+?)%>/g,cn=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Gs=/^\w*$/,Zs=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ns=/[\\^$.*+?()[\]{}|]/g,Qs=RegExp(ns.source),yr=/^\s+/,Ks=/\s/,Ys=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Ea=/\{\n\/\* \[wrapped with (.+)\] \*/,Xn=/,? & /,Xs=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Aa=/[()=,{}\[\]\/\s]/,Ta=/\\(\\)?/g,rs=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,br=/\w*$/,is=/^[-+]0x[0-9a-f]+$/i,ss=/^0b[01]+$/i,os=/^\[object .+?Constructor\]$/,Hr=/^0o[0-7]+$/i,Fa=/^(?:0|[1-9]\d*)$/,Ra=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,xi=/($^)/,Oa=/['\n\r\u2028\u2029\\]/g,wi="\\ud800-\\udfff",as="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",$i="\\u2700-\\u27bf",Nn="a-z\\xdf-\\xf6\\xf8-\\xff",un="A-Z\\xc0-\\xd6\\xd8-\\xde",Dn="\\ufe0e\\ufe0f",Vr="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",zn="['’]",La="["+wi+"]",Gr="["+Vr+"]",jn="["+as+"]",cs="\\d+",Js="["+$i+"]",to="["+Nn+"]",eo="[^"+wi+Vr+cs+$i+Nn+un+"]",ki="\\ud83c[\\udffb-\\udfff]",us="[^"+wi+"]",xr="(?:\\ud83c[\\udde6-\\uddff]){2}",qn="[\\ud800-\\udbff][\\udc00-\\udfff]",wr="["+un+"]",xn="\\u200d",no="(?:"+to+"|"+eo+")",Jn="(?:"+wr+"|"+eo+")",ls="(?:['’](?:d|ll|m|re|s|t|ve))?",ro="(?:['’](?:D|LL|M|RE|S|T|VE))?",Zr="(?:"+jn+"|"+ki+")?",Ci="["+Dn+"]?",hs=Ci+Zr+"(?:"+xn+"(?:"+[us,xr,qn].join("|")+")"+Ci+Zr+")*",io="(?:"+[Js,xr,qn].join("|")+")"+hs,so="(?:"+[us+jn+"?",jn,xr,qn,La].join("|")+")",Qr=RegExp(zn,"g"),oo=RegExp(jn,"g"),fs=RegExp(ki+"(?="+ki+")|"+so+hs,"g"),C=RegExp([wr+"?"+to+"+"+ls+"(?="+[Gr,wr,"$"].join("|")+")",Jn+"+"+ro+"(?="+[Gr,wr+no,"$"].join("|")+")",wr+"?"+no+"+"+ls,wr+"+"+ro,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",cs,io].join("|"),"g"),A=RegExp("["+xn+wi+as+Dn+"]"),W=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,et=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Ut=-1,J={};J[yn]=J[Se]=J[dt]=J[bn]=J[Ln]=J[Yn]=J[vr]=J[yi]=J[Yi]=!0,J[T]=J[j]=J[wt]=J[q]=J[he]=J[S]=J[E]=J[H]=J[st]=J[Dt]=J[pt]=J[mt]=J[Ft]=J[Ht]=J[Ct]=!1;var ft={};ft[T]=ft[j]=ft[wt]=ft[he]=ft[q]=ft[S]=ft[yn]=ft[Se]=ft[dt]=ft[bn]=ft[Ln]=ft[st]=ft[Dt]=ft[pt]=ft[mt]=ft[Ft]=ft[Ht]=ft[ce]=ft[Yn]=ft[vr]=ft[yi]=ft[Yi]=!0,ft[E]=ft[H]=ft[Ct]=!1;var te={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Ge=parseFloat,re=parseInt,ee=typeof an=="object"&&an&&an.Object===Object&&an,ln=typeof self=="object"&&self&&self.Object===Object&&self,Wt=ee||ln||Function("return this")(),fe=oa&&!oa.nodeType&&oa,Ie=fe&&sa&&!sa.nodeType&&sa,tr=Ie&&Ie.exports===fe,$r=tr&&ee.process,de=function(){try{var M=Ie&&Ie.require&&Ie.require("util").types;return M||$r&&$r.binding&&$r.binding("util")}catch{}}(),kr=de&&de.isArrayBuffer,ds=de&&de.isDate,ao=de&&de.isMap,co=de&&de.isRegExp,Rt=de&&de.isSet,At=de&&de.isTypedArray;function ye(M,N,D){switch(D.length){case 0:return M.call(N);case 1:return M.call(N,D[0]);case 2:return M.call(N,D[0],D[1]);case 3:return M.call(N,D[0],D[1],D[2])}return M.apply(N,D)}function Pn(M,N,D,K){for(var _t=-1,Mt=M==null?0:M.length;++_t<Mt;){var pe=M[_t];N(K,pe,D(pe),M)}return K}function Te(M,N){for(var D=-1,K=M==null?0:M.length;++D<K&&N(M[D],D,M)!==!1;);return M}function Ue(M,N){for(var D=M==null?0:M.length;D--&&N(M[D],D,M)!==!1;);return M}function hn(M,N){for(var D=-1,K=M==null?0:M.length;++D<K;)if(!N(M[D],D,M))return!1;return!0}function Me(M,N){for(var D=-1,K=M==null?0:M.length,_t=0,Mt=[];++D<K;){var pe=M[D];N(pe,D,M)&&(Mt[_t++]=pe)}return Mt}function Un(M,N){return!(M==null||!M.length)&&nr(M,N,0)>-1}function Cr(M,N,D){for(var K=-1,_t=M==null?0:M.length;++K<_t;)if(D(N,M[K]))return!0;return!1}function zt(M,N){for(var D=-1,K=M==null?0:M.length,_t=Array(K);++D<K;)_t[D]=N(M[D],D,M);return _t}function Ze(M,N){for(var D=-1,K=N.length,_t=M.length;++D<K;)M[_t+D]=N[D];return M}function wn(M,N,D,K){var _t=-1,Mt=M==null?0:M.length;for(K&&Mt&&(D=M[++_t]);++_t<Mt;)D=N(D,M[_t],_t,M);return D}function uo(M,N,D,K){var _t=M==null?0:M.length;for(K&&_t&&(D=M[--_t]);_t--;)D=N(D,M[_t],_t,M);return D}function Sr(M,N){for(var D=-1,K=M==null?0:M.length;++D<K;)if(N(M[D],D,M))return!0;return!1}var ps=Na("length");function Kr(M,N,D){var K;return D(M,function(_t,Mt,pe){if(N(_t,Mt,pe))return K=Mt,!1}),K}function er(M,N,D,K){for(var _t=M.length,Mt=D+(K?1:-1);K?Mt--:++Mt<_t;)if(N(M[Mt],Mt,M))return Mt;return-1}function nr(M,N,D){return N==N?function(K,_t,Mt){for(var pe=Mt-1,Wn=K.length;++pe<Wn;)if(K[pe]===_t)return pe;return-1}(M,N,D):er(M,Yr,D)}function Si(M,N,D,K){for(var _t=D-1,Mt=M.length;++_t<Mt;)if(K(M[_t],N))return _t;return-1}function Yr(M){return M!=M}function Ru(M,N){var D=M==null?0:M.length;return D?za(M,N)/D:y}function Na(M){return function(N){return N==null?o:N[M]}}function Da(M){return function(N){return M==null?o:M[N]}}function Ou(M,N,D,K,_t){return _t(M,function(Mt,pe,Wn){D=K?(K=!1,Mt):N(D,Mt,pe,Wn)}),D}function za(M,N){for(var D,K=-1,_t=M.length;++K<_t;){var Mt=N(M[K]);Mt!==o&&(D=D===o?Mt:D+Mt)}return D}function ja(M,N){for(var D=-1,K=Array(M);++D<M;)K[D]=N(D);return K}function Lu(M){return M&&M.slice(0,ju(M)+1).replace(yr,"")}function Qe(M){return function(N){return M(N)}}function qa(M,N){return zt(N,function(D){return M[D]})}function gs(M,N){return M.has(N)}function Nu(M,N){for(var D=-1,K=M.length;++D<K&&nr(N,M[D],0)>-1;);return D}function Du(M,N){for(var D=M.length;D--&&nr(N,M[D],0)>-1;);return D}var sd=Da({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),od=Da({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function ad(M){return"\\"+te[M]}function Ii(M){return A.test(M)}function Pa(M){var N=-1,D=Array(M.size);return M.forEach(function(K,_t){D[++N]=[_t,K]}),D}function zu(M,N){return function(D){return M(N(D))}}function Ir(M,N){for(var D=-1,K=M.length,_t=0,Mt=[];++D<K;){var pe=M[D];pe!==N&&pe!==r||(M[D]=r,Mt[_t++]=D)}return Mt}function lo(M){var N=-1,D=Array(M.size);return M.forEach(function(K){D[++N]=K}),D}function cd(M){var N=-1,D=Array(M.size);return M.forEach(function(K){D[++N]=[K,K]}),D}function Mi(M){return Ii(M)?function(N){for(var D=fs.lastIndex=0;fs.test(N);)++D;return D}(M):ps(M)}function $n(M){return Ii(M)?function(N){return N.match(fs)||[]}(M):function(N){return N.split("")}(M)}function ju(M){for(var N=M.length;N--&&Ks.test(M.charAt(N)););return N}var ud=Da({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),Ei=function M(N){var D,K=(N=N==null?Wt:Ei.defaults(Wt.Object(),N,Ei.pick(Wt,et))).Array,_t=N.Date,Mt=N.Error,pe=N.Function,Wn=N.Math,Zt=N.Object,Ua=N.RegExp,ld=N.String,fn=N.TypeError,ho=K.prototype,hd=pe.prototype,Ai=Zt.prototype,fo=N["__core-js_shared__"],po=hd.toString,Lt=Ai.hasOwnProperty,fd=0,qu=(D=/[^.]+$/.exec(fo&&fo.keys&&fo.keys.IE_PROTO||""))?"Symbol(src)_1."+D:"",go=Ai.toString,dd=po.call(Zt),pd=Wt._,gd=Ua("^"+po.call(Lt).replace(ns,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),mo=tr?N.Buffer:o,Mr=N.Symbol,_o=N.Uint8Array,Pu=mo?mo.allocUnsafe:o,vo=zu(Zt.getPrototypeOf,Zt),Uu=Zt.create,Wu=Ai.propertyIsEnumerable,yo=ho.splice,Bu=Mr?Mr.isConcatSpreadable:o,ms=Mr?Mr.iterator:o,Xr=Mr?Mr.toStringTag:o,bo=function(){try{var e=ri(Zt,"defineProperty");return e({},"",{}),e}catch{}}(),md=N.clearTimeout!==Wt.clearTimeout&&N.clearTimeout,_d=_t&&_t.now!==Wt.Date.now&&_t.now,vd=N.setTimeout!==Wt.setTimeout&&N.setTimeout,xo=Wn.ceil,wo=Wn.floor,Wa=Zt.getOwnPropertySymbols,yd=mo?mo.isBuffer:o,Hu=N.isFinite,bd=ho.join,xd=zu(Zt.keys,Zt),ge=Wn.max,Fe=Wn.min,wd=_t.now,$d=N.parseInt,Vu=Wn.random,kd=ho.reverse,Ba=ri(N,"DataView"),_s=ri(N,"Map"),Ha=ri(N,"Promise"),Ti=ri(N,"Set"),vs=ri(N,"WeakMap"),ys=ri(Zt,"create"),$o=vs&&new vs,Fi={},Cd=ii(Ba),Sd=ii(_s),Id=ii(Ha),Md=ii(Ti),Ed=ii(vs),ko=Mr?Mr.prototype:o,bs=ko?ko.valueOf:o,Gu=ko?ko.toString:o;function p(e){if(ne(e)&&!xt(e)&&!(e instanceof It)){if(e instanceof dn)return e;if(Lt.call(e,"__wrapped__"))return Zl(e)}return new dn(e)}var Ri=function(){function e(){}return function(i){if(!Jt(i))return{};if(Uu)return Uu(i);e.prototype=i;var c=new e;return e.prototype=o,c}}();function Co(){}function dn(e,i){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!i,this.__index__=0,this.__values__=o}function It(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=b,this.__views__=[]}function Jr(e){var i=-1,c=e==null?0:e.length;for(this.clear();++i<c;){var h=e[i];this.set(h[0],h[1])}}function rr(e){var i=-1,c=e==null?0:e.length;for(this.clear();++i<c;){var h=e[i];this.set(h[0],h[1])}}function ir(e){var i=-1,c=e==null?0:e.length;for(this.clear();++i<c;){var h=e[i];this.set(h[0],h[1])}}function ti(e){var i=-1,c=e==null?0:e.length;for(this.__data__=new ir;++i<c;)this.add(e[i])}function kn(e){var i=this.__data__=new rr(e);this.size=i.size}function Zu(e,i){var c=xt(e),h=!c&&si(e),d=!c&&!h&&Rr(e),_=!c&&!h&&!d&&Di(e),w=c||h||d||_,$=w?ja(e.length,ld):[],I=$.length;for(var O in e)!i&&!Lt.call(e,O)||w&&(O=="length"||d&&(O=="offset"||O=="parent")||_&&(O=="buffer"||O=="byteLength"||O=="byteOffset")||cr(O,I))||$.push(O);return $}function Qu(e){var i=e.length;return i?e[nc(0,i-1)]:o}function Ad(e,i){return jo(We(e),ei(i,0,e.length))}function Td(e){return jo(We(e))}function Va(e,i,c){(c!==o&&!Cn(e[i],c)||c===o&&!(i in e))&&sr(e,i,c)}function xs(e,i,c){var h=e[i];Lt.call(e,i)&&Cn(h,c)&&(c!==o||i in e)||sr(e,i,c)}function So(e,i){for(var c=e.length;c--;)if(Cn(e[c][0],i))return c;return-1}function Fd(e,i,c,h){return Er(e,function(d,_,w){i(h,d,c(d),w)}),h}function Ku(e,i){return e&&Hn(i,be(i),e)}function sr(e,i,c){i=="__proto__"&&bo?bo(e,i,{configurable:!0,enumerable:!0,value:c,writable:!0}):e[i]=c}function Ga(e,i){for(var c=-1,h=i.length,d=K(h),_=e==null;++c<h;)d[c]=_?o:Ic(e,i[c]);return d}function ei(e,i,c){return e==e&&(c!==o&&(e=e<=c?e:c),i!==o&&(e=e>=i?e:i)),e}function pn(e,i,c,h,d,_){var w,$=1&i,I=2&i,O=4&i;if(c&&(w=d?c(e,h,d,_):c(e)),w!==o)return w;if(!Jt(e))return e;var R=xt(e);if(R){if(w=function(L){var U=L.length,lt=new L.constructor(U);return U&&typeof L[0]=="string"&&Lt.call(L,"index")&&(lt.index=L.index,lt.input=L.input),lt}(e),!$)return We(e,w)}else{var P=Re(e),X=P==H||P==ot;if(Rr(e))return bl(e,$);if(P==pt||P==T||X&&!d){if(w=I||X?{}:jl(e),!$)return I?function(L,U){return Hn(L,Dl(L),U)}(e,function(L,U){return L&&Hn(U,He(U),L)}(w,e)):function(L,U){return Hn(L,mc(L),U)}(e,Ku(w,e))}else{if(!ft[P])return d?e:{};w=function(L,U,lt){var Q,yt=L.constructor;switch(U){case wt:return uc(L);case q:case S:return new yt(+L);case he:return function(gt,Tt){var nt=Tt?uc(gt.buffer):gt.buffer;return new gt.constructor(nt,gt.byteOffset,gt.byteLength)}(L,lt);case yn:case Se:case dt:case bn:case Ln:case Yn:case vr:case yi:case Yi:return xl(L,lt);case st:return new yt;case Dt:case Ht:return new yt(L);case mt:return function(gt){var Tt=new gt.constructor(gt.source,br.exec(gt));return Tt.lastIndex=gt.lastIndex,Tt}(L);case Ft:return new yt;case ce:return Q=L,bs?Zt(bs.call(Q)):{}}}(e,P,$)}}_||(_=new kn);var tt=_.get(e);if(tt)return tt;_.set(e,w),fh(e)?e.forEach(function(L){w.add(pn(L,i,c,L,e,_))}):lh(e)&&e.forEach(function(L,U){w.set(U,pn(L,i,c,U,e,_))});var at=R?o:(O?I?dc:fc:I?He:be)(e);return Te(at||e,function(L,U){at&&(L=e[U=L]),xs(w,U,pn(L,i,c,U,e,_))}),w}function Yu(e,i,c){var h=c.length;if(e==null)return!h;for(e=Zt(e);h--;){var d=c[h],_=i[d],w=e[d];if(w===o&&!(d in e)||!_(w))return!1}return!0}function Xu(e,i,c){if(typeof e!="function")throw new fn(t);return Ms(function(){e.apply(o,c)},i)}function ws(e,i,c,h){var d=-1,_=Un,w=!0,$=e.length,I=[],O=i.length;if(!$)return I;c&&(i=zt(i,Qe(c))),h?(_=Cr,w=!1):i.length>=200&&(_=gs,w=!1,i=new ti(i));t:for(;++d<$;){var R=e[d],P=c==null?R:c(R);if(R=h||R!==0?R:0,w&&P==P){for(var X=O;X--;)if(i[X]===P)continue t;I.push(R)}else _(i,P,h)||I.push(R)}return I}p.templateSettings={escape:bi,evaluate:ts,interpolate:es,variable:"",imports:{_:p}},p.prototype=Co.prototype,p.prototype.constructor=p,dn.prototype=Ri(Co.prototype),dn.prototype.constructor=dn,It.prototype=Ri(Co.prototype),It.prototype.constructor=It,Jr.prototype.clear=function(){this.__data__=ys?ys(null):{},this.size=0},Jr.prototype.delete=function(e){var i=this.has(e)&&delete this.__data__[e];return this.size-=i?1:0,i},Jr.prototype.get=function(e){var i=this.__data__;if(ys){var c=i[e];return c===n?o:c}return Lt.call(i,e)?i[e]:o},Jr.prototype.has=function(e){var i=this.__data__;return ys?i[e]!==o:Lt.call(i,e)},Jr.prototype.set=function(e,i){var c=this.__data__;return this.size+=this.has(e)?0:1,c[e]=ys&&i===o?n:i,this},rr.prototype.clear=function(){this.__data__=[],this.size=0},rr.prototype.delete=function(e){var i=this.__data__,c=So(i,e);return!(c<0||(c==i.length-1?i.pop():yo.call(i,c,1),--this.size,0))},rr.prototype.get=function(e){var i=this.__data__,c=So(i,e);return c<0?o:i[c][1]},rr.prototype.has=function(e){return So(this.__data__,e)>-1},rr.prototype.set=function(e,i){var c=this.__data__,h=So(c,e);return h<0?(++this.size,c.push([e,i])):c[h][1]=i,this},ir.prototype.clear=function(){this.size=0,this.__data__={hash:new Jr,map:new(_s||rr),string:new Jr}},ir.prototype.delete=function(e){var i=zo(this,e).delete(e);return this.size-=i?1:0,i},ir.prototype.get=function(e){return zo(this,e).get(e)},ir.prototype.has=function(e){return zo(this,e).has(e)},ir.prototype.set=function(e,i){var c=zo(this,e),h=c.size;return c.set(e,i),this.size+=c.size==h?0:1,this},ti.prototype.add=ti.prototype.push=function(e){return this.__data__.set(e,n),this},ti.prototype.has=function(e){return this.__data__.has(e)},kn.prototype.clear=function(){this.__data__=new rr,this.size=0},kn.prototype.delete=function(e){var i=this.__data__,c=i.delete(e);return this.size=i.size,c},kn.prototype.get=function(e){return this.__data__.get(e)},kn.prototype.has=function(e){return this.__data__.has(e)},kn.prototype.set=function(e,i){var c=this.__data__;if(c instanceof rr){var h=c.__data__;if(!_s||h.length<199)return h.push([e,i]),this.size=++c.size,this;c=this.__data__=new ir(h)}return c.set(e,i),this.size=c.size,this};var Er=Cl(Bn),Ju=Cl(Qa,!0);function Rd(e,i){var c=!0;return Er(e,function(h,d,_){return c=!!i(h,d,_)}),c}function Io(e,i,c){for(var h=-1,d=e.length;++h<d;){var _=e[h],w=i(_);if(w!=null&&($===o?w==w&&!Ye(w):c(w,$)))var $=w,I=_}return I}function tl(e,i){var c=[];return Er(e,function(h,d,_){i(h,d,_)&&c.push(h)}),c}function Ee(e,i,c,h,d){var _=-1,w=e.length;for(c||(c=Hd),d||(d=[]);++_<w;){var $=e[_];i>0&&c($)?i>1?Ee($,i-1,c,h,d):Ze(d,$):h||(d[d.length]=$)}return d}var Za=Sl(),el=Sl(!0);function Bn(e,i){return e&&Za(e,i,be)}function Qa(e,i){return e&&el(e,i,be)}function Mo(e,i){return Me(i,function(c){return ur(e[c])})}function ni(e,i){for(var c=0,h=(i=Tr(i,e)).length;e!=null&&c<h;)e=e[Vn(i[c++])];return c&&c==h?e:o}function nl(e,i,c){var h=i(e);return xt(e)?h:Ze(h,c(e))}function Ne(e){return e==null?e===o?"[object Undefined]":"[object Null]":Xr&&Xr in Zt(e)?function(i){var c=Lt.call(i,Xr),h=i[Xr];try{i[Xr]=o;var d=!0}catch{}var _=go.call(i);return d&&(c?i[Xr]=h:delete i[Xr]),_}(e):function(i){return go.call(i)}(e)}function Ka(e,i){return e>i}function Od(e,i){return e!=null&&Lt.call(e,i)}function Ld(e,i){return e!=null&&i in Zt(e)}function Ya(e,i,c){for(var h=c?Cr:Un,d=e[0].length,_=e.length,w=_,$=K(_),I=1/0,O=[];w--;){var R=e[w];w&&i&&(R=zt(R,Qe(i))),I=Fe(R.length,I),$[w]=!c&&(i||d>=120&&R.length>=120)?new ti(w&&R):o}R=e[0];var P=-1,X=$[0];t:for(;++P<d&&O.length<I;){var tt=R[P],at=i?i(tt):tt;if(tt=c||tt!==0?tt:0,!(X?gs(X,at):h(O,at,c))){for(w=_;--w;){var L=$[w];if(!(L?gs(L,at):h(e[w],at,c)))continue t}X&&X.push(at),O.push(tt)}}return O}function $s(e,i,c){var h=(e=Wl(e,i=Tr(i,e)))==null?e:e[Vn(mn(i))];return h==null?o:ye(h,e,c)}function rl(e){return ne(e)&&Ne(e)==T}function ks(e,i,c,h,d){return e===i||(e==null||i==null||!ne(e)&&!ne(i)?e!=e&&i!=i:function(_,w,$,I,O,R){var P=xt(_),X=xt(w),tt=P?j:Re(_),at=X?j:Re(w),L=(tt=tt==T?pt:tt)==pt,U=(at=at==T?pt:at)==pt,lt=tt==at;if(lt&&Rr(_)){if(!Rr(w))return!1;P=!0,L=!1}if(lt&&!L)return R||(R=new kn),P||Di(_)?Nl(_,w,$,I,O,R):function(nt,ht,me,se,ze,Qt,Oe){switch(me){case he:if(nt.byteLength!=ht.byteLength||nt.byteOffset!=ht.byteOffset)return!1;nt=nt.buffer,ht=ht.buffer;case wt:return!(nt.byteLength!=ht.byteLength||!Qt(new _o(nt),new _o(ht)));case q:case S:case Dt:return Cn(+nt,+ht);case E:return nt.name==ht.name&&nt.message==ht.message;case mt:case Ht:return nt==ht+"";case st:var Gn=Pa;case Ft:var Or=1&se;if(Gn||(Gn=lo),nt.size!=ht.size&&!Or)return!1;var Zo=Oe.get(nt);if(Zo)return Zo==ht;se|=2,Oe.set(nt,ht);var Dc=Nl(Gn(nt),Gn(ht),se,ze,Qt,Oe);return Oe.delete(nt),Dc;case ce:if(bs)return bs.call(nt)==bs.call(ht)}return!1}(_,w,tt,$,I,O,R);if(!(1&$)){var Q=L&&Lt.call(_,"__wrapped__"),yt=U&&Lt.call(w,"__wrapped__");if(Q||yt){var gt=Q?_.value():_,Tt=yt?w.value():w;return R||(R=new kn),O(gt,Tt,$,I,R)}}return!!lt&&(R||(R=new kn),function(nt,ht,me,se,ze,Qt){var Oe=1&me,Gn=fc(nt),Or=Gn.length,Zo=fc(ht),Dc=Zo.length;if(Or!=Dc&&!Oe)return!1;for(var Qo=Or;Qo--;){var oi=Gn[Qo];if(!(Oe?oi in ht:Lt.call(ht,oi)))return!1}var Sh=Qt.get(nt),Ih=Qt.get(ht);if(Sh&&Ih)return Sh==ht&&Ih==nt;var Ko=!0;Qt.set(nt,ht),Qt.set(ht,nt);for(var zc=Oe;++Qo<Or;){var Yo=nt[oi=Gn[Qo]],Xo=ht[oi];if(se)var Mh=Oe?se(Xo,Yo,oi,ht,nt,Qt):se(Yo,Xo,oi,nt,ht,Qt);if(!(Mh===o?Yo===Xo||ze(Yo,Xo,me,se,Qt):Mh)){Ko=!1;break}zc||(zc=oi=="constructor")}if(Ko&&!zc){var Jo=nt.constructor,ta=ht.constructor;Jo==ta||!("constructor"in nt)||!("constructor"in ht)||typeof Jo=="function"&&Jo instanceof Jo&&typeof ta=="function"&&ta instanceof ta||(Ko=!1)}return Qt.delete(nt),Qt.delete(ht),Ko}(_,w,$,I,O,R))}(e,i,c,h,ks,d))}function Xa(e,i,c,h){var d=c.length,_=d,w=!h;if(e==null)return!_;for(e=Zt(e);d--;){var $=c[d];if(w&&$[2]?$[1]!==e[$[0]]:!($[0]in e))return!1}for(;++d<_;){var I=($=c[d])[0],O=e[I],R=$[1];if(w&&$[2]){if(O===o&&!(I in e))return!1}else{var P=new kn;if(h)var X=h(O,R,I,e,i,P);if(!(X===o?ks(R,O,3,h,P):X))return!1}}return!0}function il(e){return!(!Jt(e)||(i=e,qu&&qu in i))&&(ur(e)?gd:os).test(ii(e));var i}function sl(e){return typeof e=="function"?e:e==null?Ve:typeof e=="object"?xt(e)?cl(e[0],e[1]):al(e):Ch(e)}function Ja(e){if(!Is(e))return xd(e);var i=[];for(var c in Zt(e))Lt.call(e,c)&&c!="constructor"&&i.push(c);return i}function Nd(e){if(!Jt(e))return function(d){var _=[];if(d!=null)for(var w in Zt(d))_.push(w);return _}(e);var i=Is(e),c=[];for(var h in e)(h!="constructor"||!i&&Lt.call(e,h))&&c.push(h);return c}function tc(e,i){return e<i}function ol(e,i){var c=-1,h=Be(e)?K(e.length):[];return Er(e,function(d,_,w){h[++c]=i(d,_,w)}),h}function al(e){var i=gc(e);return i.length==1&&i[0][2]?Pl(i[0][0],i[0][1]):function(c){return c===e||Xa(c,e,i)}}function cl(e,i){return _c(e)&&ql(i)?Pl(Vn(e),i):function(c){var h=Ic(c,e);return h===o&&h===i?Mc(c,e):ks(i,h,3)}}function Eo(e,i,c,h,d){e!==i&&Za(i,function(_,w){if(d||(d=new kn),Jt(_))(function(I,O,R,P,X,tt,at){var L=yc(I,R),U=yc(O,R),lt=at.get(U);if(lt)Va(I,R,lt);else{var Q=tt?tt(L,U,R+"",I,O,at):o,yt=Q===o;if(yt){var gt=xt(U),Tt=!gt&&Rr(U),nt=!gt&&!Tt&&Di(U);Q=U,gt||Tt||nt?xt(L)?Q=L:ie(L)?Q=We(L):Tt?(yt=!1,Q=bl(U,!0)):nt?(yt=!1,Q=xl(U,!0)):Q=[]:Es(U)||si(U)?(Q=L,si(L)?Q=gh(L):Jt(L)&&!ur(L)||(Q=jl(U))):yt=!1}yt&&(at.set(U,Q),X(Q,U,P,tt,at),at.delete(U)),Va(I,R,Q)}})(e,i,w,c,Eo,h,d);else{var $=h?h(yc(e,w),_,w+"",e,i,d):o;$===o&&($=_),Va(e,w,$)}},He)}function ul(e,i){var c=e.length;if(c)return cr(i+=i<0?c:0,c)?e[i]:o}function ll(e,i,c){i=i.length?zt(i,function(_){return xt(_)?function(w){return ni(w,_.length===1?_[0]:_)}:_}):[Ve];var h=-1;i=zt(i,Qe(ct()));var d=ol(e,function(_,w,$){var I=zt(i,function(O){return O(_)});return{criteria:I,index:++h,value:_}});return function(_,w){var $=_.length;for(_.sort(w);$--;)_[$]=_[$].value;return _}(d,function(_,w){return function($,I,O){for(var R=-1,P=$.criteria,X=I.criteria,tt=P.length,at=O.length;++R<tt;){var L=wl(P[R],X[R]);if(L)return R>=at?L:L*(O[R]=="desc"?-1:1)}return $.index-I.index}(_,w,c)})}function hl(e,i,c){for(var h=-1,d=i.length,_={};++h<d;){var w=i[h],$=ni(e,w);c($,w)&&Cs(_,Tr(w,e),$)}return _}function ec(e,i,c,h){var d=h?Si:nr,_=-1,w=i.length,$=e;for(e===i&&(i=We(i)),c&&($=zt(e,Qe(c)));++_<w;)for(var I=0,O=i[_],R=c?c(O):O;(I=d($,R,I,h))>-1;)$!==e&&yo.call($,I,1),yo.call(e,I,1);return e}function fl(e,i){for(var c=e?i.length:0,h=c-1;c--;){var d=i[c];if(c==h||d!==_){var _=d;cr(d)?yo.call(e,d,1):sc(e,d)}}return e}function nc(e,i){return e+wo(Vu()*(i-e+1))}function rc(e,i){var c="";if(!e||i<1||i>g)return c;do i%2&&(c+=e),(i=wo(i/2))&&(e+=e);while(i);return c}function kt(e,i){return bc(Ul(e,i,Ve),e+"")}function Dd(e){return Qu(zi(e))}function zd(e,i){var c=zi(e);return jo(c,ei(i,0,c.length))}function Cs(e,i,c,h){if(!Jt(e))return e;for(var d=-1,_=(i=Tr(i,e)).length,w=_-1,$=e;$!=null&&++d<_;){var I=Vn(i[d]),O=c;if(I==="__proto__"||I==="constructor"||I==="prototype")return e;if(d!=w){var R=$[I];(O=h?h(R,I,$):o)===o&&(O=Jt(R)?R:cr(i[d+1])?[]:{})}xs($,I,O),$=$[I]}return e}var dl=$o?function(e,i){return $o.set(e,i),e}:Ve,jd=bo?function(e,i){return bo(e,"toString",{configurable:!0,enumerable:!1,value:Ac(i),writable:!0})}:Ve;function qd(e){return jo(zi(e))}function gn(e,i,c){var h=-1,d=e.length;i<0&&(i=-i>d?0:d+i),(c=c>d?d:c)<0&&(c+=d),d=i>c?0:c-i>>>0,i>>>=0;for(var _=K(d);++h<d;)_[h]=e[h+i];return _}function Pd(e,i){var c;return Er(e,function(h,d,_){return!(c=i(h,d,_))}),!!c}function Ao(e,i,c){var h=0,d=e==null?h:e.length;if(typeof i=="number"&&i==i&&d<=2147483647){for(;h<d;){var _=h+d>>>1,w=e[_];w!==null&&!Ye(w)&&(c?w<=i:w<i)?h=_+1:d=_}return d}return ic(e,i,Ve,c)}function ic(e,i,c,h){var d=0,_=e==null?0:e.length;if(_===0)return 0;for(var w=(i=c(i))!=i,$=i===null,I=Ye(i),O=i===o;d<_;){var R=wo((d+_)/2),P=c(e[R]),X=P!==o,tt=P===null,at=P==P,L=Ye(P);if(w)var U=h||at;else U=O?at&&(h||X):$?at&&X&&(h||!tt):I?at&&X&&!tt&&(h||!L):!tt&&!L&&(h?P<=i:P<i);U?d=R+1:_=R}return Fe(_,4294967294)}function pl(e,i){for(var c=-1,h=e.length,d=0,_=[];++c<h;){var w=e[c],$=i?i(w):w;if(!c||!Cn($,I)){var I=$;_[d++]=w===0?0:w}}return _}function gl(e){return typeof e=="number"?e:Ye(e)?y:+e}function Ke(e){if(typeof e=="string")return e;if(xt(e))return zt(e,Ke)+"";if(Ye(e))return Gu?Gu.call(e):"";var i=e+"";return i=="0"&&1/e==-1/0?"-0":i}function Ar(e,i,c){var h=-1,d=Un,_=e.length,w=!0,$=[],I=$;if(c)w=!1,d=Cr;else if(_>=200){var O=i?null:Wd(e);if(O)return lo(O);w=!1,d=gs,I=new ti}else I=i?[]:$;t:for(;++h<_;){var R=e[h],P=i?i(R):R;if(R=c||R!==0?R:0,w&&P==P){for(var X=I.length;X--;)if(I[X]===P)continue t;i&&I.push(P),$.push(R)}else d(I,P,c)||(I!==$&&I.push(P),$.push(R))}return $}function sc(e,i){return(e=Wl(e,i=Tr(i,e)))==null||delete e[Vn(mn(i))]}function ml(e,i,c,h){return Cs(e,i,c(ni(e,i)),h)}function To(e,i,c,h){for(var d=e.length,_=h?d:-1;(h?_--:++_<d)&&i(e[_],_,e););return c?gn(e,h?0:_,h?_+1:d):gn(e,h?_+1:0,h?d:_)}function _l(e,i){var c=e;return c instanceof It&&(c=c.value()),wn(i,function(h,d){return d.func.apply(d.thisArg,Ze([h],d.args))},c)}function oc(e,i,c){var h=e.length;if(h<2)return h?Ar(e[0]):[];for(var d=-1,_=K(h);++d<h;)for(var w=e[d],$=-1;++$<h;)$!=d&&(_[d]=ws(_[d]||w,e[$],i,c));return Ar(Ee(_,1),i,c)}function vl(e,i,c){for(var h=-1,d=e.length,_=i.length,w={};++h<d;){var $=h<_?i[h]:o;c(w,e[h],$)}return w}function ac(e){return ie(e)?e:[]}function cc(e){return typeof e=="function"?e:Ve}function Tr(e,i){return xt(e)?e:_c(e,i)?[e]:Gl(Ot(e))}var Ud=kt;function Fr(e,i,c){var h=e.length;return c=c===o?h:c,!i&&c>=h?e:gn(e,i,c)}var yl=md||function(e){return Wt.clearTimeout(e)};function bl(e,i){if(i)return e.slice();var c=e.length,h=Pu?Pu(c):new e.constructor(c);return e.copy(h),h}function uc(e){var i=new e.constructor(e.byteLength);return new _o(i).set(new _o(e)),i}function xl(e,i){var c=i?uc(e.buffer):e.buffer;return new e.constructor(c,e.byteOffset,e.length)}function wl(e,i){if(e!==i){var c=e!==o,h=e===null,d=e==e,_=Ye(e),w=i!==o,$=i===null,I=i==i,O=Ye(i);if(!$&&!O&&!_&&e>i||_&&w&&I&&!$&&!O||h&&w&&I||!c&&I||!d)return 1;if(!h&&!_&&!O&&e<i||O&&c&&d&&!h&&!_||$&&c&&d||!w&&d||!I)return-1}return 0}function $l(e,i,c,h){for(var d=-1,_=e.length,w=c.length,$=-1,I=i.length,O=ge(_-w,0),R=K(I+O),P=!h;++$<I;)R[$]=i[$];for(;++d<w;)(P||d<_)&&(R[c[d]]=e[d]);for(;O--;)R[$++]=e[d++];return R}function kl(e,i,c,h){for(var d=-1,_=e.length,w=-1,$=c.length,I=-1,O=i.length,R=ge(_-$,0),P=K(R+O),X=!h;++d<R;)P[d]=e[d];for(var tt=d;++I<O;)P[tt+I]=i[I];for(;++w<$;)(X||d<_)&&(P[tt+c[w]]=e[d++]);return P}function We(e,i){var c=-1,h=e.length;for(i||(i=K(h));++c<h;)i[c]=e[c];return i}function Hn(e,i,c,h){var d=!c;c||(c={});for(var _=-1,w=i.length;++_<w;){var $=i[_],I=h?h(c[$],e[$],$,c,e):o;I===o&&(I=e[$]),d?sr(c,$,I):xs(c,$,I)}return c}function Fo(e,i){return function(c,h){var d=xt(c)?Pn:Fd,_=i?i():{};return d(c,e,ct(h,2),_)}}function Oi(e){return kt(function(i,c){var h=-1,d=c.length,_=d>1?c[d-1]:o,w=d>2?c[2]:o;for(_=e.length>3&&typeof _=="function"?(d--,_):o,w&&De(c[0],c[1],w)&&(_=d<3?o:_,d=1),i=Zt(i);++h<d;){var $=c[h];$&&e(i,$,h,_)}return i})}function Cl(e,i){return function(c,h){if(c==null)return c;if(!Be(c))return e(c,h);for(var d=c.length,_=i?d:-1,w=Zt(c);(i?_--:++_<d)&&h(w[_],_,w)!==!1;);return c}}function Sl(e){return function(i,c,h){for(var d=-1,_=Zt(i),w=h(i),$=w.length;$--;){var I=w[e?$:++d];if(c(_[I],I,_)===!1)break}return i}}function Il(e){return function(i){var c=Ii(i=Ot(i))?$n(i):o,h=c?c[0]:i.charAt(0),d=c?Fr(c,1).join(""):i.slice(1);return h[e]()+d}}function Li(e){return function(i){return wn($h(wh(i).replace(Qr,"")),e,"")}}function Ss(e){return function(){var i=arguments;switch(i.length){case 0:return new e;case 1:return new e(i[0]);case 2:return new e(i[0],i[1]);case 3:return new e(i[0],i[1],i[2]);case 4:return new e(i[0],i[1],i[2],i[3]);case 5:return new e(i[0],i[1],i[2],i[3],i[4]);case 6:return new e(i[0],i[1],i[2],i[3],i[4],i[5]);case 7:return new e(i[0],i[1],i[2],i[3],i[4],i[5],i[6])}var c=Ri(e.prototype),h=e.apply(c,i);return Jt(h)?h:c}}function Ml(e){return function(i,c,h){var d=Zt(i);if(!Be(i)){var _=ct(c,3);i=be(i),c=function($){return _(d[$],$,d)}}var w=e(i,c,h);return w>-1?d[_?i[w]:w]:o}}function El(e){return ar(function(i){var c=i.length,h=c,d=dn.prototype.thru;for(e&&i.reverse();h--;){var _=i[h];if(typeof _!="function")throw new fn(t);if(d&&!w&&Do(_)=="wrapper")var w=new dn([],!0)}for(h=w?h:c;++h<c;){var $=Do(_=i[h]),I=$=="wrapper"?pc(_):o;w=I&&vc(I[0])&&I[1]==424&&!I[4].length&&I[9]==1?w[Do(I[0])].apply(w,I[3]):_.length==1&&vc(_)?w[$]():w.thru(_)}return function(){var O=arguments,R=O[0];if(w&&O.length==1&&xt(R))return w.plant(R).value();for(var P=0,X=c?i[P].apply(this,O):R;++P<c;)X=i[P].call(this,X);return X}})}function Ro(e,i,c,h,d,_,w,$,I,O){var R=i&l,P=1&i,X=2&i,tt=24&i,at=512&i,L=X?o:Ss(e);return function U(){for(var lt=arguments.length,Q=K(lt),yt=lt;yt--;)Q[yt]=arguments[yt];if(tt)var gt=Ni(U),Tt=function(se,ze){for(var Qt=se.length,Oe=0;Qt--;)se[Qt]===ze&&++Oe;return Oe}(Q,gt);if(h&&(Q=$l(Q,h,d,tt)),_&&(Q=kl(Q,_,w,tt)),lt-=Tt,tt&&lt<O){var nt=Ir(Q,gt);return Fl(e,i,Ro,U.placeholder,c,Q,nt,$,I,O-lt)}var ht=P?c:this,me=X?ht[e]:e;return lt=Q.length,$?Q=function(se,ze){for(var Qt=se.length,Oe=Fe(ze.length,Qt),Gn=We(se);Oe--;){var Or=ze[Oe];se[Oe]=cr(Or,Qt)?Gn[Or]:o}return se}(Q,$):at&&lt>1&&Q.reverse(),R&&I<lt&&(Q.length=I),this&&this!==Wt&&this instanceof U&&(me=L||Ss(me)),me.apply(ht,Q)}}function Al(e,i){return function(c,h){return function(d,_,w,$){return Bn(d,function(I,O,R){_($,w(I),O,R)}),$}(c,e,i(h),{})}}function Oo(e,i){return function(c,h){var d;if(c===o&&h===o)return i;if(c!==o&&(d=c),h!==o){if(d===o)return h;typeof c=="string"||typeof h=="string"?(c=Ke(c),h=Ke(h)):(c=gl(c),h=gl(h)),d=e(c,h)}return d}}function lc(e){return ar(function(i){return i=zt(i,Qe(ct())),kt(function(c){var h=this;return e(i,function(d){return ye(d,h,c)})})})}function Lo(e,i){var c=(i=i===o?" ":Ke(i)).length;if(c<2)return c?rc(i,e):i;var h=rc(i,xo(e/Mi(i)));return Ii(i)?Fr($n(h),0,e).join(""):h.slice(0,e)}function Tl(e){return function(i,c,h){return h&&typeof h!="number"&&De(i,c,h)&&(c=h=o),i=lr(i),c===o?(c=i,i=0):c=lr(c),function(d,_,w,$){for(var I=-1,O=ge(xo((_-d)/(w||1)),0),R=K(O);O--;)R[$?O:++I]=d,d+=w;return R}(i,c,h=h===o?i<c?1:-1:lr(h),e)}}function No(e){return function(i,c){return typeof i=="string"&&typeof c=="string"||(i=_n(i),c=_n(c)),e(i,c)}}function Fl(e,i,c,h,d,_,w,$,I,O){var R=8&i;i|=R?a:u,4&(i&=~(R?u:a))||(i&=-4);var P=[e,i,d,R?_:o,R?w:o,R?o:_,R?o:w,$,I,O],X=c.apply(o,P);return vc(e)&&Bl(X,P),X.placeholder=h,Hl(X,e,i)}function hc(e){var i=Wn[e];return function(c,h){if(c=_n(c),(h=h==null?0:Fe($t(h),292))&&Hu(c)){var d=(Ot(c)+"e").split("e");return+((d=(Ot(i(d[0]+"e"+(+d[1]+h)))+"e").split("e"))[0]+"e"+(+d[1]-h))}return i(c)}}var Wd=Ti&&1/lo(new Ti([,-0]))[1]==m?function(e){return new Ti(e)}:Rc;function Rl(e){return function(i){var c=Re(i);return c==st?Pa(i):c==Ft?cd(i):function(h,d){return zt(d,function(_){return[_,h[_]]})}(i,e(i))}}function or(e,i,c,h,d,_,w,$){var I=2&i;if(!I&&typeof e!="function")throw new fn(t);var O=h?h.length:0;if(O||(i&=-97,h=d=o),w=w===o?w:ge($t(w),0),$=$===o?$:$t($),O-=d?d.length:0,i&u){var R=h,P=d;h=d=o}var X=I?o:pc(e),tt=[e,i,c,h,d,R,P,_,w,$];if(X&&function(L,U){var lt=L[1],Q=U[1],yt=lt|Q,gt=yt<131,Tt=Q==l&&lt==8||Q==l&&lt==f&&L[7].length<=U[8]||Q==384&&U[7].length<=U[8]&&lt==8;if(!gt&&!Tt)return L;1&Q&&(L[2]=U[2],yt|=1&lt?0:4);var nt=U[3];if(nt){var ht=L[3];L[3]=ht?$l(ht,nt,U[4]):nt,L[4]=ht?Ir(L[3],r):U[4]}(nt=U[5])&&(ht=L[5],L[5]=ht?kl(ht,nt,U[6]):nt,L[6]=ht?Ir(L[5],r):U[6]),(nt=U[7])&&(L[7]=nt),Q&l&&(L[8]=L[8]==null?U[8]:Fe(L[8],U[8])),L[9]==null&&(L[9]=U[9]),L[0]=U[0],L[1]=yt}(tt,X),e=tt[0],i=tt[1],c=tt[2],h=tt[3],d=tt[4],!($=tt[9]=tt[9]===o?I?0:e.length:ge(tt[9]-O,0))&&24&i&&(i&=-25),i&&i!=1)at=i==8||i==s?function(L,U,lt){var Q=Ss(L);return function yt(){for(var gt=arguments.length,Tt=K(gt),nt=gt,ht=Ni(yt);nt--;)Tt[nt]=arguments[nt];var me=gt<3&&Tt[0]!==ht&&Tt[gt-1]!==ht?[]:Ir(Tt,ht);return(gt-=me.length)<lt?Fl(L,U,Ro,yt.placeholder,o,Tt,me,o,o,lt-gt):ye(this&&this!==Wt&&this instanceof yt?Q:L,this,Tt)}}(e,i,$):i!=a&&i!=33||d.length?Ro.apply(o,tt):function(L,U,lt,Q){var yt=1&U,gt=Ss(L);return function Tt(){for(var nt=-1,ht=arguments.length,me=-1,se=Q.length,ze=K(se+ht),Qt=this&&this!==Wt&&this instanceof Tt?gt:L;++me<se;)ze[me]=Q[me];for(;ht--;)ze[me++]=arguments[++nt];return ye(Qt,yt?lt:this,ze)}}(e,i,c,h);else var at=function(L,U,lt){var Q=1&U,yt=Ss(L);return function gt(){return(this&&this!==Wt&&this instanceof gt?yt:L).apply(Q?lt:this,arguments)}}(e,i,c);return Hl((X?dl:Bl)(at,tt),e,i)}function Ol(e,i,c,h){return e===o||Cn(e,Ai[c])&&!Lt.call(h,c)?i:e}function Ll(e,i,c,h,d,_){return Jt(e)&&Jt(i)&&(_.set(i,e),Eo(e,i,o,Ll,_),_.delete(i)),e}function Bd(e){return Es(e)?o:e}function Nl(e,i,c,h,d,_){var w=1&c,$=e.length,I=i.length;if($!=I&&!(w&&I>$))return!1;var O=_.get(e),R=_.get(i);if(O&&R)return O==i&&R==e;var P=-1,X=!0,tt=2&c?new ti:o;for(_.set(e,i),_.set(i,e);++P<$;){var at=e[P],L=i[P];if(h)var U=w?h(L,at,P,i,e,_):h(at,L,P,e,i,_);if(U!==o){if(U)continue;X=!1;break}if(tt){if(!Sr(i,function(lt,Q){if(!gs(tt,Q)&&(at===lt||d(at,lt,c,h,_)))return tt.push(Q)})){X=!1;break}}else if(at!==L&&!d(at,L,c,h,_)){X=!1;break}}return _.delete(e),_.delete(i),X}function ar(e){return bc(Ul(e,o,Yl),e+"")}function fc(e){return nl(e,be,mc)}function dc(e){return nl(e,He,Dl)}var pc=$o?function(e){return $o.get(e)}:Rc;function Do(e){for(var i=e.name+"",c=Fi[i],h=Lt.call(Fi,i)?c.length:0;h--;){var d=c[h],_=d.func;if(_==null||_==e)return d.name}return i}function Ni(e){return(Lt.call(p,"placeholder")?p:e).placeholder}function ct(){var e=p.iteratee||Tc;return e=e===Tc?sl:e,arguments.length?e(arguments[0],arguments[1]):e}function zo(e,i){var c,h,d=e.__data__;return((h=typeof(c=i))=="string"||h=="number"||h=="symbol"||h=="boolean"?c!=="__proto__":c===null)?d[typeof i=="string"?"string":"hash"]:d.map}function gc(e){for(var i=be(e),c=i.length;c--;){var h=i[c],d=e[h];i[c]=[h,d,ql(d)]}return i}function ri(e,i){var c=function(h,d){return h==null?o:h[d]}(e,i);return il(c)?c:o}var mc=Wa?function(e){return e==null?[]:(e=Zt(e),Me(Wa(e),function(i){return Wu.call(e,i)}))}:Oc,Dl=Wa?function(e){for(var i=[];e;)Ze(i,mc(e)),e=vo(e);return i}:Oc,Re=Ne;function zl(e,i,c){for(var h=-1,d=(i=Tr(i,e)).length,_=!1;++h<d;){var w=Vn(i[h]);if(!(_=e!=null&&c(e,w)))break;e=e[w]}return _||++h!=d?_:!!(d=e==null?0:e.length)&&Ho(d)&&cr(w,d)&&(xt(e)||si(e))}function jl(e){return typeof e.constructor!="function"||Is(e)?{}:Ri(vo(e))}function Hd(e){return xt(e)||si(e)||!!(Bu&&e&&e[Bu])}function cr(e,i){var c=typeof e;return!!(i=i??g)&&(c=="number"||c!="symbol"&&Fa.test(e))&&e>-1&&e%1==0&&e<i}function De(e,i,c){if(!Jt(c))return!1;var h=typeof i;return!!(h=="number"?Be(c)&&cr(i,c.length):h=="string"&&i in c)&&Cn(c[i],e)}function _c(e,i){if(xt(e))return!1;var c=typeof e;return!(c!="number"&&c!="symbol"&&c!="boolean"&&e!=null&&!Ye(e))||Gs.test(e)||!cn.test(e)||i!=null&&e in Zt(i)}function vc(e){var i=Do(e),c=p[i];if(typeof c!="function"||!(i in It.prototype))return!1;if(e===c)return!0;var h=pc(c);return!!h&&e===h[0]}(Ba&&Re(new Ba(new ArrayBuffer(1)))!=he||_s&&Re(new _s)!=st||Ha&&Re(Ha.resolve())!=St||Ti&&Re(new Ti)!=Ft||vs&&Re(new vs)!=Ct)&&(Re=function(e){var i=Ne(e),c=i==pt?e.constructor:o,h=c?ii(c):"";if(h)switch(h){case Cd:return he;case Sd:return st;case Id:return St;case Md:return Ft;case Ed:return Ct}return i});var Vd=fo?ur:Lc;function Is(e){var i=e&&e.constructor;return e===(typeof i=="function"&&i.prototype||Ai)}function ql(e){return e==e&&!Jt(e)}function Pl(e,i){return function(c){return c!=null&&c[e]===i&&(i!==o||e in Zt(c))}}function Ul(e,i,c){return i=ge(i===o?e.length-1:i,0),function(){for(var h=arguments,d=-1,_=ge(h.length-i,0),w=K(_);++d<_;)w[d]=h[i+d];d=-1;for(var $=K(i+1);++d<i;)$[d]=h[d];return $[i]=c(w),ye(e,this,$)}}function Wl(e,i){return i.length<2?e:ni(e,gn(i,0,-1))}function yc(e,i){if((i!=="constructor"||typeof e[i]!="function")&&i!="__proto__")return e[i]}var Bl=Vl(dl),Ms=vd||function(e,i){return Wt.setTimeout(e,i)},bc=Vl(jd);function Hl(e,i,c){var h=i+"";return bc(e,function(d,_){var w=_.length;if(!w)return d;var $=w-1;return _[$]=(w>1?"& ":"")+_[$],_=_.join(w>2?", ":" "),d.replace(Ys,`{
/* [wrapped with `+_+`] */
`)}(h,function(d,_){return Te(k,function(w){var $="_."+w[0];_&w[1]&&!Un(d,$)&&d.push($)}),d.sort()}(function(d){var _=d.match(Ea);return _?_[1].split(Xn):[]}(h),c)))}function Vl(e){var i=0,c=0;return function(){var h=wd(),d=16-(h-c);if(c=h,d>0){if(++i>=800)return arguments[0]}else i=0;return e.apply(o,arguments)}}function jo(e,i){var c=-1,h=e.length,d=h-1;for(i=i===o?h:i;++c<i;){var _=nc(c,d),w=e[_];e[_]=e[c],e[c]=w}return e.length=i,e}var Gl=function(e){var i=Wo(e,function(h){return c.size===500&&c.clear(),h}),c=i.cache;return i}(function(e){var i=[];return e.charCodeAt(0)===46&&i.push(""),e.replace(Zs,function(c,h,d,_){i.push(d?_.replace(Ta,"$1"):h||c)}),i});function Vn(e){if(typeof e=="string"||Ye(e))return e;var i=e+"";return i=="0"&&1/e==-1/0?"-0":i}function ii(e){if(e!=null){try{return po.call(e)}catch{}try{return e+""}catch{}}return""}function Zl(e){if(e instanceof It)return e.clone();var i=new dn(e.__wrapped__,e.__chain__);return i.__actions__=We(e.__actions__),i.__index__=e.__index__,i.__values__=e.__values__,i}var Gd=kt(function(e,i){return ie(e)?ws(e,Ee(i,1,ie,!0)):[]}),Zd=kt(function(e,i){var c=mn(i);return ie(c)&&(c=o),ie(e)?ws(e,Ee(i,1,ie,!0),ct(c,2)):[]}),Qd=kt(function(e,i){var c=mn(i);return ie(c)&&(c=o),ie(e)?ws(e,Ee(i,1,ie,!0),o,c):[]});function Ql(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var d=c==null?0:$t(c);return d<0&&(d=ge(h+d,0)),er(e,ct(i,3),d)}function Kl(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var d=h-1;return c!==o&&(d=$t(c),d=c<0?ge(h+d,0):Fe(d,h-1)),er(e,ct(i,3),d,!0)}function Yl(e){return e!=null&&e.length?Ee(e,1):[]}function Xl(e){return e&&e.length?e[0]:o}var Kd=kt(function(e){var i=zt(e,ac);return i.length&&i[0]===e[0]?Ya(i):[]}),Yd=kt(function(e){var i=mn(e),c=zt(e,ac);return i===mn(c)?i=o:c.pop(),c.length&&c[0]===e[0]?Ya(c,ct(i,2)):[]}),Xd=kt(function(e){var i=mn(e),c=zt(e,ac);return(i=typeof i=="function"?i:o)&&c.pop(),c.length&&c[0]===e[0]?Ya(c,o,i):[]});function mn(e){var i=e==null?0:e.length;return i?e[i-1]:o}var Jd=kt(Jl);function Jl(e,i){return e&&e.length&&i&&i.length?ec(e,i):e}var tp=ar(function(e,i){var c=e==null?0:e.length,h=Ga(e,i);return fl(e,zt(i,function(d){return cr(d,c)?+d:d}).sort(wl)),h});function xc(e){return e==null?e:kd.call(e)}var ep=kt(function(e){return Ar(Ee(e,1,ie,!0))}),np=kt(function(e){var i=mn(e);return ie(i)&&(i=o),Ar(Ee(e,1,ie,!0),ct(i,2))}),rp=kt(function(e){var i=mn(e);return i=typeof i=="function"?i:o,Ar(Ee(e,1,ie,!0),o,i)});function wc(e){if(!e||!e.length)return[];var i=0;return e=Me(e,function(c){if(ie(c))return i=ge(c.length,i),!0}),ja(i,function(c){return zt(e,Na(c))})}function th(e,i){if(!e||!e.length)return[];var c=wc(e);return i==null?c:zt(c,function(h){return ye(i,o,h)})}var ip=kt(function(e,i){return ie(e)?ws(e,i):[]}),sp=kt(function(e){return oc(Me(e,ie))}),op=kt(function(e){var i=mn(e);return ie(i)&&(i=o),oc(Me(e,ie),ct(i,2))}),ap=kt(function(e){var i=mn(e);return i=typeof i=="function"?i:o,oc(Me(e,ie),o,i)}),cp=kt(wc),up=kt(function(e){var i=e.length,c=i>1?e[i-1]:o;return c=typeof c=="function"?(e.pop(),c):o,th(e,c)});function eh(e){var i=p(e);return i.__chain__=!0,i}function qo(e,i){return i(e)}var lp=ar(function(e){var i=e.length,c=i?e[0]:0,h=this.__wrapped__,d=function(_){return Ga(_,e)};return!(i>1||this.__actions__.length)&&h instanceof It&&cr(c)?((h=h.slice(c,+c+(i?1:0))).__actions__.push({func:qo,args:[d],thisArg:o}),new dn(h,this.__chain__).thru(function(_){return i&&!_.length&&_.push(o),_})):this.thru(d)}),hp=Fo(function(e,i,c){Lt.call(e,c)?++e[c]:sr(e,c,1)}),fp=Ml(Ql),dp=Ml(Kl);function nh(e,i){return(xt(e)?Te:Er)(e,ct(i,3))}function rh(e,i){return(xt(e)?Ue:Ju)(e,ct(i,3))}var pp=Fo(function(e,i,c){Lt.call(e,c)?e[c].push(i):sr(e,c,[i])}),gp=kt(function(e,i,c){var h=-1,d=typeof i=="function",_=Be(e)?K(e.length):[];return Er(e,function(w){_[++h]=d?ye(i,w,c):$s(w,i,c)}),_}),mp=Fo(function(e,i,c){sr(e,c,i)});function Po(e,i){return(xt(e)?zt:ol)(e,ct(i,3))}var _p=Fo(function(e,i,c){e[c?0:1].push(i)},function(){return[[],[]]}),vp=kt(function(e,i){if(e==null)return[];var c=i.length;return c>1&&De(e,i[0],i[1])?i=[]:c>2&&De(i[0],i[1],i[2])&&(i=[i[0]]),ll(e,Ee(i,1),[])}),Uo=_d||function(){return Wt.Date.now()};function ih(e,i,c){return i=c?o:i,i=e&&i==null?e.length:i,or(e,l,o,o,o,o,i)}function sh(e,i){var c;if(typeof i!="function")throw new fn(t);return e=$t(e),function(){return--e>0&&(c=i.apply(this,arguments)),e<=1&&(i=o),c}}var $c=kt(function(e,i,c){var h=1;if(c.length){var d=Ir(c,Ni($c));h|=a}return or(e,h,i,c,d)}),oh=kt(function(e,i,c){var h=3;if(c.length){var d=Ir(c,Ni(oh));h|=a}return or(i,h,e,c,d)});function ah(e,i,c){var h,d,_,w,$,I,O=0,R=!1,P=!1,X=!0;if(typeof e!="function")throw new fn(t);function tt(Q){var yt=h,gt=d;return h=d=o,O=Q,w=e.apply(gt,yt)}function at(Q){var yt=Q-I;return I===o||yt>=i||yt<0||P&&Q-O>=_}function L(){var Q=Uo();if(at(Q))return U(Q);$=Ms(L,function(yt){var gt=i-(yt-I);return P?Fe(gt,_-(yt-O)):gt}(Q))}function U(Q){return $=o,X&&h?tt(Q):(h=d=o,w)}function lt(){var Q=Uo(),yt=at(Q);if(h=arguments,d=this,I=Q,yt){if($===o)return function(gt){return O=gt,$=Ms(L,i),R?tt(gt):w}(I);if(P)return yl($),$=Ms(L,i),tt(I)}return $===o&&($=Ms(L,i)),w}return i=_n(i)||0,Jt(c)&&(R=!!c.leading,_=(P="maxWait"in c)?ge(_n(c.maxWait)||0,i):_,X="trailing"in c?!!c.trailing:X),lt.cancel=function(){$!==o&&yl($),O=0,h=I=d=$=o},lt.flush=function(){return $===o?w:U(Uo())},lt}var yp=kt(function(e,i){return Xu(e,1,i)}),bp=kt(function(e,i,c){return Xu(e,_n(i)||0,c)});function Wo(e,i){if(typeof e!="function"||i!=null&&typeof i!="function")throw new fn(t);var c=function(){var h=arguments,d=i?i.apply(this,h):h[0],_=c.cache;if(_.has(d))return _.get(d);var w=e.apply(this,h);return c.cache=_.set(d,w)||_,w};return c.cache=new(Wo.Cache||ir),c}function Bo(e){if(typeof e!="function")throw new fn(t);return function(){var i=arguments;switch(i.length){case 0:return!e.call(this);case 1:return!e.call(this,i[0]);case 2:return!e.call(this,i[0],i[1]);case 3:return!e.call(this,i[0],i[1],i[2])}return!e.apply(this,i)}}Wo.Cache=ir;var xp=Ud(function(e,i){var c=(i=i.length==1&&xt(i[0])?zt(i[0],Qe(ct())):zt(Ee(i,1),Qe(ct()))).length;return kt(function(h){for(var d=-1,_=Fe(h.length,c);++d<_;)h[d]=i[d].call(this,h[d]);return ye(e,this,h)})}),kc=kt(function(e,i){var c=Ir(i,Ni(kc));return or(e,a,o,i,c)}),ch=kt(function(e,i){var c=Ir(i,Ni(ch));return or(e,u,o,i,c)}),wp=ar(function(e,i){return or(e,f,o,o,o,i)});function Cn(e,i){return e===i||e!=e&&i!=i}var $p=No(Ka),kp=No(function(e,i){return e>=i}),si=rl(function(){return arguments}())?rl:function(e){return ne(e)&&Lt.call(e,"callee")&&!Wu.call(e,"callee")},xt=K.isArray,Cp=kr?Qe(kr):function(e){return ne(e)&&Ne(e)==wt};function Be(e){return e!=null&&Ho(e.length)&&!ur(e)}function ie(e){return ne(e)&&Be(e)}var Rr=yd||Lc,Sp=ds?Qe(ds):function(e){return ne(e)&&Ne(e)==S};function Cc(e){if(!ne(e))return!1;var i=Ne(e);return i==E||i=="[object DOMException]"||typeof e.message=="string"&&typeof e.name=="string"&&!Es(e)}function ur(e){if(!Jt(e))return!1;var i=Ne(e);return i==H||i==ot||i=="[object AsyncFunction]"||i=="[object Proxy]"}function uh(e){return typeof e=="number"&&e==$t(e)}function Ho(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=g}function Jt(e){var i=typeof e;return e!=null&&(i=="object"||i=="function")}function ne(e){return e!=null&&typeof e=="object"}var lh=ao?Qe(ao):function(e){return ne(e)&&Re(e)==st};function hh(e){return typeof e=="number"||ne(e)&&Ne(e)==Dt}function Es(e){if(!ne(e)||Ne(e)!=pt)return!1;var i=vo(e);if(i===null)return!0;var c=Lt.call(i,"constructor")&&i.constructor;return typeof c=="function"&&c instanceof c&&po.call(c)==dd}var Sc=co?Qe(co):function(e){return ne(e)&&Ne(e)==mt},fh=Rt?Qe(Rt):function(e){return ne(e)&&Re(e)==Ft};function Vo(e){return typeof e=="string"||!xt(e)&&ne(e)&&Ne(e)==Ht}function Ye(e){return typeof e=="symbol"||ne(e)&&Ne(e)==ce}var Di=At?Qe(At):function(e){return ne(e)&&Ho(e.length)&&!!J[Ne(e)]},Ip=No(tc),Mp=No(function(e,i){return e<=i});function dh(e){if(!e)return[];if(Be(e))return Vo(e)?$n(e):We(e);if(ms&&e[ms])return function(c){for(var h,d=[];!(h=c.next()).done;)d.push(h.value);return d}(e[ms]());var i=Re(e);return(i==st?Pa:i==Ft?lo:zi)(e)}function lr(e){return e?(e=_n(e))===m||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:e===0?e:0}function $t(e){var i=lr(e),c=i%1;return i==i?c?i-c:i:0}function ph(e){return e?ei($t(e),0,b):0}function _n(e){if(typeof e=="number")return e;if(Ye(e))return y;if(Jt(e)){var i=typeof e.valueOf=="function"?e.valueOf():e;e=Jt(i)?i+"":i}if(typeof e!="string")return e===0?e:+e;e=Lu(e);var c=ss.test(e);return c||Hr.test(e)?re(e.slice(2),c?2:8):is.test(e)?y:+e}function gh(e){return Hn(e,He(e))}function Ot(e){return e==null?"":Ke(e)}var Ep=Oi(function(e,i){if(Is(i)||Be(i))Hn(i,be(i),e);else for(var c in i)Lt.call(i,c)&&xs(e,c,i[c])}),mh=Oi(function(e,i){Hn(i,He(i),e)}),Go=Oi(function(e,i,c,h){Hn(i,He(i),e,h)}),Ap=Oi(function(e,i,c,h){Hn(i,be(i),e,h)}),Tp=ar(Ga),Fp=kt(function(e,i){e=Zt(e);var c=-1,h=i.length,d=h>2?i[2]:o;for(d&&De(i[0],i[1],d)&&(h=1);++c<h;)for(var _=i[c],w=He(_),$=-1,I=w.length;++$<I;){var O=w[$],R=e[O];(R===o||Cn(R,Ai[O])&&!Lt.call(e,O))&&(e[O]=_[O])}return e}),Rp=kt(function(e){return e.push(o,Ll),ye(_h,o,e)});function Ic(e,i,c){var h=e==null?o:ni(e,i);return h===o?c:h}function Mc(e,i){return e!=null&&zl(e,i,Ld)}var Op=Al(function(e,i,c){i!=null&&typeof i.toString!="function"&&(i=go.call(i)),e[i]=c},Ac(Ve)),Lp=Al(function(e,i,c){i!=null&&typeof i.toString!="function"&&(i=go.call(i)),Lt.call(e,i)?e[i].push(c):e[i]=[c]},ct),Np=kt($s);function be(e){return Be(e)?Zu(e):Ja(e)}function He(e){return Be(e)?Zu(e,!0):Nd(e)}var Dp=Oi(function(e,i,c){Eo(e,i,c)}),_h=Oi(function(e,i,c,h){Eo(e,i,c,h)}),zp=ar(function(e,i){var c={};if(e==null)return c;var h=!1;i=zt(i,function(_){return _=Tr(_,e),h||(h=_.length>1),_}),Hn(e,dc(e),c),h&&(c=pn(c,7,Bd));for(var d=i.length;d--;)sc(c,i[d]);return c}),jp=ar(function(e,i){return e==null?{}:function(c,h){return hl(c,h,function(d,_){return Mc(c,_)})}(e,i)});function vh(e,i){if(e==null)return{};var c=zt(dc(e),function(h){return[h]});return i=ct(i),hl(e,c,function(h,d){return i(h,d[0])})}var yh=Rl(be),bh=Rl(He);function zi(e){return e==null?[]:qa(e,be(e))}var qp=Li(function(e,i,c){return i=i.toLowerCase(),e+(c?xh(i):i)});function xh(e){return Ec(Ot(e).toLowerCase())}function wh(e){return(e=Ot(e))&&e.replace(Ra,sd).replace(oo,"")}var Pp=Li(function(e,i,c){return e+(c?"-":"")+i.toLowerCase()}),Up=Li(function(e,i,c){return e+(c?" ":"")+i.toLowerCase()}),Wp=Il("toLowerCase"),Bp=Li(function(e,i,c){return e+(c?"_":"")+i.toLowerCase()}),Hp=Li(function(e,i,c){return e+(c?" ":"")+Ec(i)}),Vp=Li(function(e,i,c){return e+(c?" ":"")+i.toUpperCase()}),Ec=Il("toUpperCase");function $h(e,i,c){return e=Ot(e),(i=c?o:i)===o?function(h){return W.test(h)}(e)?function(h){return h.match(C)||[]}(e):function(h){return h.match(Xs)||[]}(e):e.match(i)||[]}var kh=kt(function(e,i){try{return ye(e,o,i)}catch(c){return Cc(c)?c:new Mt(c)}}),Gp=ar(function(e,i){return Te(i,function(c){c=Vn(c),sr(e,c,$c(e[c],e))}),e});function Ac(e){return function(){return e}}var Zp=El(),Qp=El(!0);function Ve(e){return e}function Tc(e){return sl(typeof e=="function"?e:pn(e,1))}var Kp=kt(function(e,i){return function(c){return $s(c,e,i)}}),Yp=kt(function(e,i){return function(c){return $s(e,c,i)}});function Fc(e,i,c){var h=be(i),d=Mo(i,h);c!=null||Jt(i)&&(d.length||!h.length)||(c=i,i=e,e=this,d=Mo(i,be(i)));var _=!(Jt(c)&&"chain"in c&&!c.chain),w=ur(e);return Te(d,function($){var I=i[$];e[$]=I,w&&(e.prototype[$]=function(){var O=this.__chain__;if(_||O){var R=e(this.__wrapped__);return(R.__actions__=We(this.__actions__)).push({func:I,args:arguments,thisArg:e}),R.__chain__=O,R}return I.apply(e,Ze([this.value()],arguments))})}),e}function Rc(){}var Xp=lc(zt),Jp=lc(hn),tg=lc(Sr);function Ch(e){return _c(e)?Na(Vn(e)):function(i){return function(c){return ni(c,i)}}(e)}var eg=Tl(),ng=Tl(!0);function Oc(){return[]}function Lc(){return!1}var Nc,rg=Oo(function(e,i){return e+i},0),ig=hc("ceil"),sg=Oo(function(e,i){return e/i},1),og=hc("floor"),ag=Oo(function(e,i){return e*i},1),cg=hc("round"),ug=Oo(function(e,i){return e-i},0);return p.after=function(e,i){if(typeof i!="function")throw new fn(t);return e=$t(e),function(){if(--e<1)return i.apply(this,arguments)}},p.ary=ih,p.assign=Ep,p.assignIn=mh,p.assignInWith=Go,p.assignWith=Ap,p.at=Tp,p.before=sh,p.bind=$c,p.bindAll=Gp,p.bindKey=oh,p.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return xt(e)?e:[e]},p.chain=eh,p.chunk=function(e,i,c){i=(c?De(e,i,c):i===o)?1:ge($t(i),0);var h=e==null?0:e.length;if(!h||i<1)return[];for(var d=0,_=0,w=K(xo(h/i));d<h;)w[_++]=gn(e,d,d+=i);return w},p.compact=function(e){for(var i=-1,c=e==null?0:e.length,h=0,d=[];++i<c;){var _=e[i];_&&(d[h++]=_)}return d},p.concat=function(){var e=arguments.length;if(!e)return[];for(var i=K(e-1),c=arguments[0],h=e;h--;)i[h-1]=arguments[h];return Ze(xt(c)?We(c):[c],Ee(i,1))},p.cond=function(e){var i=e==null?0:e.length,c=ct();return e=i?zt(e,function(h){if(typeof h[1]!="function")throw new fn(t);return[c(h[0]),h[1]]}):[],kt(function(h){for(var d=-1;++d<i;){var _=e[d];if(ye(_[0],this,h))return ye(_[1],this,h)}})},p.conforms=function(e){return function(i){var c=be(i);return function(h){return Yu(h,i,c)}}(pn(e,1))},p.constant=Ac,p.countBy=hp,p.create=function(e,i){var c=Ri(e);return i==null?c:Ku(c,i)},p.curry=function e(i,c,h){var d=or(i,8,o,o,o,o,o,c=h?o:c);return d.placeholder=e.placeholder,d},p.curryRight=function e(i,c,h){var d=or(i,s,o,o,o,o,o,c=h?o:c);return d.placeholder=e.placeholder,d},p.debounce=ah,p.defaults=Fp,p.defaultsDeep=Rp,p.defer=yp,p.delay=bp,p.difference=Gd,p.differenceBy=Zd,p.differenceWith=Qd,p.drop=function(e,i,c){var h=e==null?0:e.length;return h?gn(e,(i=c||i===o?1:$t(i))<0?0:i,h):[]},p.dropRight=function(e,i,c){var h=e==null?0:e.length;return h?gn(e,0,(i=h-(i=c||i===o?1:$t(i)))<0?0:i):[]},p.dropRightWhile=function(e,i){return e&&e.length?To(e,ct(i,3),!0,!0):[]},p.dropWhile=function(e,i){return e&&e.length?To(e,ct(i,3),!0):[]},p.fill=function(e,i,c,h){var d=e==null?0:e.length;return d?(c&&typeof c!="number"&&De(e,i,c)&&(c=0,h=d),function(_,w,$,I){var O=_.length;for(($=$t($))<0&&($=-$>O?0:O+$),(I=I===o||I>O?O:$t(I))<0&&(I+=O),I=$>I?0:ph(I);$<I;)_[$++]=w;return _}(e,i,c,h)):[]},p.filter=function(e,i){return(xt(e)?Me:tl)(e,ct(i,3))},p.flatMap=function(e,i){return Ee(Po(e,i),1)},p.flatMapDeep=function(e,i){return Ee(Po(e,i),m)},p.flatMapDepth=function(e,i,c){return c=c===o?1:$t(c),Ee(Po(e,i),c)},p.flatten=Yl,p.flattenDeep=function(e){return e!=null&&e.length?Ee(e,m):[]},p.flattenDepth=function(e,i){return e!=null&&e.length?Ee(e,i=i===o?1:$t(i)):[]},p.flip=function(e){return or(e,512)},p.flow=Zp,p.flowRight=Qp,p.fromPairs=function(e){for(var i=-1,c=e==null?0:e.length,h={};++i<c;){var d=e[i];h[d[0]]=d[1]}return h},p.functions=function(e){return e==null?[]:Mo(e,be(e))},p.functionsIn=function(e){return e==null?[]:Mo(e,He(e))},p.groupBy=pp,p.initial=function(e){return e!=null&&e.length?gn(e,0,-1):[]},p.intersection=Kd,p.intersectionBy=Yd,p.intersectionWith=Xd,p.invert=Op,p.invertBy=Lp,p.invokeMap=gp,p.iteratee=Tc,p.keyBy=mp,p.keys=be,p.keysIn=He,p.map=Po,p.mapKeys=function(e,i){var c={};return i=ct(i,3),Bn(e,function(h,d,_){sr(c,i(h,d,_),h)}),c},p.mapValues=function(e,i){var c={};return i=ct(i,3),Bn(e,function(h,d,_){sr(c,d,i(h,d,_))}),c},p.matches=function(e){return al(pn(e,1))},p.matchesProperty=function(e,i){return cl(e,pn(i,1))},p.memoize=Wo,p.merge=Dp,p.mergeWith=_h,p.method=Kp,p.methodOf=Yp,p.mixin=Fc,p.negate=Bo,p.nthArg=function(e){return e=$t(e),kt(function(i){return ul(i,e)})},p.omit=zp,p.omitBy=function(e,i){return vh(e,Bo(ct(i)))},p.once=function(e){return sh(2,e)},p.orderBy=function(e,i,c,h){return e==null?[]:(xt(i)||(i=i==null?[]:[i]),xt(c=h?o:c)||(c=c==null?[]:[c]),ll(e,i,c))},p.over=Xp,p.overArgs=xp,p.overEvery=Jp,p.overSome=tg,p.partial=kc,p.partialRight=ch,p.partition=_p,p.pick=jp,p.pickBy=vh,p.property=Ch,p.propertyOf=function(e){return function(i){return e==null?o:ni(e,i)}},p.pull=Jd,p.pullAll=Jl,p.pullAllBy=function(e,i,c){return e&&e.length&&i&&i.length?ec(e,i,ct(c,2)):e},p.pullAllWith=function(e,i,c){return e&&e.length&&i&&i.length?ec(e,i,o,c):e},p.pullAt=tp,p.range=eg,p.rangeRight=ng,p.rearg=wp,p.reject=function(e,i){return(xt(e)?Me:tl)(e,Bo(ct(i,3)))},p.remove=function(e,i){var c=[];if(!e||!e.length)return c;var h=-1,d=[],_=e.length;for(i=ct(i,3);++h<_;){var w=e[h];i(w,h,e)&&(c.push(w),d.push(h))}return fl(e,d),c},p.rest=function(e,i){if(typeof e!="function")throw new fn(t);return kt(e,i=i===o?i:$t(i))},p.reverse=xc,p.sampleSize=function(e,i,c){return i=(c?De(e,i,c):i===o)?1:$t(i),(xt(e)?Ad:zd)(e,i)},p.set=function(e,i,c){return e==null?e:Cs(e,i,c)},p.setWith=function(e,i,c,h){return h=typeof h=="function"?h:o,e==null?e:Cs(e,i,c,h)},p.shuffle=function(e){return(xt(e)?Td:qd)(e)},p.slice=function(e,i,c){var h=e==null?0:e.length;return h?(c&&typeof c!="number"&&De(e,i,c)?(i=0,c=h):(i=i==null?0:$t(i),c=c===o?h:$t(c)),gn(e,i,c)):[]},p.sortBy=vp,p.sortedUniq=function(e){return e&&e.length?pl(e):[]},p.sortedUniqBy=function(e,i){return e&&e.length?pl(e,ct(i,2)):[]},p.split=function(e,i,c){return c&&typeof c!="number"&&De(e,i,c)&&(i=c=o),(c=c===o?b:c>>>0)?(e=Ot(e))&&(typeof i=="string"||i!=null&&!Sc(i))&&!(i=Ke(i))&&Ii(e)?Fr($n(e),0,c):e.split(i,c):[]},p.spread=function(e,i){if(typeof e!="function")throw new fn(t);return i=i==null?0:ge($t(i),0),kt(function(c){var h=c[i],d=Fr(c,0,i);return h&&Ze(d,h),ye(e,this,d)})},p.tail=function(e){var i=e==null?0:e.length;return i?gn(e,1,i):[]},p.take=function(e,i,c){return e&&e.length?gn(e,0,(i=c||i===o?1:$t(i))<0?0:i):[]},p.takeRight=function(e,i,c){var h=e==null?0:e.length;return h?gn(e,(i=h-(i=c||i===o?1:$t(i)))<0?0:i,h):[]},p.takeRightWhile=function(e,i){return e&&e.length?To(e,ct(i,3),!1,!0):[]},p.takeWhile=function(e,i){return e&&e.length?To(e,ct(i,3)):[]},p.tap=function(e,i){return i(e),e},p.throttle=function(e,i,c){var h=!0,d=!0;if(typeof e!="function")throw new fn(t);return Jt(c)&&(h="leading"in c?!!c.leading:h,d="trailing"in c?!!c.trailing:d),ah(e,i,{leading:h,maxWait:i,trailing:d})},p.thru=qo,p.toArray=dh,p.toPairs=yh,p.toPairsIn=bh,p.toPath=function(e){return xt(e)?zt(e,Vn):Ye(e)?[e]:We(Gl(Ot(e)))},p.toPlainObject=gh,p.transform=function(e,i,c){var h=xt(e),d=h||Rr(e)||Di(e);if(i=ct(i,4),c==null){var _=e&&e.constructor;c=d?h?new _:[]:Jt(e)&&ur(_)?Ri(vo(e)):{}}return(d?Te:Bn)(e,function(w,$,I){return i(c,w,$,I)}),c},p.unary=function(e){return ih(e,1)},p.union=ep,p.unionBy=np,p.unionWith=rp,p.uniq=function(e){return e&&e.length?Ar(e):[]},p.uniqBy=function(e,i){return e&&e.length?Ar(e,ct(i,2)):[]},p.uniqWith=function(e,i){return i=typeof i=="function"?i:o,e&&e.length?Ar(e,o,i):[]},p.unset=function(e,i){return e==null||sc(e,i)},p.unzip=wc,p.unzipWith=th,p.update=function(e,i,c){return e==null?e:ml(e,i,cc(c))},p.updateWith=function(e,i,c,h){return h=typeof h=="function"?h:o,e==null?e:ml(e,i,cc(c),h)},p.values=zi,p.valuesIn=function(e){return e==null?[]:qa(e,He(e))},p.without=ip,p.words=$h,p.wrap=function(e,i){return kc(cc(i),e)},p.xor=sp,p.xorBy=op,p.xorWith=ap,p.zip=cp,p.zipObject=function(e,i){return vl(e||[],i||[],xs)},p.zipObjectDeep=function(e,i){return vl(e||[],i||[],Cs)},p.zipWith=up,p.entries=yh,p.entriesIn=bh,p.extend=mh,p.extendWith=Go,Fc(p,p),p.add=rg,p.attempt=kh,p.camelCase=qp,p.capitalize=xh,p.ceil=ig,p.clamp=function(e,i,c){return c===o&&(c=i,i=o),c!==o&&(c=(c=_n(c))==c?c:0),i!==o&&(i=(i=_n(i))==i?i:0),ei(_n(e),i,c)},p.clone=function(e){return pn(e,4)},p.cloneDeep=function(e){return pn(e,5)},p.cloneDeepWith=function(e,i){return pn(e,5,i=typeof i=="function"?i:o)},p.cloneWith=function(e,i){return pn(e,4,i=typeof i=="function"?i:o)},p.conformsTo=function(e,i){return i==null||Yu(e,i,be(i))},p.deburr=wh,p.defaultTo=function(e,i){return e==null||e!=e?i:e},p.divide=sg,p.endsWith=function(e,i,c){e=Ot(e),i=Ke(i);var h=e.length,d=c=c===o?h:ei($t(c),0,h);return(c-=i.length)>=0&&e.slice(c,d)==i},p.eq=Cn,p.escape=function(e){return(e=Ot(e))&&Ma.test(e)?e.replace(Ji,od):e},p.escapeRegExp=function(e){return(e=Ot(e))&&Qs.test(e)?e.replace(ns,"\\$&"):e},p.every=function(e,i,c){var h=xt(e)?hn:Rd;return c&&De(e,i,c)&&(i=o),h(e,ct(i,3))},p.find=fp,p.findIndex=Ql,p.findKey=function(e,i){return Kr(e,ct(i,3),Bn)},p.findLast=dp,p.findLastIndex=Kl,p.findLastKey=function(e,i){return Kr(e,ct(i,3),Qa)},p.floor=og,p.forEach=nh,p.forEachRight=rh,p.forIn=function(e,i){return e==null?e:Za(e,ct(i,3),He)},p.forInRight=function(e,i){return e==null?e:el(e,ct(i,3),He)},p.forOwn=function(e,i){return e&&Bn(e,ct(i,3))},p.forOwnRight=function(e,i){return e&&Qa(e,ct(i,3))},p.get=Ic,p.gt=$p,p.gte=kp,p.has=function(e,i){return e!=null&&zl(e,i,Od)},p.hasIn=Mc,p.head=Xl,p.identity=Ve,p.includes=function(e,i,c,h){e=Be(e)?e:zi(e),c=c&&!h?$t(c):0;var d=e.length;return c<0&&(c=ge(d+c,0)),Vo(e)?c<=d&&e.indexOf(i,c)>-1:!!d&&nr(e,i,c)>-1},p.indexOf=function(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var d=c==null?0:$t(c);return d<0&&(d=ge(h+d,0)),nr(e,i,d)},p.inRange=function(e,i,c){return i=lr(i),c===o?(c=i,i=0):c=lr(c),function(h,d,_){return h>=Fe(d,_)&&h<ge(d,_)}(e=_n(e),i,c)},p.invoke=Np,p.isArguments=si,p.isArray=xt,p.isArrayBuffer=Cp,p.isArrayLike=Be,p.isArrayLikeObject=ie,p.isBoolean=function(e){return e===!0||e===!1||ne(e)&&Ne(e)==q},p.isBuffer=Rr,p.isDate=Sp,p.isElement=function(e){return ne(e)&&e.nodeType===1&&!Es(e)},p.isEmpty=function(e){if(e==null)return!0;if(Be(e)&&(xt(e)||typeof e=="string"||typeof e.splice=="function"||Rr(e)||Di(e)||si(e)))return!e.length;var i=Re(e);if(i==st||i==Ft)return!e.size;if(Is(e))return!Ja(e).length;for(var c in e)if(Lt.call(e,c))return!1;return!0},p.isEqual=function(e,i){return ks(e,i)},p.isEqualWith=function(e,i,c){var h=(c=typeof c=="function"?c:o)?c(e,i):o;return h===o?ks(e,i,o,c):!!h},p.isError=Cc,p.isFinite=function(e){return typeof e=="number"&&Hu(e)},p.isFunction=ur,p.isInteger=uh,p.isLength=Ho,p.isMap=lh,p.isMatch=function(e,i){return e===i||Xa(e,i,gc(i))},p.isMatchWith=function(e,i,c){return c=typeof c=="function"?c:o,Xa(e,i,gc(i),c)},p.isNaN=function(e){return hh(e)&&e!=+e},p.isNative=function(e){if(Vd(e))throw new Mt("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return il(e)},p.isNil=function(e){return e==null},p.isNull=function(e){return e===null},p.isNumber=hh,p.isObject=Jt,p.isObjectLike=ne,p.isPlainObject=Es,p.isRegExp=Sc,p.isSafeInteger=function(e){return uh(e)&&e>=-9007199254740991&&e<=g},p.isSet=fh,p.isString=Vo,p.isSymbol=Ye,p.isTypedArray=Di,p.isUndefined=function(e){return e===o},p.isWeakMap=function(e){return ne(e)&&Re(e)==Ct},p.isWeakSet=function(e){return ne(e)&&Ne(e)=="[object WeakSet]"},p.join=function(e,i){return e==null?"":bd.call(e,i)},p.kebabCase=Pp,p.last=mn,p.lastIndexOf=function(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var d=h;return c!==o&&(d=(d=$t(c))<0?ge(h+d,0):Fe(d,h-1)),i==i?function(_,w,$){for(var I=$+1;I--;)if(_[I]===w)return I;return I}(e,i,d):er(e,Yr,d,!0)},p.lowerCase=Up,p.lowerFirst=Wp,p.lt=Ip,p.lte=Mp,p.max=function(e){return e&&e.length?Io(e,Ve,Ka):o},p.maxBy=function(e,i){return e&&e.length?Io(e,ct(i,2),Ka):o},p.mean=function(e){return Ru(e,Ve)},p.meanBy=function(e,i){return Ru(e,ct(i,2))},p.min=function(e){return e&&e.length?Io(e,Ve,tc):o},p.minBy=function(e,i){return e&&e.length?Io(e,ct(i,2),tc):o},p.stubArray=Oc,p.stubFalse=Lc,p.stubObject=function(){return{}},p.stubString=function(){return""},p.stubTrue=function(){return!0},p.multiply=ag,p.nth=function(e,i){return e&&e.length?ul(e,$t(i)):o},p.noConflict=function(){return Wt._===this&&(Wt._=pd),this},p.noop=Rc,p.now=Uo,p.pad=function(e,i,c){e=Ot(e);var h=(i=$t(i))?Mi(e):0;if(!i||h>=i)return e;var d=(i-h)/2;return Lo(wo(d),c)+e+Lo(xo(d),c)},p.padEnd=function(e,i,c){e=Ot(e);var h=(i=$t(i))?Mi(e):0;return i&&h<i?e+Lo(i-h,c):e},p.padStart=function(e,i,c){e=Ot(e);var h=(i=$t(i))?Mi(e):0;return i&&h<i?Lo(i-h,c)+e:e},p.parseInt=function(e,i,c){return c||i==null?i=0:i&&(i=+i),$d(Ot(e).replace(yr,""),i||0)},p.random=function(e,i,c){if(c&&typeof c!="boolean"&&De(e,i,c)&&(i=c=o),c===o&&(typeof i=="boolean"?(c=i,i=o):typeof e=="boolean"&&(c=e,e=o)),e===o&&i===o?(e=0,i=1):(e=lr(e),i===o?(i=e,e=0):i=lr(i)),e>i){var h=e;e=i,i=h}if(c||e%1||i%1){var d=Vu();return Fe(e+d*(i-e+Ge("1e-"+((d+"").length-1))),i)}return nc(e,i)},p.reduce=function(e,i,c){var h=xt(e)?wn:Ou,d=arguments.length<3;return h(e,ct(i,4),c,d,Er)},p.reduceRight=function(e,i,c){var h=xt(e)?uo:Ou,d=arguments.length<3;return h(e,ct(i,4),c,d,Ju)},p.repeat=function(e,i,c){return i=(c?De(e,i,c):i===o)?1:$t(i),rc(Ot(e),i)},p.replace=function(){var e=arguments,i=Ot(e[0]);return e.length<3?i:i.replace(e[1],e[2])},p.result=function(e,i,c){var h=-1,d=(i=Tr(i,e)).length;for(d||(d=1,e=o);++h<d;){var _=e==null?o:e[Vn(i[h])];_===o&&(h=d,_=c),e=ur(_)?_.call(e):_}return e},p.round=cg,p.runInContext=M,p.sample=function(e){return(xt(e)?Qu:Dd)(e)},p.size=function(e){if(e==null)return 0;if(Be(e))return Vo(e)?Mi(e):e.length;var i=Re(e);return i==st||i==Ft?e.size:Ja(e).length},p.snakeCase=Bp,p.some=function(e,i,c){var h=xt(e)?Sr:Pd;return c&&De(e,i,c)&&(i=o),h(e,ct(i,3))},p.sortedIndex=function(e,i){return Ao(e,i)},p.sortedIndexBy=function(e,i,c){return ic(e,i,ct(c,2))},p.sortedIndexOf=function(e,i){var c=e==null?0:e.length;if(c){var h=Ao(e,i);if(h<c&&Cn(e[h],i))return h}return-1},p.sortedLastIndex=function(e,i){return Ao(e,i,!0)},p.sortedLastIndexBy=function(e,i,c){return ic(e,i,ct(c,2),!0)},p.sortedLastIndexOf=function(e,i){if(e!=null&&e.length){var c=Ao(e,i,!0)-1;if(Cn(e[c],i))return c}return-1},p.startCase=Hp,p.startsWith=function(e,i,c){return e=Ot(e),c=c==null?0:ei($t(c),0,e.length),i=Ke(i),e.slice(c,c+i.length)==i},p.subtract=ug,p.sum=function(e){return e&&e.length?za(e,Ve):0},p.sumBy=function(e,i){return e&&e.length?za(e,ct(i,2)):0},p.template=function(e,i,c){var h=p.templateSettings;c&&De(e,i,c)&&(i=o),e=Ot(e),i=Go({},i,h,Ol);var d,_,w=Go({},i.imports,h.imports,Ol),$=be(w),I=qa(w,$),O=0,R=i.interpolate||xi,P="__p += '",X=Ua((i.escape||xi).source+"|"+R.source+"|"+(R===es?rs:xi).source+"|"+(i.evaluate||xi).source+"|$","g"),tt="//# sourceURL="+(Lt.call(i,"sourceURL")?(i.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Ut+"]")+`
`;e.replace(X,function(U,lt,Q,yt,gt,Tt){return Q||(Q=yt),P+=e.slice(O,Tt).replace(Oa,ad),lt&&(d=!0,P+=`' +
__e(`+lt+`) +
'`),gt&&(_=!0,P+=`';
`+gt+`;
__p += '`),Q&&(P+=`' +
((__t = (`+Q+`)) == null ? '' : __t) +
'`),O=Tt+U.length,U}),P+=`';
`;var at=Lt.call(i,"variable")&&i.variable;if(at){if(Aa.test(at))throw new Mt("Invalid `variable` option passed into `_.template`")}else P=`with (obj) {
`+P+`
}
`;P=(_?P.replace(Ca,""):P).replace(Sa,"$1").replace(Vs,"$1;"),P="function("+(at||"obj")+`) {
`+(at?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(d?", __e = _.escape":"")+(_?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+P+`return __p
}`;var L=kh(function(){return pe($,tt+"return "+P).apply(o,I)});if(L.source=P,Cc(L))throw L;return L},p.times=function(e,i){if((e=$t(e))<1||e>g)return[];var c=b,h=Fe(e,b);i=ct(i),e-=b;for(var d=ja(h,i);++c<e;)i(c);return d},p.toFinite=lr,p.toInteger=$t,p.toLength=ph,p.toLower=function(e){return Ot(e).toLowerCase()},p.toNumber=_n,p.toSafeInteger=function(e){return e?ei($t(e),-9007199254740991,g):e===0?e:0},p.toString=Ot,p.toUpper=function(e){return Ot(e).toUpperCase()},p.trim=function(e,i,c){if((e=Ot(e))&&(c||i===o))return Lu(e);if(!e||!(i=Ke(i)))return e;var h=$n(e),d=$n(i);return Fr(h,Nu(h,d),Du(h,d)+1).join("")},p.trimEnd=function(e,i,c){if((e=Ot(e))&&(c||i===o))return e.slice(0,ju(e)+1);if(!e||!(i=Ke(i)))return e;var h=$n(e);return Fr(h,0,Du(h,$n(i))+1).join("")},p.trimStart=function(e,i,c){if((e=Ot(e))&&(c||i===o))return e.replace(yr,"");if(!e||!(i=Ke(i)))return e;var h=$n(e);return Fr(h,Nu(h,$n(i))).join("")},p.truncate=function(e,i){var c=30,h="...";if(Jt(i)){var d="separator"in i?i.separator:d;c="length"in i?$t(i.length):c,h="omission"in i?Ke(i.omission):h}var _=(e=Ot(e)).length;if(Ii(e)){var w=$n(e);_=w.length}if(c>=_)return e;var $=c-Mi(h);if($<1)return h;var I=w?Fr(w,0,$).join(""):e.slice(0,$);if(d===o)return I+h;if(w&&($+=I.length-$),Sc(d)){if(e.slice($).search(d)){var O,R=I;for(d.global||(d=Ua(d.source,Ot(br.exec(d))+"g")),d.lastIndex=0;O=d.exec(R);)var P=O.index;I=I.slice(0,P===o?$:P)}}else if(e.indexOf(Ke(d),$)!=$){var X=I.lastIndexOf(d);X>-1&&(I=I.slice(0,X))}return I+h},p.unescape=function(e){return(e=Ot(e))&&Ia.test(e)?e.replace(Xi,ud):e},p.uniqueId=function(e){var i=++fd;return Ot(e)+i},p.upperCase=Vp,p.upperFirst=Ec,p.each=nh,p.eachRight=rh,p.first=Xl,Fc(p,(Nc={},Bn(p,function(e,i){Lt.call(p.prototype,i)||(Nc[i]=e)}),Nc),{chain:!1}),p.VERSION="4.17.21",Te(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){p[e].placeholder=p}),Te(["drop","take"],function(e,i){It.prototype[e]=function(c){c=c===o?1:ge($t(c),0);var h=this.__filtered__&&!i?new It(this):this.clone();return h.__filtered__?h.__takeCount__=Fe(c,h.__takeCount__):h.__views__.push({size:Fe(c,b),type:e+(h.__dir__<0?"Right":"")}),h},It.prototype[e+"Right"]=function(c){return this.reverse()[e](c).reverse()}}),Te(["filter","map","takeWhile"],function(e,i){var c=i+1,h=c==1||c==3;It.prototype[e]=function(d){var _=this.clone();return _.__iteratees__.push({iteratee:ct(d,3),type:c}),_.__filtered__=_.__filtered__||h,_}}),Te(["head","last"],function(e,i){var c="take"+(i?"Right":"");It.prototype[e]=function(){return this[c](1).value()[0]}}),Te(["initial","tail"],function(e,i){var c="drop"+(i?"":"Right");It.prototype[e]=function(){return this.__filtered__?new It(this):this[c](1)}}),It.prototype.compact=function(){return this.filter(Ve)},It.prototype.find=function(e){return this.filter(e).head()},It.prototype.findLast=function(e){return this.reverse().find(e)},It.prototype.invokeMap=kt(function(e,i){return typeof e=="function"?new It(this):this.map(function(c){return $s(c,e,i)})}),It.prototype.reject=function(e){return this.filter(Bo(ct(e)))},It.prototype.slice=function(e,i){e=$t(e);var c=this;return c.__filtered__&&(e>0||i<0)?new It(c):(e<0?c=c.takeRight(-e):e&&(c=c.drop(e)),i!==o&&(c=(i=$t(i))<0?c.dropRight(-i):c.take(i-e)),c)},It.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},It.prototype.toArray=function(){return this.take(b)},Bn(It.prototype,function(e,i){var c=/^(?:filter|find|map|reject)|While$/.test(i),h=/^(?:head|last)$/.test(i),d=p[h?"take"+(i=="last"?"Right":""):i],_=h||/^find/.test(i);d&&(p.prototype[i]=function(){var w=this.__wrapped__,$=h?[1]:arguments,I=w instanceof It,O=$[0],R=I||xt(w),P=function(lt){var Q=d.apply(p,Ze([lt],$));return h&&X?Q[0]:Q};R&&c&&typeof O=="function"&&O.length!=1&&(I=R=!1);var X=this.__chain__,tt=!!this.__actions__.length,at=_&&!X,L=I&&!tt;if(!_&&R){w=L?w:new It(this);var U=e.apply(w,$);return U.__actions__.push({func:qo,args:[P],thisArg:o}),new dn(U,X)}return at&&L?e.apply(this,$):(U=this.thru(P),at?h?U.value()[0]:U.value():U)})}),Te(["pop","push","shift","sort","splice","unshift"],function(e){var i=ho[e],c=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",h=/^(?:pop|shift)$/.test(e);p.prototype[e]=function(){var d=arguments;if(h&&!this.__chain__){var _=this.value();return i.apply(xt(_)?_:[],d)}return this[c](function(w){return i.apply(xt(w)?w:[],d)})}}),Bn(It.prototype,function(e,i){var c=p[i];if(c){var h=c.name+"";Lt.call(Fi,h)||(Fi[h]=[]),Fi[h].push({name:i,func:c})}}),Fi[Ro(o,2).name]=[{name:"wrapper",func:o}],It.prototype.clone=function(){var e=new It(this.__wrapped__);return e.__actions__=We(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=We(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=We(this.__views__),e},It.prototype.reverse=function(){if(this.__filtered__){var e=new It(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},It.prototype.value=function(){var e=this.__wrapped__.value(),i=this.__dir__,c=xt(e),h=i<0,d=c?e.length:0,_=function(Tt,nt,ht){for(var me=-1,se=ht.length;++me<se;){var ze=ht[me],Qt=ze.size;switch(ze.type){case"drop":Tt+=Qt;break;case"dropRight":nt-=Qt;break;case"take":nt=Fe(nt,Tt+Qt);break;case"takeRight":Tt=ge(Tt,nt-Qt)}}return{start:Tt,end:nt}}(0,d,this.__views__),w=_.start,$=_.end,I=$-w,O=h?$:w-1,R=this.__iteratees__,P=R.length,X=0,tt=Fe(I,this.__takeCount__);if(!c||!h&&d==I&&tt==I)return _l(e,this.__actions__);var at=[];t:for(;I--&&X<tt;){for(var L=-1,U=e[O+=i];++L<P;){var lt=R[L],Q=lt.iteratee,yt=lt.type,gt=Q(U);if(yt==2)U=gt;else if(!gt){if(yt==1)continue t;break t}}at[X++]=U}return at},p.prototype.at=lp,p.prototype.chain=function(){return eh(this)},p.prototype.commit=function(){return new dn(this.value(),this.__chain__)},p.prototype.next=function(){this.__values__===o&&(this.__values__=dh(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?o:this.__values__[this.__index__++]}},p.prototype.plant=function(e){for(var i,c=this;c instanceof Co;){var h=Zl(c);h.__index__=0,h.__values__=o,i?d.__wrapped__=h:i=h;var d=h;c=c.__wrapped__}return d.__wrapped__=e,i},p.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof It){var i=e;return this.__actions__.length&&(i=new It(this)),(i=i.reverse()).__actions__.push({func:qo,args:[xc],thisArg:o}),new dn(i,this.__chain__)}return this.thru(xc)},p.prototype.toJSON=p.prototype.valueOf=p.prototype.value=function(){return _l(this.__wrapped__,this.__actions__)},p.prototype.first=p.prototype.head,ms&&(p.prototype[ms]=function(){return this}),p}();Ie?((Ie.exports=Ei)._=Ei,fe._=Ei):Wt._=Ei}).call(an);var g_=fu.exports;function mr(o){return Array.isArray?Array.isArray(o):Xf(o)==="[object Array]"}const m_=1/0;function __(o){return o==null?"":function(t){if(typeof t=="string")return t;let n=t+"";return n=="0"&&1/t==-m_?"-0":n}(o)}function Kn(o){return typeof o=="string"}function Kf(o){return typeof o=="number"}function v_(o){return o===!0||o===!1||function(t){return Yf(t)&&t!==null}(o)&&Xf(o)=="[object Boolean]"}function Yf(o){return typeof o=="object"}function on(o){return o!=null}function Gc(o){return!o.trim().length}function Xf(o){return o==null?o===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(o)}const y_=o=>`Missing ${o} property in key`,b_=o=>`Property 'weight' in key '${o}' must be a positive integer`,of=Object.prototype.hasOwnProperty;class x_{constructor(t){this._keys=[],this._keyMap={};let n=0;t.forEach(r=>{let s=Jf(r);this._keys.push(s),this._keyMap[s.id]=s,n+=s.weight}),this._keys.forEach(r=>{r.weight/=n})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Jf(o){let t=null,n=null,r=null,s=1,a=null;if(Kn(o)||mr(o))r=o,t=af(o),n=du(o);else{if(!of.call(o,"name"))throw new Error(y_("name"));const u=o.name;if(r=u,of.call(o,"weight")&&(s=o.weight,s<=0))throw new Error(b_(u));t=af(u),n=du(u),a=o.getFn}return{path:t,id:n,weight:s,src:r,getFn:a}}function af(o){return mr(o)?o:o.split(".")}function du(o){return mr(o)?o.join("."):o}const w_={useExtendedSearch:!1,getFn:function(o,t){let n=[],r=!1;const s=(a,u,l)=>{if(on(a))if(u[l]){const f=a[u[l]];if(!on(f))return;if(l===u.length-1&&(Kn(f)||Kf(f)||v_(f)))n.push(__(f));else if(mr(f)){r=!0;for(let m=0,g=f.length;m<g;m+=1)s(f[m],u,l+1)}else u.length&&s(f,u,l+1)}else n.push(a)};return s(o,Kn(t)?t.split("."):t,0),r?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var vt={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(o,t)=>o.score===t.score?o.idx<t.idx?-1:1:o.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...w_};const $_=/[^ ]+/g;class Au{constructor({getFn:t=vt.getFn,fieldNormWeight:n=vt.fieldNormWeight}={}){this.norm=function(r=1,s=3){const a=new Map,u=Math.pow(10,s);return{get(l){const f=l.match($_).length;if(a.has(f))return a.get(f);const m=1/Math.pow(f,.5*r),g=parseFloat(Math.round(m*u)/u);return a.set(f,g),g},clear(){a.clear()}}}(n,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((n,r)=>{this._keysMap[n.id]=r})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,Kn(this.docs[0])?this.docs.forEach((t,n)=>{this._addString(t,n)}):this.docs.forEach((t,n)=>{this._addObject(t,n)}),this.norm.clear())}add(t){const n=this.size();Kn(t)?this._addString(t,n):this._addObject(t,n)}removeAt(t){this.records.splice(t,1);for(let n=t,r=this.size();n<r;n+=1)this.records[n].i-=1}getValueForItemAtKeyId(t,n){return t[this._keysMap[n]]}size(){return this.records.length}_addString(t,n){if(!on(t)||Gc(t))return;let r={v:t,i:n,n:this.norm.get(t)};this.records.push(r)}_addObject(t,n){let r={i:n,$:{}};this.keys.forEach((s,a)=>{let u=s.getFn?s.getFn(t):this.getFn(t,s.path);if(on(u)){if(mr(u)){let l=[];const f=[{nestedArrIndex:-1,value:u}];for(;f.length;){const{nestedArrIndex:m,value:g}=f.pop();if(on(g))if(Kn(g)&&!Gc(g)){let y={v:g,i:m,n:this.norm.get(g)};l.push(y)}else mr(g)&&g.forEach((y,b)=>{f.push({nestedArrIndex:b,value:y})})}r.$[a]=l}else if(Kn(u)&&!Gc(u)){let l={v:u,n:this.norm.get(u)};r.$[a]=l}}}),this.records.push(r)}toJSON(){return{keys:this.keys,records:this.records}}}function td(o,t,{getFn:n=vt.getFn,fieldNormWeight:r=vt.fieldNormWeight}={}){const s=new Au({getFn:n,fieldNormWeight:r});return s.setKeys(o.map(Jf)),s.setSources(t),s.create(),s}function aa(o,{errors:t=0,currentLocation:n=0,expectedLocation:r=0,distance:s=vt.distance,ignoreLocation:a=vt.ignoreLocation}={}){const u=t/o.length;if(a)return u;const l=Math.abs(r-n);return s?u+l/s:l?1:u}const ci=32;function k_(o,t,n,{location:r=vt.location,distance:s=vt.distance,threshold:a=vt.threshold,findAllMatches:u=vt.findAllMatches,minMatchCharLength:l=vt.minMatchCharLength,includeMatches:f=vt.includeMatches,ignoreLocation:m=vt.ignoreLocation}={}){if(t.length>ci)throw new Error(`Pattern length exceeds max of ${ci}.`);const g=t.length,y=o.length,b=Math.max(0,Math.min(r,y));let k=a,T=b;const j=l>1||f,q=j?Array(y):[];let S;for(;(S=o.indexOf(t,T))>-1;){let pt=aa(t,{currentLocation:S,expectedLocation:b,distance:s,ignoreLocation:m});if(k=Math.min(pt,k),T=S+g,j){let St=0;for(;St<g;)q[S+St]=1,St+=1}}T=-1;let E=[],H=1,ot=g+y;const st=1<<g-1;for(let pt=0;pt<g;pt+=1){let St=0,mt=ot;for(;St<mt;)aa(t,{errors:pt,currentLocation:b+mt,expectedLocation:b,distance:s,ignoreLocation:m})<=k?St=mt:ot=mt,mt=Math.floor((ot-St)/2+St);ot=mt;let Ft=Math.max(1,b-mt+1),Ht=u?y:Math.min(b+mt,y)+g,ce=Array(Ht+2);ce[Ht+1]=(1<<pt)-1;for(let Ct=Ht;Ct>=Ft;Ct-=1){let wt=Ct-1,he=n[o.charAt(wt)];if(j&&(q[wt]=+!!he),ce[Ct]=(ce[Ct+1]<<1|1)&he,pt&&(ce[Ct]|=(E[Ct+1]|E[Ct])<<1|1|E[Ct+1]),ce[Ct]&st&&(H=aa(t,{errors:pt,currentLocation:wt,expectedLocation:b,distance:s,ignoreLocation:m}),H<=k)){if(k=H,T=wt,T<=b)break;Ft=Math.max(1,2*b-T)}}if(aa(t,{errors:pt+1,currentLocation:b,expectedLocation:b,distance:s,ignoreLocation:m})>k)break;E=ce}const Dt={isMatch:T>=0,score:Math.max(.001,H)};if(j){const pt=function(St=[],mt=vt.minMatchCharLength){let Ft=[],Ht=-1,ce=-1,Ct=0;for(let wt=St.length;Ct<wt;Ct+=1){let he=St[Ct];he&&Ht===-1?Ht=Ct:he||Ht===-1||(ce=Ct-1,ce-Ht+1>=mt&&Ft.push([Ht,ce]),Ht=-1)}return St[Ct-1]&&Ct-Ht>=mt&&Ft.push([Ht,Ct-1]),Ft}(q,l);pt.length?f&&(Dt.indices=pt):Dt.isMatch=!1}return Dt}function C_(o){let t={};for(let n=0,r=o.length;n<r;n+=1){const s=o.charAt(n);t[s]=(t[s]||0)|1<<r-n-1}return t}class ed{constructor(t,{location:n=vt.location,threshold:r=vt.threshold,distance:s=vt.distance,includeMatches:a=vt.includeMatches,findAllMatches:u=vt.findAllMatches,minMatchCharLength:l=vt.minMatchCharLength,isCaseSensitive:f=vt.isCaseSensitive,ignoreLocation:m=vt.ignoreLocation}={}){if(this.options={location:n,threshold:r,distance:s,includeMatches:a,findAllMatches:u,minMatchCharLength:l,isCaseSensitive:f,ignoreLocation:m},this.pattern=f?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const g=(b,k)=>{this.chunks.push({pattern:b,alphabet:C_(b),startIndex:k})},y=this.pattern.length;if(y>ci){let b=0;const k=y%ci,T=y-k;for(;b<T;)g(this.pattern.substr(b,ci),b),b+=ci;if(k){const j=y-ci;g(this.pattern.substr(j),j)}}else g(this.pattern,0)}searchIn(t){const{isCaseSensitive:n,includeMatches:r}=this.options;if(n||(t=t.toLowerCase()),this.pattern===t){let T={isMatch:!0,score:0};return r&&(T.indices=[[0,t.length-1]]),T}const{location:s,distance:a,threshold:u,findAllMatches:l,minMatchCharLength:f,ignoreLocation:m}=this.options;let g=[],y=0,b=!1;this.chunks.forEach(({pattern:T,alphabet:j,startIndex:q})=>{const{isMatch:S,score:E,indices:H}=k_(t,T,j,{location:s+q,distance:a,threshold:u,findAllMatches:l,minMatchCharLength:f,includeMatches:r,ignoreLocation:m});S&&(b=!0),y+=E,S&&H&&(g=[...g,...H])});let k={isMatch:b,score:b?y/this.chunks.length:1};return b&&r&&(k.indices=g),k}}class jr{constructor(t){this.pattern=t}static isMultiMatch(t){return cf(t,this.multiRegex)}static isSingleMatch(t){return cf(t,this.singleRegex)}search(){}}function cf(o,t){const n=o.match(t);return n?n[1]:null}class nd extends jr{constructor(t,{location:n=vt.location,threshold:r=vt.threshold,distance:s=vt.distance,includeMatches:a=vt.includeMatches,findAllMatches:u=vt.findAllMatches,minMatchCharLength:l=vt.minMatchCharLength,isCaseSensitive:f=vt.isCaseSensitive,ignoreLocation:m=vt.ignoreLocation}={}){super(t),this._bitapSearch=new ed(t,{location:n,threshold:r,distance:s,includeMatches:a,findAllMatches:u,minMatchCharLength:l,isCaseSensitive:f,ignoreLocation:m})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class rd extends jr{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let n,r=0;const s=[],a=this.pattern.length;for(;(n=t.indexOf(this.pattern,r))>-1;)r=n+a,s.push([n,r-1]);const u=!!s.length;return{isMatch:u,score:u?0:1,indices:s}}}const pu=[class extends jr{constructor(o){super(o)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(o){const t=o===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},rd,class extends jr{constructor(o){super(o)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(o){const t=o.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends jr{constructor(o){super(o)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(o){const t=!o.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},class extends jr{constructor(o){super(o)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(o){const t=!o.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},class extends jr{constructor(o){super(o)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(o){const t=o.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[o.length-this.pattern.length,o.length-1]}}},class extends jr{constructor(o){super(o)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(o){const t=o.indexOf(this.pattern)===-1;return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},nd],uf=pu.length,S_=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,I_=new Set([nd.type,rd.type]);class M_{constructor(t,{isCaseSensitive:n=vt.isCaseSensitive,includeMatches:r=vt.includeMatches,minMatchCharLength:s=vt.minMatchCharLength,ignoreLocation:a=vt.ignoreLocation,findAllMatches:u=vt.findAllMatches,location:l=vt.location,threshold:f=vt.threshold,distance:m=vt.distance}={}){this.query=null,this.options={isCaseSensitive:n,includeMatches:r,minMatchCharLength:s,findAllMatches:u,ignoreLocation:a,location:l,threshold:f,distance:m},this.pattern=n?t:t.toLowerCase(),this.query=function(g,y={}){return g.split("|").map(b=>{let k=b.trim().split(S_).filter(j=>j&&!!j.trim()),T=[];for(let j=0,q=k.length;j<q;j+=1){const S=k[j];let E=!1,H=-1;for(;!E&&++H<uf;){const ot=pu[H];let st=ot.isMultiMatch(S);st&&(T.push(new ot(st,y)),E=!0)}if(!E)for(H=-1;++H<uf;){const ot=pu[H];let st=ot.isSingleMatch(S);if(st){T.push(new ot(st,y));break}}}return T})}(this.pattern,this.options)}static condition(t,n){return n.useExtendedSearch}searchIn(t){const n=this.query;if(!n)return{isMatch:!1,score:1};const{includeMatches:r,isCaseSensitive:s}=this.options;t=s?t:t.toLowerCase();let a=0,u=[],l=0;for(let f=0,m=n.length;f<m;f+=1){const g=n[f];u.length=0,a=0;for(let y=0,b=g.length;y<b;y+=1){const k=g[y],{isMatch:T,indices:j,score:q}=k.search(t);if(!T){l=0,a=0,u.length=0;break}if(a+=1,l+=q,r){const S=k.constructor.type;I_.has(S)?u=[...u,...j]:u.push(j)}}if(a){let y={isMatch:!0,score:l/a};return r&&(y.indices=u),y}}return{isMatch:!1,score:1}}}const gu=[];function mu(o,t){for(let n=0,r=gu.length;n<r;n+=1){let s=gu[n];if(s.condition(o,t))return new s(o,t)}return new ed(o,t)}const Tu="$and",E_="$or",lf="$path",A_="$val",Zc=o=>!(!o[Tu]&&!o[E_]),hf=o=>({[Tu]:Object.keys(o).map(t=>({[t]:o[t]}))});function id(o,t,{auto:n=!0}={}){const r=s=>{let a=Object.keys(s);const u=(f=>!!f[lf])(s);if(!u&&a.length>1&&!Zc(s))return r(hf(s));if((f=>!mr(f)&&Yf(f)&&!Zc(f))(s)){const f=u?s[lf]:a[0],m=u?s[A_]:s[f];if(!Kn(m))throw new Error((y=>`Invalid value for key ${y}`)(f));const g={keyId:du(f),pattern:m};return n&&(g.searcher=mu(m,t)),g}let l={children:[],operator:a[0]};return a.forEach(f=>{const m=s[f];mr(m)&&m.forEach(g=>{l.children.push(r(g))})}),l};return Zc(o)||(o=hf(o)),r(o)}function T_(o,t){const n=o.matches;t.matches=[],on(n)&&n.forEach(r=>{if(!on(r.indices)||!r.indices.length)return;const{indices:s,value:a}=r;let u={indices:s,value:a};r.key&&(u.key=r.key.src),r.idx>-1&&(u.refIndex=r.idx),t.matches.push(u)})}function F_(o,t){t.score=o.score}class qi{constructor(t,n={},r){this.options={...vt,...n},this.options.useExtendedSearch,this._keyStore=new x_(this.options.keys),this.setCollection(t,r)}setCollection(t,n){if(this._docs=t,n&&!(n instanceof Au))throw new Error("Incorrect 'index' type");this._myIndex=n||td(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){on(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const n=[];for(let r=0,s=this._docs.length;r<s;r+=1){const a=this._docs[r];t(a,r)&&(this.removeAt(r),r-=1,s-=1,n.push(a))}return n}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:n=-1}={}){const{includeMatches:r,includeScore:s,shouldSort:a,sortFn:u,ignoreFieldNorm:l}=this.options;let f=Kn(t)?Kn(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(m,{ignoreFieldNorm:g=vt.ignoreFieldNorm}){m.forEach(y=>{let b=1;y.matches.forEach(({key:k,norm:T,score:j})=>{const q=k?k.weight:null;b*=Math.pow(j===0&&q?Number.EPSILON:j,(q||1)*(g?1:T))}),y.score=b})}(f,{ignoreFieldNorm:l}),a&&f.sort(u),Kf(n)&&n>-1&&(f=f.slice(0,n)),function(m,g,{includeMatches:y=vt.includeMatches,includeScore:b=vt.includeScore}={}){const k=[];return y&&k.push(T_),b&&k.push(F_),m.map(T=>{const{idx:j}=T,q={item:g[j],refIndex:j};return k.length&&k.forEach(S=>{S(T,q)}),q})}(f,this._docs,{includeMatches:r,includeScore:s})}_searchStringList(t){const n=mu(t,this.options),{records:r}=this._myIndex,s=[];return r.forEach(({v:a,i:u,n:l})=>{if(!on(a))return;const{isMatch:f,score:m,indices:g}=n.searchIn(a);f&&s.push({item:a,idx:u,matches:[{score:m,value:a,norm:l,indices:g}]})}),s}_searchLogical(t){const n=id(t,this.options),r=(l,f,m)=>{if(!l.children){const{keyId:y,searcher:b}=l,k=this._findMatches({key:this._keyStore.get(y),value:this._myIndex.getValueForItemAtKeyId(f,y),searcher:b});return k&&k.length?[{idx:m,item:f,matches:k}]:[]}const g=[];for(let y=0,b=l.children.length;y<b;y+=1){const k=l.children[y],T=r(k,f,m);if(T.length)g.push(...T);else if(l.operator===Tu)return[]}return g},s=this._myIndex.records,a={},u=[];return s.forEach(({$:l,i:f})=>{if(on(l)){let m=r(n,l,f);m.length&&(a[f]||(a[f]={idx:f,item:l,matches:[]},u.push(a[f])),m.forEach(({matches:g})=>{a[f].matches.push(...g)}))}}),u}_searchObjectList(t){const n=mu(t,this.options),{keys:r,records:s}=this._myIndex,a=[];return s.forEach(({$:u,i:l})=>{if(!on(u))return;let f=[];r.forEach((m,g)=>{f.push(...this._findMatches({key:m,value:u[g],searcher:n}))}),f.length&&a.push({idx:l,item:u,matches:f})}),a}_findMatches({key:t,value:n,searcher:r}){if(!on(n))return[];let s=[];if(mr(n))n.forEach(({v:a,i:u,n:l})=>{if(!on(a))return;const{isMatch:f,score:m,indices:g}=r.searchIn(a);f&&s.push({score:m,key:t,value:a,idx:u,norm:l,indices:g})});else{const{v:a,n:u}=n,{isMatch:l,score:f,indices:m}=r.searchIn(a);l&&s.push({score:f,key:t,value:a,norm:u,indices:m})}return s}}qi.version="7.0.0",qi.createIndex=td,qi.parseIndex=function(o,{getFn:t=vt.getFn,fieldNormWeight:n=vt.fieldNormWeight}={}){const{keys:r,records:s}=o,a=new Au({getFn:t,fieldNormWeight:n});return a.setKeys(r),a.setIndexRecords(s),a},qi.config=vt,qi.parseQuery=id,function(...o){gu.push(...o)}(M_);const qr=class qr{constructor(t,n){v(this,"_disposers",[]);v(this,"_allMentionables",ae([]));v(this,"_breadcrumbIds",ae([]));v(this,"_userQuery",ae(""));v(this,"_active",ae(!1));v(this,"_allGroups",qc([this._active,this._allMentionables],([t,n])=>t?Eg(n):[]));v(this,"_currentGroup",qc([this._breadcrumbIds,this._allGroups],([t,n])=>{if(t.length===0)return;const r=t[t.length-1];return n.find(s=>Ps(s)&&s.id===r)}));v(this,"dispose",()=>{for(const t of this._disposers)t()});v(this,"openDropdown",()=>{this._active.set(!0)});v(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});v(this,"toggleDropdown",()=>le(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));v(this,"pushBreadcrumb",t=>{le(this._active)&&this._breadcrumbIds.update(n=>[...n,t.id])});v(this,"popBreadcrumb",()=>{le(this._active)&&this._breadcrumbIds.update(t=>t.slice(0,-1))});v(this,"selectMentionable",t=>{var s;const n=this._chatModel.extensionClient,r=this._chatModel.specialContextInputModel;return Ps(t)&&t.type==="breadcrumb"?(this.pushBreadcrumb(t),!0):t.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):Af(t)?(r.markAllActive(),this.closeDropdown(),n.reportWebviewClientEvent(Yc.chatRestoreDefaultContext),!0):t.clearContext?(r.markAllInactive(),this.closeDropdown(),n.reportWebviewClientEvent(Yc.chatClearContext),!0):t.userGuidelines?(n.openSettingsPage("userGuidelines"),this.closeDropdown(),!0):((s=this._insertMentionNode)==null||s.call(this,t),this.closeDropdown(),!0)});v(this,"_displayItems",qc([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([t,n,r,s,a])=>t?n.length>0&&s?[{...s,type:"breadcrumb-back"},...s.group.items.slice(0,qr.SINGLE_GROUP_MAX_ITEMS).map(u=>({...u,type:"item"}))]:r.length>0?a.flatMap(u=>[{...u,type:"breadcrumb"},...u.group.items.slice(0,qr.MULTI_GROUP_MAX_ITEMS).map(l=>({...l,type:"item"}))]):[{...Ag,type:"item"},...a.map(u=>({...u,type:"breadcrumb"})),{...Tg,type:"item"},{...Fg,type:"item"}]:[]));v(this,"_refreshSeqNum",0);v(this,"_refreshMentionables",g_.throttle(async()=>{if(!le(this._active))return;this._refreshSeqNum++;const t=this._refreshSeqNum,n=this._chatModel.currentConversationModel&&Bi(this._chatModel.currentConversationModel),r=le(this._userQuery),s=await this._chatModel.extensionClient.getSuggestions(r,n);t===this._refreshSeqNum&&this._allMentionables.set(R_(r,s))},qr.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=t,this._insertMentionNode=n,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};v(qr,"REFRESH_THROTTLE_MS",600),v(qr,"SINGLE_GROUP_MAX_ITEMS",12),v(qr,"MULTI_GROUP_MAX_ITEMS",6);let _u=qr;const R_=(o,t)=>{if(o.length<=1)return t;const n=new qi(t,{keys:["label"],threshold:1,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(o);return n.length===0?t:n.map(r=>r.item)};function wa(o){switch(o){case Ae.DEFAULT:return Lh;case Ae.PROTOTYPER:return Wg;case Ae.BRAINSTORM:return Ug;case Ae.REVIEWER:return Pg;default:return Lh}}function O_(o){let t,n,r,s=o[0].label+"";return{c(){t=Vt("span"),n=Vt("span"),r=Ce(s),qt(n,"class","c-mentionable-group-label__text right"),qt(t,"class","c-mentionable-group-label")},m(a,u){rt(a,t,u),Gt(t,n),Gt(n,r)},p(a,u){1&u&&s!==(s=a[0].label+"")&&gr(r,s)},i:Bt,o:Bt,d(a){a&&it(t)}}}function L_(o){let t,n;return t=new Bg({props:{$$slots:{text:[H_],leftIcon:[B_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};17&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function N_(o){let t,n=o[0].label+"";return{c(){t=Ce(n)},m(r,s){rt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].label+"")&&gr(t,n)},i:Bt,o:Bt,d(r){r&&it(t)}}}function D_(o){let t,n;return t=new Pe({props:{filepath:o[0].rule.path,$$slots:{leftIcon:[V_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].rule.path),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function z_(o){let t,n;return t=new Pe({props:{filepath:o[0].recentFile.pathName,$$slots:{leftIcon:[G_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].recentFile.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function j_(o){let t,n;return t=new Pe({props:{filepath:o[0].selection.pathName,$$slots:{leftIcon:[Z_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].selection.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function q_(o){let t,n;return t=new Pe({props:{filepath:o[0].sourceFolder.folderRoot,$$slots:{leftIcon:[Q_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].sourceFolder.folderRoot),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function P_(o){let t,n;return t=new Pe({props:{filepath:o[0].externalSource.name,$$slots:{leftIcon:[K_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].externalSource.name),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function U_(o){let t,n;return t=new Pe({props:{filepath:o[0].folder.pathName,$$slots:{leftIcon:[Y_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].folder.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function W_(o){let t,n;return t=new Pe({props:{filepath:o[0].file.pathName,$$slots:{leftIcon:[X_]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].file.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function B_(o){let t,n,r;var s=wa(o[0].personality.type);return s&&(n=Qi(s,{})),{c(){t=Vt("span"),n&&V(n.$$.fragment),qt(t,"slot","leftIcon"),qt(t,"class","c-context-menu-item__icon svelte-1a2w9oo")},m(a,u){rt(a,t,u),n&&G(n,t,null),r=!0},p(a,u){if(1&u&&s!==(s=wa(a[0].personality.type))){if(n){$e();const l=n;z(l.$$.fragment,1,0,()=>{Z(l,1)}),ke()}s?(n=Qi(s,{}),V(n.$$.fragment),F(n.$$.fragment,1),G(n,t,null)):n=null}},i(a){r||(n&&F(n.$$.fragment,a),r=!0)},o(a){n&&z(n.$$.fragment,a),r=!1},d(a){a&&it(t),n&&Z(n)}}}function H_(o){let t,n,r=o[0].label+"";return{c(){t=Vt("span"),n=Ce(r),qt(t,"slot","text")},m(s,a){rt(s,t,a),Gt(t,n)},p(s,a){1&a&&r!==(r=s[0].label+"")&&gr(n,r)},d(s){s&&it(t)}}}function V_(o){let t,n;return t=new Of({props:{slot:"leftIcon",iconName:"rule"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function G_(o){let t,n;return t=new Ki({props:{slot:"leftIcon",iconName:"description"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function Z_(o){let t,n;return t=new Ki({props:{slot:"leftIcon",iconName:"text_select_start"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function Q_(o){let t,n;return t=new Ki({props:{slot:"leftIcon",iconName:"folder_managed"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function K_(o){let t,n;return t=new Ki({props:{slot:"leftIcon",iconName:"import_contacts"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function Y_(o){let t,n;return t=new Ki({props:{slot:"leftIcon",iconName:"folder_open"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function X_(o){let t,n;return t=new Ki({props:{slot:"leftIcon",iconName:"description"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function J_(o){let t,n,r,s,a,u,l,f,m,g,y,b,k,T;const j=[W_,U_,P_,q_,j_,z_,D_,N_,L_,O_],q=[];function S(E,H){return 1&H&&(t=null),1&H&&(n=null),1&H&&(r=null),1&H&&(s=null),1&H&&(a=null),1&H&&(u=null),1&H&&(l=null),1&H&&(f=null),1&H&&(m=null),1&H&&(g=null),t==null&&(t=!!bu(E[0])),t?0:(n==null&&(n=!!xu(E[0])),n?1:(r==null&&(r=!!wu(E[0])),r?2:(s==null&&(s=!!ga(E[0])),s?3:(a==null&&(a=!!pa(E[0])),a?4:(u==null&&(u=!!da(E[0])),u?5:(l==null&&(l=!!_a(E[0])),l?6:(f==null&&(f=!!Ps(E[0])),f?7:(m==null&&(m=!!$a(E[0])),m?8:(g==null&&(g=!!(Af(E[0])||Rg(E[0])||ma(E[0]))),g?9:-1)))))))))}return~(y=S(o,-1))&&(b=q[y]=j[y](o)),{c(){b&&b.c(),k=_r()},m(E,H){~y&&q[y].m(E,H),rt(E,k,H),T=!0},p(E,H){let ot=y;y=S(E,H),y===ot?~y&&q[y].p(E,H):(b&&($e(),z(q[ot],1,1,()=>{q[ot]=null}),ke()),~y?(b=q[y],b?b.p(E,H):(b=q[y]=j[y](E),b.c()),F(b,1),b.m(k.parentNode,k)):b=null)},i(E){T||(F(b),T=!0)},o(E){z(b),T=!1},d(E){E&&it(k),~y&&q[y].d(E)}}}function t0(o){let t,n,r;var s=o[3];function a(u,l){return{props:{highlight:u[2],onSelect:u[1],$$slots:{default:[J_]},$$scope:{ctx:u}}}}return s&&(t=Qi(s,a(o))),{c(){t&&V(t.$$.fragment),n=_r()},m(u,l){t&&G(t,u,l),rt(u,n,l),r=!0},p(u,[l]){if(8&l&&s!==(s=u[3])){if(t){$e();const f=t;z(f.$$.fragment,1,0,()=>{Z(f,1)}),ke()}s?(t=Qi(s,a(u)),V(t.$$.fragment),F(t.$$.fragment,1),G(t,n.parentNode,n)):t=null}else if(s){const f={};4&l&&(f.highlight=u[2]),2&l&&(f.onSelect=u[1]),17&l&&(f.$$scope={dirty:l,ctx:u}),t.$set(f)}},i(u){r||(t&&F(t.$$.fragment,u),r=!0)},o(u){t&&z(t.$$.fragment,u),r=!1},d(u){u&&it(n),t&&Z(t,u)}}}function e0(o,t,n){let r,{item:s}=t,{onSelect:a}=t,{highlight:u}=t;return o.$$set=l=>{"item"in l&&n(0,s=l.item),"onSelect"in l&&n(1,a=l.onSelect),"highlight"in l&&n(2,u=l.highlight)},o.$$.update=()=>{1&o.$$.dirty&&(s.type==="breadcrumb-back"?n(3,r=Bc.BreadcrumbBackItem):s.type==="breadcrumb"&&Ps(s)?n(3,r=Bc.BreadcrumbItem):s.type!=="item"||Ps(s)||n(3,r=Bc.Item))},[s,a,u,r]}class n0 extends pi{constructor(t){super(),gi(this,t,e0,t0,mi,{item:0,onSelect:1,highlight:2})}}function r0(o){let t,n=o[0].label+"";return{c(){t=Ce(n)},m(r,s){rt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].label+"")&&gr(t,n)},i:Bt,o:Bt,d(r){r&&it(t)}}}function i0(o){let t,n,r,s;return t=new Of({}),r=new Pe({props:{filepath:`${Rh}/${Oh}/${o[0].rule.path}`}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(a,u){G(t,a,u),rt(a,n,u),G(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=`${Rh}/${Oh}/${a[0].rule.path}`),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&it(n),Z(t,a),Z(r,a)}}}function s0(o){let t,n,r,s,a,u,l,f;return n=new Hg({props:{heightPx:32,floatHeight:4,animationDuration:2.25,$$slots:{default:[d0]},$$scope:{ctx:o}}}),a=new Th({props:{size:2,weight:"medium",$$slots:{default:[p0]},$$scope:{ctx:o}}}),l=new Th({props:{size:1,$$slots:{default:[g0]},$$scope:{ctx:o}}}),{c(){t=Vt("div"),V(n.$$.fragment),r=Nt(),s=Vt("div"),V(a.$$.fragment),u=Nt(),V(l.$$.fragment),qt(t,"class","c-mention-hover-contents__personality-icon svelte-11069rs"),qt(s,"class","c-mention-hover-contents__personality svelte-11069rs")},m(m,g){rt(m,t,g),G(n,t,null),rt(m,r,g),rt(m,s,g),G(a,s,null),Gt(s,u),G(l,s,null),f=!0},p(m,g){const y={};3&g&&(y.$$scope={dirty:g,ctx:m}),n.$set(y);const b={};3&g&&(b.$$scope={dirty:g,ctx:m}),a.$set(b);const k={};3&g&&(k.$$scope={dirty:g,ctx:m}),l.$set(k)},i(m){f||(F(n.$$.fragment,m),F(a.$$.fragment,m),F(l.$$.fragment,m),f=!0)},o(m){z(n.$$.fragment,m),z(a.$$.fragment,m),z(l.$$.fragment,m),f=!1},d(m){m&&(it(t),it(r),it(s)),Z(n),Z(a),Z(l)}}}function o0(o){var a,u;let t,n,r,s;return t=new Vg({}),r=new Pe({props:{filepath:`${o[0].selection.pathName}:L${(a=o[0].selection.fullRange)==null?void 0:a.startLineNumber}-${(u=o[0].selection.fullRange)==null?void 0:u.endLineNumber}`}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(l,f){G(t,l,f),rt(l,n,f),G(r,l,f),s=!0},p(l,f){var g,y;const m={};1&f&&(m.filepath=`${l[0].selection.pathName}:L${(g=l[0].selection.fullRange)==null?void 0:g.startLineNumber}-${(y=l[0].selection.fullRange)==null?void 0:y.endLineNumber}`),r.$set(m)},i(l){s||(F(t.$$.fragment,l),F(r.$$.fragment,l),s=!0)},o(l){z(t.$$.fragment,l),z(r.$$.fragment,l),s=!1},d(l){l&&it(n),Z(t,l),Z(r,l)}}}function a0(o){let t,n,r=o[0].userGuidelines.overLimit&&ff(o);return{c(){r&&r.c(),t=_r()},m(s,a){r&&r.m(s,a),rt(s,t,a),n=!0},p(s,a){s[0].userGuidelines.overLimit?r?(r.p(s,a),1&a&&F(r,1)):(r=ff(s),r.c(),F(r,1),r.m(t.parentNode,t)):r&&($e(),z(r,1,1,()=>{r=null}),ke())},i(s){n||(F(r),n=!0)},o(s){z(r),n=!1},d(s){s&&it(t),r&&r.d(s)}}}function c0(o){let t,n,r,s,a,u,l,f;return r=new Gg({}),a=new Pe({props:{class:"c-source-folder-item",filepath:o[0].sourceFolder.folderRoot}}),l=new Zg({props:{class:"guidelines-filespan",sourceFolder:o[0].sourceFolder}}),{c(){t=Vt("div"),n=Vt("div"),V(r.$$.fragment),s=Nt(),V(a.$$.fragment),u=Nt(),V(l.$$.fragment),qt(n,"class","l-source-folder-name svelte-11069rs"),qt(t,"class","l-mention-hover-contents__source-folder")},m(m,g){rt(m,t,g),Gt(t,n),G(r,n,null),Gt(n,s),G(a,n,null),Gt(t,u),G(l,t,null),f=!0},p(m,g){const y={};1&g&&(y.filepath=m[0].sourceFolder.folderRoot),a.$set(y);const b={};1&g&&(b.sourceFolder=m[0].sourceFolder),l.$set(b)},i(m){f||(F(r.$$.fragment,m),F(a.$$.fragment,m),F(l.$$.fragment,m),f=!0)},o(m){z(r.$$.fragment,m),z(a.$$.fragment,m),z(l.$$.fragment,m),f=!1},d(m){m&&it(t),Z(r),Z(a),Z(l)}}}function u0(o){let t,n,r,s;return t=new Qg({}),r=new Pe({props:{filepath:o[0].externalSource.name}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(a,u){G(t,a,u),rt(a,n,u),G(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].externalSource.name),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&it(n),Z(t,a),Z(r,a)}}}function l0(o){let t,n,r,s;return t=new Lg({}),r=new Pe({props:{filepath:o[0].folder.pathName}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(a,u){G(t,a,u),rt(a,n,u),G(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].folder.pathName),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&it(n),Z(t,a),Z(r,a)}}}function h0(o){let t,n,r,s;return t=new Ff({}),r=new Pe({props:{filepath:o[0].recentFile.pathName}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(a,u){G(t,a,u),rt(a,n,u),G(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].recentFile.pathName),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&it(n),Z(t,a),Z(r,a)}}}function f0(o){let t,n,r,s;return t=new Ff({}),r=new Pe({props:{filepath:o[0].file.pathName}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(a,u){G(t,a,u),rt(a,n,u),G(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].file.pathName),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&it(n),Z(t,a),Z(r,a)}}}function d0(o){let t,n,r;var s=wa(o[0].personality.type);return s&&(t=Qi(s,{})),{c(){t&&V(t.$$.fragment),n=_r()},m(a,u){t&&G(t,a,u),rt(a,n,u),r=!0},p(a,u){if(1&u&&s!==(s=wa(a[0].personality.type))){if(t){$e();const l=t;z(l.$$.fragment,1,0,()=>{Z(l,1)}),ke()}s?(t=Qi(s,{}),V(t.$$.fragment),F(t.$$.fragment,1),G(t,n.parentNode,n)):t=null}},i(a){r||(t&&F(t.$$.fragment,a),r=!0)},o(a){t&&z(t.$$.fragment,a),r=!1},d(a){a&&it(n),t&&Z(t,a)}}}function p0(o){let t,n=o[0].label+"";return{c(){t=Ce(n)},m(r,s){rt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].label+"")&&gr(t,n)},d(r){r&&it(t)}}}function g0(o){let t,n=o[0].personality.description+"";return{c(){t=Ce(n)},m(r,s){rt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].personality.description+"")&&gr(t,n)},d(r){r&&it(t)}}}function ff(o){let t,n,r,s;return t=new Jg({props:{class:"c-mention-hover-contents__guidelines-warning-icon"}}),r=new Pe({props:{filepath:`Guidelines exceeded length limit of ${o[0].userGuidelines.lengthLimit} characters`}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(a,u){G(t,a,u),rt(a,n,u),G(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=`Guidelines exceeded length limit of ${a[0].userGuidelines.lengthLimit} characters`),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&it(n),Z(t,a),Z(r,a)}}}function m0(o){let t,n,r,s,a,u,l,f,m,g,y,b,k;const T=[f0,h0,l0,u0,c0,a0,o0,s0,i0,r0],j=[];function q(S,E){return 1&E&&(n=null),1&E&&(r=null),1&E&&(s=null),1&E&&(a=null),1&E&&(u=null),1&E&&(l=null),1&E&&(f=null),1&E&&(m=null),1&E&&(g=null),n==null&&(n=!(!S[0]||!bu(S[0]))),n?0:(r==null&&(r=!(!S[0]||!da(S[0]))),r?1:(s==null&&(s=!(!S[0]||!xu(S[0]))),s?2:(a==null&&(a=!(!S[0]||!wu(S[0]))),a?3:(u==null&&(u=!(!S[0]||!ga(S[0]))),u?4:(l==null&&(l=!!(S[0]&&ma(S[0])&&S[0].userGuidelines.enabled)),l?5:(f==null&&(f=!(!S[0]||!pa(S[0]))),f?6:(m==null&&(m=!(!S[0]||!$a(S[0]))),m?7:(g==null&&(g=!(!S[0]||!_a(S[0]))),g?8:9))))))))}return y=q(o,-1),b=j[y]=T[y](o),{c(){t=Vt("div"),b.c(),qt(t,"class","c-mention-hover-contents svelte-11069rs")},m(S,E){rt(S,t,E),j[y].m(t,null),k=!0},p(S,[E]){let H=y;y=q(S,E),y===H?j[y].p(S,E):($e(),z(j[H],1,1,()=>{j[H]=null}),ke(),b=j[y],b?b.p(S,E):(b=j[y]=T[y](S),b.c()),F(b,1),b.m(t,null))},i(S){k||(F(b),k=!0)},o(S){z(b),k=!1},d(S){S&&it(t),j[y].d()}}}function _0(o,t,n){let{option:r}=t;return o.$$set=s=>{"option"in s&&n(0,r=s.option)},[r]}class v0 extends pi{constructor(t){super(),gi(this,t,_0,m0,mi,{option:0})}}function df(o,t,n){const r=o.slice();return r[15]=t[n],r}function pf(o){let t,n;function r(){return o[8](o[15])}return t=new n0({props:{item:o[15],highlight:o[15]===o[14],onSelect:r}}),{c(){V(t.$$.fragment)},m(s,a){G(t,s,a),n=!0},p(s,a){o=s;const u={};4&a&&(u.item=o[15]),16388&a&&(u.highlight=o[15]===o[14]),4&a&&(u.onSelect=r),t.$set(u)},i(s){n||(F(t.$$.fragment,s),n=!0)},o(s){z(t.$$.fragment,s),n=!1},d(s){Z(t,s)}}}function y0(o){let t,n,r=fa(o[2]),s=[];for(let u=0;u<r.length;u+=1)s[u]=pf(df(o,r,u));const a=u=>z(s[u],1,1,()=>{s[u]=null});return{c(){for(let u=0;u<s.length;u+=1)s[u].c();t=_r()},m(u,l){for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(u,l);rt(u,t,l),n=!0},p(u,l){if(16420&l){let f;for(r=fa(u[2]),f=0;f<r.length;f+=1){const m=df(u,r,f);s[f]?(s[f].p(m,l),F(s[f],1)):(s[f]=pf(m),s[f].c(),F(s[f],1),s[f].m(t.parentNode,t))}for($e(),f=r.length;f<s.length;f+=1)a(f);ke()}},i(u){if(!n){for(let l=0;l<r.length;l+=1)F(s[l]);n=!0}},o(u){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)z(s[l]);n=!1},d(u){u&&it(t),Mf(s,u)}}}function b0(o){let t,n;return t=new v0({props:{slot:"mentionable",option:o[13]}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p(r,s){const a={};8192&s&&(a.option=r[13]),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function x0(o){let t,n,r,s;return t=new Xc.Menu.Root({props:{mentionables:o[2],onQueryUpdate:o[4],onSelectMentionable:o[5],$$slots:{default:[y0,({activeItem:a})=>({14:a}),({activeItem:a})=>a?16384:0]},$$scope:{ctx:o}}}),r=new Xc.ChipTooltip({props:{$$slots:{mentionable:[b0,({mentionable:a})=>({13:a}),({mentionable:a})=>a?8192:0]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment)},m(a,u){G(t,a,u),rt(a,n,u),G(r,a,u),s=!0},p(a,u){const l={};4&u&&(l.mentionables=a[2]),278532&u&&(l.$$scope={dirty:u,ctx:a}),t.$set(l);const f={};270336&u&&(f.$$scope={dirty:u,ctx:a}),r.$set(f)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&it(n),Z(t,a),Z(r,a)}}}function w0(o){let t,n,r={triggerCharacter:"@",onMentionItemsUpdated:o[0],$$slots:{default:[x0]},$$scope:{ctx:o}};return t=new Xc.Root({props:r}),o[9](t),{c(){V(t.$$.fragment)},m(s,a){G(t,s,a),n=!0},p(s,[a]){const u={};1&a&&(u.onMentionItemsUpdated=s[0]),262148&a&&(u.$$scope={dirty:a,ctx:s}),t.$set(u)},i(s){n||(F(t.$$.fragment,s),n=!0)},o(s){z(t.$$.fragment,s),n=!1},d(s){o[9](null),Z(t,s)}}}function $0(o,t,n){let r,{requestEditorFocus:s}=t,{onMentionItemsUpdated:a}=t;const u=pg("chatModel");if(!u)throw new Error("ChatModel not found in context");const l=new _u(u,g),f=l.displayItems;let m;function g(b){return!!m&&(m.insertMention(b),l.closeDropdown(),!0)}function y(b){const k=l.selectMentionable(b);return s(),k}return fi(o,f,b=>n(2,r=b)),yu(()=>{l.dispose()}),o.$$set=b=>{"requestEditorFocus"in b&&n(6,s=b.requestEditorFocus),"onMentionItemsUpdated"in b&&n(0,a=b.onMentionItemsUpdated)},[a,m,r,f,function(b){b===void 0?l.closeDropdown():(l.openDropdown(),l.userQuery.set(b))},y,s,b=>g(b),b=>y(b),function(b){Ui[b?"unshift":"push"](()=>{m=b,n(1,m)})}]}class k0 extends pi{constructor(t){super(),gi(this,t,$0,w0,mi,{requestEditorFocus:6,onMentionItemsUpdated:0,insertMentionNode:7})}get insertMentionNode(){return this.$$.ctx[7]}}function gf(o){let t,n,r,s,a,u,l,f,m,g,y,b,k,T,j={focusOnInit:!0,$$slots:{default:[C0]},$$scope:{ctx:o}};return u=new Lf.Root({props:j}),o[25](u),m=new Wr({props:{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$slots:{default:[S0]},$$scope:{ctx:o}}}),m.$on("click",function(){pr(o[0].disposeDiffViewPanel)&&o[0].disposeDiffViewPanel.apply(this,arguments)}),y=new Wr({props:{id:"send",size:1,variant:"solid",color:"accent",title:o[3]===Br.instruction?"Instruct Augment":"Edit with Augment",disabled:!o[4].trim()||o[11],$$slots:{iconRight:[M0],default:[I0]},$$scope:{ctx:o}}}),y.$on("click",o[14]),{c(){t=Vt("div"),n=Nt(),r=Vt("div"),s=Vt("div"),a=Vt("div"),V(u.$$.fragment),l=Nt(),f=Vt("div"),V(m.$$.fragment),g=Nt(),V(y.$$.fragment),qt(a,"class","l-input-area__input svelte-1cxscce"),qt(f,"class","c-instruction-drawer-panel__btn-container svelte-1cxscce"),qt(s,"class","instruction-drawer-panel__contents svelte-1cxscce"),qt(s,"tabindex","0"),qt(s,"role","button"),qt(r,"class","instruction-drawer-panel svelte-1cxscce"),Pi(r,"top",o[5]+"px"),Pi(r,"height",o[6]+"px")},m(q,S){rt(q,t,S),rt(q,n,S),rt(q,r,S),Gt(r,s),Gt(s,a),G(u,a,null),o[26](a),Gt(s,l),Gt(s,f),G(m,f,null),Gt(f,g),G(y,f,null),b=!0,k||(T=[Sf(o[15].call(null,t)),Ur(s,"click",o[17]),Ur(s,"keydown",o[27])],k=!0)},p(q,S){o=q;const E={};1296&S[0]|256&S[1]&&(E.$$scope={dirty:S,ctx:o}),u.$set(E);const H={};256&S[1]&&(H.$$scope={dirty:S,ctx:o}),m.$set(H);const ot={};8&S[0]&&(ot.title=o[3]===Br.instruction?"Instruct Augment":"Edit with Augment"),2064&S[0]&&(ot.disabled=!o[4].trim()||o[11]),8&S[0]|256&S[1]&&(ot.$$scope={dirty:S,ctx:o}),y.$set(ot),(!b||32&S[0])&&Pi(r,"top",o[5]+"px"),(!b||64&S[0])&&Pi(r,"height",o[6]+"px")},i(q){b||(F(u.$$.fragment,q),F(m.$$.fragment,q),F(y.$$.fragment,q),b=!0)},o(q){z(u.$$.fragment,q),z(m.$$.fragment,q),z(y.$$.fragment,q),b=!1},d(q){q&&(it(t),it(n),it(r)),o[25](null),Z(u),o[26](null),Z(m),Z(y),k=!1,vu(T)}}}function C0(o){let t,n,r,s,a,u,l,f;t=new Kg({props:{shortcuts:{Enter:o[23]}}});let m={requestEditorFocus:o[16],onMentionItemsUpdated:o[18]};return r=new k0({props:m}),o[24](r),a=new Lf.Content({props:{content:o[4],onContentChanged:o[19]}}),l=new Yg({props:{placeholder:o[10]}}),{c(){V(t.$$.fragment),n=Nt(),V(r.$$.fragment),s=Nt(),V(a.$$.fragment),u=Nt(),V(l.$$.fragment)},m(g,y){G(t,g,y),rt(g,n,y),G(r,g,y),rt(g,s,y),G(a,g,y),rt(g,u,y),G(l,g,y),f=!0},p(g,y){r.$set({});const b={};16&y[0]&&(b.content=g[4]),a.$set(b);const k={};1024&y[0]&&(k.placeholder=g[10]),l.$set(k)},i(g){f||(F(t.$$.fragment,g),F(r.$$.fragment,g),F(a.$$.fragment,g),F(l.$$.fragment,g),f=!0)},o(g){z(t.$$.fragment,g),z(r.$$.fragment,g),z(a.$$.fragment,g),z(l.$$.fragment,g),f=!1},d(g){g&&(it(n),it(s),it(u)),Z(t,g),o[24](null),Z(r,g),Z(a,g),Z(l,g)}}}function S0(o){let t,n,r;return n=new _i({props:{keybinding:"esc"}}),{c(){t=Ce(`Close
          `),V(n.$$.fragment)},m(s,a){rt(s,t,a),G(n,s,a),r=!0},p:Bt,i(s){r||(F(n.$$.fragment,s),r=!0)},o(s){z(n.$$.fragment,s),r=!1},d(s){s&&it(t),Z(n,s)}}}function I0(o){let t,n=o[3]===Br.instruction?"Instruct":"Edit";return{c(){t=Ce(n)},m(r,s){rt(r,t,s)},p(r,s){8&s[0]&&n!==(n=r[3]===Br.instruction?"Instruct":"Edit")&&gr(t,n)},d(r){r&&it(t)}}}function M0(o){let t,n;return t=new Ng({props:{slot:"iconRight"}}),{c(){V(t.$$.fragment)},m(r,s){G(t,r,s),n=!0},p:Bt,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function E0(o){let t,n,r=o[2]&&gf(o);return{c(){r&&r.c(),t=_r()},m(s,a){r&&r.m(s,a),rt(s,t,a),n=!0},p(s,a){s[2]?r?(r.p(s,a),4&a[0]&&F(r,1)):(r=gf(s),r.c(),F(r,1),r.m(t.parentNode,t)):r&&($e(),z(r,1,1,()=>{r=null}),ke())},i(s){n||(F(r),n=!0)},o(s){z(r),n=!1},d(s){s&&it(t),r&&r.d(s)}}}function A0(o,t,n){let r,s,a,u,l,f,m=Bt,g=()=>(m(),m=dr(b,dt=>n(22,u=dt)),b),y=Bt;o.$$.on_destroy.push(()=>m()),o.$$.on_destroy.push(()=>y());let{diffViewModel:b}=t;g();let{initialConversation:k}=t,{initialFlags:T}=t;const j=tm.getContext().monaco,q={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},S=new Rf(Ns);let E=new Qf;S.registerConsumer(E);let H=new Eu(S,Ns,E,{initialConversation:k,initialFlags:T});const ot=H.currentConversationModel;let st,Dt;S.registerConsumer(H),function(dt){gg("chatModel",dt)}(H);let pt,St="";const mt=b.mode;fi(o,mt,dt=>n(3,l=dt));const Ft=b.selectionLines;function Ht(){const dt=b.getModifiedEditor(),bn=le(j);if(!dt||!bn||(pt==null||pt.clear(),!a))return;const Ln=a.start,Yn=a.end,vr={range:new bn.Range(Ln+1,1,Yn+1,1),options:q};pt||(pt=dt.createDecorationsCollection()),pt.set([vr])}function ce(){return!!(St!=null&&St.trim())&&(b.handleInstructionSubmit(St),!0)}fi(o,Ft,dt=>n(2,a=dt)),Ef(async()=>{await Kc(),Se(),n(5,he=b.editorOffset)}),yu(()=>{st==null||st.destroy(),pt==null||pt.clear()});let Ct,wt,he=0,yn=57;const Se=()=>Ct==null?void 0:Ct.forceFocus();return o.$$set=dt=>{"diffViewModel"in dt&&g(n(0,b=dt.diffViewModel)),"initialConversation"in dt&&n(20,k=dt.initialConversation),"initialFlags"in dt&&n(21,T=dt.initialFlags)},o.$$.update=()=>{if(8&o.$$.dirty[0]&&n(10,r=(l===Br.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs"),4194304&o.$$.dirty[0]&&(n(9,s=u.isLoading),y(),y=dr(s,dt=>n(11,f=dt))),6&o.$$.dirty[0]&&Dt){if(a==null)n(6,yn=0);else{const dt=Dt.scrollHeight;n(6,yn=Math.min(40+dt,108))}st==null||st.update({heightInPx:yn}),Ht()}},[b,Dt,a,l,St,he,yn,Ct,wt,s,r,f,mt,Ft,ce,function(dt){if(dt){const bn=a?a.start:1;st=b.renderInstructionsDrawerViewZone(dt,{line:bn,heightInPx:yn,onDomNodeTop:Ln=>{n(5,he=b.editorOffset+Ln)},autoFocus:!0}),Ht()}},()=>Ct==null?void 0:Ct.requestFocus(),Se,dt=>{ot.saveDraftMentions(dt.current)},function(dt){n(4,St=dt.rawText)},k,T,u,()=>ce(),function(dt){Ui[dt?"unshift":"push"](()=>{wt=dt,n(8,wt)})},function(dt){Ui[dt?"unshift":"push"](()=>{Ct=dt,n(7,Ct)})},function(dt){Ui[dt?"unshift":"push"](()=>{Dt=dt,n(1,Dt)})},dt=>{dt.key==="Enter"&&(Se(),dt.stopPropagation(),dt.preventDefault())}]}class T0 extends pi{constructor(t){super(),gi(this,t,A0,E0,mi,{diffViewModel:0,initialConversation:20,initialFlags:21},null,[-1,-1])}}const{window:Qc}=vg;function mf(o,t,n){const r=o.slice();return r[17]=t[n],r[19]=n,r}function _f(o){let t,n,r,s,a;return n=new p_({props:{diffViewModel:o[3]}}),s=new T0({props:{diffViewModel:o[3]}}),{c(){t=Vt("div"),V(n.$$.fragment),r=Nt(),V(s.$$.fragment),qt(t,"class","sticky-top svelte-dpt00g")},m(u,l){rt(u,t,l),G(n,t,null),rt(u,r,l),G(s,u,l),a=!0},p(u,l){const f={};8&l&&(f.diffViewModel=u[3]),n.$set(f);const m={};8&l&&(m.diffViewModel=u[3]),s.$set(m)},i(u){a||(F(n.$$.fragment,u),F(s.$$.fragment,u),a=!0)},o(u){z(n.$$.fragment,u),z(s.$$.fragment,u),a=!1},d(u){u&&(it(t),it(r)),Z(n),Z(s,u)}}}function vf(o){let t,n,r=fa(o[4]),s=[];for(let u=0;u<r.length;u+=1)s[u]=bf(mf(o,r,u));const a=u=>z(s[u],1,1,()=>{s[u]=null});return{c(){for(let u=0;u<s.length;u+=1)s[u].c();t=_r()},m(u,l){for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(u,l);rt(u,t,l),n=!0},p(u,l){if(280&l){let f;for(r=fa(u[4]),f=0;f<r.length;f+=1){const m=mf(u,r,f);s[f]?(s[f].p(m,l),F(s[f],1)):(s[f]=bf(m),s[f].c(),F(s[f],1),s[f].m(t.parentNode,t))}for($e(),f=r.length;f<s.length;f+=1)a(f);ke()}},i(u){if(!n){for(let l=0;l<r.length;l+=1)F(s[l]);n=!0}},o(u){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)z(s[l]);n=!1},d(u){u&&it(t),Mf(s,u)}}}function yf(o){var a;let t,n;function r(){return o[14](o[17])}function s(){return o[15](o[17])}return t=new n_({props:{isFocused:((a=o[3])==null?void 0:a.currFocusedChunkIdx)===o[19],onAccept:r,onReject:s,diffViewModel:o[3],leaf:o[17],align:"right",disableApply:o[8]}}),{c(){V(t.$$.fragment)},m(u,l){G(t,u,l),n=!0},p(u,l){var m;o=u;const f={};8&l&&(f.isFocused=((m=o[3])==null?void 0:m.currFocusedChunkIdx)===o[19]),24&l&&(f.onAccept=r),24&l&&(f.onReject=s),8&l&&(f.diffViewModel=o[3]),16&l&&(f.leaf=o[17]),256&l&&(f.disableApply=o[8]),t.$set(f)},i(u){n||(F(t.$$.fragment,u),n=!0)},o(u){z(t.$$.fragment,u),n=!1},d(u){Z(t,u)}}}function bf(o){let t,n,r=o[17].unitOfCodeWork.modifiedCode!==o[17].unitOfCodeWork.originalCode&&yf(o);return{c(){r&&r.c(),t=_r()},m(s,a){r&&r.m(s,a),rt(s,t,a),n=!0},p(s,a){s[17].unitOfCodeWork.modifiedCode!==s[17].unitOfCodeWork.originalCode?r?(r.p(s,a),16&a&&F(r,1)):(r=yf(s),r.c(),F(r,1),r.m(t.parentNode,t)):r&&($e(),z(r,1,1,()=>{r=null}),ke())},i(s){n||(F(r),n=!0)},o(s){z(r),n=!1},d(s){s&&it(t),r&&r.d(s)}}}function F0(o){var m;let t,n,r,s,a,u,l=o[3]&&_f(o),f=o[3]&&((m=o[4])==null?void 0:m.length)&&!o[7]&&vf(o);return{c(){t=Vt("div"),l&&l.c(),n=Nt(),r=Vt("div"),s=Vt("div"),a=Nt(),f&&f.c(),qt(s,"class","editor svelte-dpt00g"),qt(r,"class","editor-container svelte-dpt00g"),qt(t,"class","diff-view-container svelte-dpt00g")},m(g,y){rt(g,t,y),l&&l.m(t,null),Gt(t,n),Gt(t,r),Gt(r,s),o[13](s),Gt(r,a),f&&f.m(r,null),u=!0},p(g,y){var b;g[3]?l?(l.p(g,y),8&y&&F(l,1)):(l=_f(g),l.c(),F(l,1),l.m(t,n)):l&&($e(),z(l,1,1,()=>{l=null}),ke()),g[3]&&((b=g[4])!=null&&b.length)&&!g[7]?f?(f.p(g,y),152&y&&F(f,1)):(f=vf(g),f.c(),F(f,1),f.m(r,null)):f&&($e(),z(f,1,1,()=>{f=null}),ke())},i(g){u||(F(l),F(f),u=!0)},o(g){z(l),z(f),u=!1},d(g){g&&it(t),l&&l.d(),o[13](null),f&&f.d()}}}function R0(o){let t,n,r,s;return t=new em.Root({props:{$$slots:{default:[F0]},$$scope:{ctx:o}}}),{c(){V(t.$$.fragment)},m(a,u){G(t,a,u),n=!0,r||(s=[Ur(Qc,"message",function(){var l,f;pr((l=o[0])==null?void 0:l.handleMessageFromExtension)&&((f=o[0])==null||f.handleMessageFromExtension.apply(this,arguments))}),Ur(Qc,"focus",o[11]),Ur(Qc,"blur",o[12])],r=!0)},p(a,[u]){o=a;const l={};1048986&u&&(l.$$scope={dirty:u,ctx:o}),t.$set(l)},i(a){n||(F(t.$$.fragment,a),n=!0)},o(a){z(t.$$.fragment,a),n=!1},d(a){Z(t,a),r=!1,vu(s)}}}function O0(o,t,n){let r,s,a,u,l,f,m,g,y,b,k=Bt,T=Bt,j=Bt;function q(E){const H=_g.dark;return nm((E==null?void 0:E.category)||H,E==null?void 0:E.intensity)??rm.get(H)}fi(o,mg,E=>n(10,l=E)),o.$$.on_destroy.push(()=>k()),o.$$.on_destroy.push(()=>T()),o.$$.on_destroy.push(()=>j()),Ef(async()=>{n(9,b=await window.augmentDeps.monaco),b||console.error("Monaco not loaded. Diff view cannot be initialized.")}),yu(()=>{g==null||g.dispose()});let S=!1;return o.$$.update=()=>{if(1539&o.$$.dirty&&b&&y&&!g&&(n(0,g=new Km(y,q(l),b)),k(),k=dr(g,E=>n(3,u=E))),1&o.$$.dirty&&(n(6,r=g==null?void 0:g.disableApply),j(),j=dr(r,E=>n(8,m=E))),1&o.$$.dirty&&(n(5,s=g==null?void 0:g.disableResolution),T(),T=dr(s,E=>n(7,f=E))),1025&o.$$.dirty){const E=l;g&&(g==null||g.updateTheme(q(E)))}8&o.$$.dirty&&n(4,a=u==null?void 0:u.leaves),5&o.$$.dirty&&(g==null||g.updateIsWebviewFocused(S))},[g,y,S,u,a,s,r,f,m,b,l,()=>n(2,S=!0),()=>n(2,S=!1),function(E){Ui[E?"unshift":"push"](()=>{y=E,n(1,y)})},E=>u==null?void 0:u.acceptChunk(E),E=>u==null?void 0:u.rejectChunk(E)]}new class extends pi{constructor(o){super(),gi(this,o,O0,R0,mi,{})}}({target:document.getElementById("app")});

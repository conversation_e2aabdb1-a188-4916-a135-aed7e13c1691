var it=Object.defineProperty;var at=(o,e,t)=>e in o?it(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var te=(o,e,t)=>at(o,typeof e!="symbol"?e+"":e,t);import{S as Un,T as He,R as ze}from"./check-DCGJZZwz.js";import{R as Ze,b as ge,a as Te}from"./types-Cgd-nZOV.js";import{g as je,a as lt,h as ut,b as dt,c as $t,d as mt,e as pt,f as gt,j as De,k as We,m as Vn,A as Ee,E as oe,o as Ke,S as ft,p as Me}from"./lodash-D9Au3xFg.js";import{S as Z,i as j,s as W,E as Q,e as v,u as $,q as U,t as p,r as V,h,af as ce,ag as le,a as Ie,a9 as Ye,V as M,y,D as B,G as z,c as w,a2 as Xe,a3 as D,z as C,f as R,a5 as ee,g as Ue,_ as ht,H as K,B as _,a7 as Fe,R as he,ab as Qe,X as ve,Y as we,Z as xe,ax as vt,n as N,C as fe,al as Jn,a8 as wt,w as re,a0 as Gn,x as qe,A as Be,aE as ye,W as en,a6 as Zn,ac as pe,a1 as Ce,am as _e,T as ne,b as me,ap as xt,F as jn,aA as nn,K as yt,L as Ct,M as _t,N as kt,d as tn,O as bt,j as sn}from"./SpinnerAugment-BGEGncoZ.js";import{M as It}from"./MaterialIcon-MCURCZji.js";import{o as Wn}from"./keypress-DD1aQVr0.js";import{A as on,a as Y}from"./autofix-state-d-ymFdyn.js";import{bb as St,bc as Ge,bd as Tt,aq as Rt,be as Et,bf as Kn,bg as Mt,bh as Lt,bi as At,bj as Oe,bk as zt}from"./AugmentMessage-tJmvlmov.js";import{t as Ft}from"./index-BS_CDetd.js";import{T as Pe}from"./Content-CTqTUTf_.js";import{E as qt,a as Yn,b as Bt}from"./folder-CJRvA1r9.js";import{e as Ne,f as Pt,g as Nt,E as Ht,h as Ot,T as Xn}from"./Keybindings-BaohEaHR.js";import{c as Qn,I as Dt,R as Ut,D as Vt,d as Jt,e as et,f as Gt,h as Le,i as Zt}from"./main-panel-CdQtCUjm.js";import{B as nt}from"./ButtonAugment-DZMhbPz9.js";import{P as jt,C as Wt}from"./folder-opened-CVKdq1wC.js";import{E as Kt}from"./expand-BMjiP8tK.js";import{P as Yt}from"./pen-to-square-TjBwLyJp.js";import{T as Se}from"./TextTooltipAugment-Cor0M5Er.js";import{I as ke}from"./IconButtonAugment-DR78svzs.js";import{C as Xt,a as Qt,M as es,b as tt,c as st}from"./diff-utils-DVDPX5m5.js";import{B as ot}from"./layer-group-CxYvF2MG.js";import{e as Ae}from"./BaseButton-Bbk8_XKh.js";import{C as ns}from"./CardAugment-oII5PndH.js";import{M as ts,S as ss}from"./index-D3MPLNkT.js";import{O as os}from"./open-in-new-window-BWJckjMw.js";import{C as rs}from"./github-DSqbVTgM.js";import{s as cs}from"./types-DlPx64PZ.js";const is=(o,e,t,n)=>{const s={retryMessage:void 0,showGeneratingResponse:!1,showResumingRemoteAgent:!1,showAwaitingUserInput:!1,showRunningSpacer:!1,showStopped:!1,remoteAgentErrorConfig:void 0,showPaused:!1};if(e===Ee.running){const r=o==null?void 0:o.lastExchange;if(r!=null&&r.isRetriable&&(r!=null&&r.display_error_message))s.retryMessage=r.display_error_message;else if(t||n.isActive){const c=n.isActive?n.getLastToolUseState():o.getLastToolUseState();if(n.isActive){const i=n.currentAgent;(i==null?void 0:i.workspace_status)===Ze.workspaceResuming?s.showResumingRemoteAgent=!0:c.phase!==He.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else c.phase!==He.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else s.showGeneratingResponse=!0}else e===Ee.awaitingUserAction?(s.showAwaitingUserInput=!0,s.showRunningSpacer=!0):((r,c)=>{var d;const i=(d=r==null?void 0:r.lastExchange)==null?void 0:d.status,a=i===oe.cancelled,l=r==null?void 0:r.getLastToolUseState().phase,u=l===He.cancelled;return!c.isActive&&(a||u)})(o,n)&&(s.showStopped=!0);if(n.isActive){const r=n.currentAgent;(r==null?void 0:r.workspace_status)===Ze.workspacePaused&&(s.showPaused=!0)}return s},vc=(o,e,t,n)=>{const s=o.currentConversationModel,r=((d,m)=>m.isActive?m.getCurrentChatHistory():d.chatHistory.filter(g=>je(g)||lt(g)||ut(g)||dt(g)||$t(g)||mt(g)||pt(g)||gt(g)||De(g)||We(g)))(s,n),c=(d=>d.reduce((m,g,k)=>(je(g)&&Vn(g)&&m.length>0||We(g)&&m.length>0?m[m.length-1].push({turn:g,idx:k}):m.push([{turn:g,idx:k}]),m),[]))(r),i=((d,m)=>m.isActive?m.isCurrentAgentRunning?Ee.running:Ee.notRunning:d)(e,n),a=is(s,i,t,n),l=!n.isActive,u=!!n.isActive;if(n.isActive){if(n.sendMessageError&&n.currentAgentId){const d=n.currentAgentId,m=n.sendMessageError;a.remoteAgentErrorConfig={error:m,onRetry:m.canRetry&&m.failedExchangeId?()=>n.retryFailedMessage(d,m.failedExchangeId):void 0,onDelete:m.type===Un.agentFailed?()=>n.deleteAgent(d):void 0}}else if(n.agentChatHistoryError&&n.currentAgentId){const d=n.currentAgentId;a.remoteAgentErrorConfig={error:n.agentChatHistoryError,onRetry:()=>n.refreshAgentChatHistory(d)}}}return{chatHistory:r,groupedChatHistory:c,lastGroupConfig:a,doShowFloatingButtons:l,doShowAgentSetupLogs:u}};function rn(o){let e,t,n,s,r,c,i,a;const l=[o[4][o[1]]];let u={};for(let g=0;g<l.length;g+=1)u=Ie(u,l[g]);t=new It({props:u});let d=[{class:"stage-container"},o[1]?Ye(o[3][o[1]]):{},{role:"button"},{tabindex:"0"}],m={};for(let g=0;g<d.length;g+=1)m=Ie(m,d[g]);return{c(){e=M("div"),y(t.$$.fragment),n=B(),s=M("div"),r=z(o[1]),w(s,"class","message svelte-1etsput"),Xe(e,m),D(e,"active",o[0]),D(e,"svelte-1etsput",!0)},m(g,k){v(g,e,k),C(t,e,null),R(e,n),R(e,s),R(s,r),c=!0,i||(a=[ee(e,"click",o[5]),ee(e,"keydown",Wn("Enter",o[5]))],i=!0)},p(g,k){const L=18&k?Ue(l,[ht(g[4][g[1]])]):{};t.$set(L),(!c||2&k)&&K(r,g[1]),Xe(e,m=Ue(d,[{class:"stage-container"},2&k&&(g[1]?Ye(g[3][g[1]]):{}),{role:"button"},{tabindex:"0"}])),D(e,"active",g[0]),D(e,"svelte-1etsput",!0)},i(g){c||($(t.$$.fragment,g),c=!0)},o(g){p(t.$$.fragment,g),c=!1},d(g){g&&h(e),_(t),i=!1,Fe(a)}}}function as(o){let e,t,n=o[1]&&rn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,[r]){s[1]?n?(n.p(s,r),2&r&&$(n,1)):(n=rn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function ls(o,e,t){let n,s,r,c,{stage:i}=e,{iterationId:a}=e,{stageCount:l}=e;const u=ce("autofixConversationModel");le(o,u,g=>t(10,c=g));const d={[Y.retesting]:"info",[Y.testRunning]:"info",[Y.testFailed]:"error",[Y.testPassed]:"success",[Y.generatingSolutions]:"info",[Y.suggestedSolutions]:"warning",[Y.selectedSolutions]:"success"},m={[Y.retesting]:{iconName:"cached",color:"#FFFFFF"},[Y.testRunning]:{iconName:"cached",color:"#FFFFFF"},[Y.testFailed]:{iconName:"error",color:"#DB3B4B"},[Y.testPassed]:{iconName:"check_circle",color:"#388A34"},[Y.generatingSolutions]:{iconName:"cached",color:"#FFFFFF"},[Y.suggestedSolutions]:{iconName:"edit",color:"#FFFFFF"},[Y.selectedSolutions]:{iconName:"edit",color:"#FFFFFF"}};return o.$$set=g=>{"stage"in g&&t(6,i=g.stage),"iterationId"in g&&t(7,a=g.iterationId),"stageCount"in g&&t(8,l=g.stageCount)},o.$$.update=()=>{var g,k,L;1152&o.$$.dirty&&t(9,n=c==null?void 0:c.getAutofixIteration(a)),1600&o.$$.dirty&&t(0,s=n&&((L=(k=(g=c.extraData)==null?void 0:g.autofixIterations)==null?void 0:k.at(-1))==null?void 0:L.id)===n.id&&n.currentStage===i),833&o.$$.dirty&&t(1,r=function(f,I,A,P){var E;return f?I===on.runTest?f.commandFailed===void 0&&P?f.isFirstIteration?Y.testRunning:Y.retesting:f.commandFailed===!0?Y.testFailed:Y.testPassed:I===on.applyFix?A===(((E=f.suggestedSolutions)==null?void 0:E.length)||0)?f.selectedSolutions?Y.selectedSolutions:Y.generatingSolutions:Y.suggestedSolutions:null:null}(n,i,l,s))},[s,r,u,d,m,()=>{r!==Y.generatingSolutions&&u.launchAutofixPanel(a,i)},i,a,l,n,c]}class wc extends Z{constructor(e){super(),j(this,e,ls,as,W,{stage:6,iterationId:7,stageCount:8})}}function xc(o,e){const t=Math.abs(o);let n=200,s=500;typeof e=="number"?n=e:e&&(n=e.baseThreshold??200,s=e.predictTime??500);const r=t*s/1e3;return Math.max(n,r)}function yc(o,e=10){const t=Math.abs(o);return t>1e3?2*e:t>500?1.5*e:t>200?e:.5*e}function cn(o){const{scrollTop:e,clientHeight:t,scrollHeight:n}=o;return n-e-t}function Cc(o,e={}){let t=e,n={scrollTop:0,scrollBottom:0,scrollHeight:0,scrolledIntoBottom:!0,scrolledAwayFromBottom:!0};const s=()=>{var f,I,A;const{scrollTop:r,scrollHeight:c,offsetHeight:i}=o,a=cn(o),l=r>n.scrollTop+1,u=c-n.scrollHeight,d=!(u<0&&n.scrollBottom<-u)&&r<n.scrollTop-1&&a>n.scrollBottom+1,m=c>i,g=((P,E=40)=>cn(P)<=E)(o),k=g&&m&&l,L=d||!m;k&&!n.scrolledIntoBottom?(f=t.onScrollIntoBottom)==null||f.call(t):L&&!n.scrolledAwayFromBottom&&((I=t.onScrollAwayFromBottom)==null||I.call(t)),n={scrollTop:r,scrollBottom:a,scrolledIntoBottom:k,scrolledAwayFromBottom:L,scrollHeight:c},(A=t.onScroll)==null||A.call(t,r)};return o.addEventListener("scroll",s),{update(r){t=r},destroy(){o.removeEventListener("scroll",s)}}}function us(o){let e,t,n;const s=o[4].default,r=he(s,o,o[3],null);return{c(){e=M("div"),r&&r.c(),w(e,"class",t="c-gradient-mask "+o[2]+" svelte-say8yn"),Qe(e,"--fade-size",o[1]+"px"),D(e,"is-horizontal",o[0]==="horizontal")},m(c,i){v(c,e,i),r&&r.m(e,null),n=!0},p(c,[i]){r&&r.p&&(!n||8&i)&&ve(r,s,c,c[3],n?xe(s,c[3],i,null):we(c[3]),null),(!n||4&i&&t!==(t="c-gradient-mask "+c[2]+" svelte-say8yn"))&&w(e,"class",t),(!n||2&i)&&Qe(e,"--fade-size",c[1]+"px"),(!n||5&i)&&D(e,"is-horizontal",c[0]==="horizontal")},i(c){n||($(r,c),n=!0)},o(c){p(r,c),n=!1},d(c){c&&h(e),r&&r.d(c)}}}function ds(o,e,t){let{$$slots:n={},$$scope:s}=e,{direction:r="vertical"}=e,{fadeSize:c=St}=e,{class:i=""}=e;return o.$$set=a=>{"direction"in a&&t(0,r=a.direction),"fadeSize"in a&&t(1,c=a.fadeSize),"class"in a&&t(2,i=a.class),"$$scope"in a&&t(3,s=a.$$scope)},[r,c,i,s,n]}class _c extends Z{constructor(e){super(),j(this,e,ds,us,W,{direction:0,fadeSize:1,class:2})}}function kc(o,e){var r;let t=o.offsetHeight,n=e;const s=new ResizeObserver(c=>{var a;const i=c[0].contentRect.height;c.length===1?i!==t&&((a=n.onHeightChange)==null||a.call(n,i),t=i):console.warn("Unexpected number of resize entries: ",c)});return s.observe(o),(r=n==null?void 0:n.onHeightChange)==null||r.call(n,t),{update(c){n=c},destroy:()=>s.disconnect()}}function bc(o,e){let t=e;const n=Ft(()=>{t.onSeen()},1e3,{leading:!0,trailing:!0}),s=new IntersectionObserver(c=>{c.length===1?c[0].isIntersecting&&n():console.warn("Unexpected number of intersection entries: ",c)},{threshold:.5}),r=()=>{s.disconnect(),t.track&&s.observe(o)};return r(),{update(c){const i=t;t=c,t.track!==i.track&&r()},destroy:()=>{s.disconnect(),t.onSeen()}}}function an(o){let e,t,n,s=o[6]&&ln();return t=new Ut({props:{changeImageMode:o[32],saveImage:o[9].saveImage,deleteImage:o[9].deleteImage,renderImage:o[9].renderImage,isEditable:o[33]}}),{c(){s&&s.c(),e=B(),y(t.$$.fragment)},m(r,c){s&&s.m(r,c),v(r,e,c),C(t,r,c),n=!0},p(r,c){r[6]?s?64&c[0]&&$(s,1):(s=ln(),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(U(),p(s,1,1,()=>{s=null}),V());const i={};258&c[0]&&(i.changeImageMode=r[32]),512&c[0]&&(i.saveImage=r[9].saveImage),512&c[0]&&(i.deleteImage=r[9].deleteImage),512&c[0]&&(i.renderImage=r[9].renderImage),64&c[0]&&(i.isEditable=r[33]),t.$set(i)},i(r){n||($(s),$(t.$$.fragment,r),n=!0)},o(r){p(s),p(t.$$.fragment,r),n=!1},d(r){r&&h(e),s&&s.d(r),_(t,r)}}}function ln(o){let e,t;return e=new Vt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function $s(o){var L;let e,t,n,s,r,c,i,a,l,u,d=o[9].flags.enableChatMultimodal&&an(o);t=new Pt({props:{shortcuts:o[13]}});let m={requestEditorFocus:o[22],onMentionItemsUpdated:o[21]};s=new Qn({props:m}),o[34](s),c=new Nt({props:{placeholder:o[2]}}),a=new Ne.Content({props:{content:((L=o[7])==null?void 0:L.richTextJsonRepr)??o[3],onContentChanged:o[20]}});const g=o[29].default,k=he(g,o,o[37],null);return{c(){d&&d.c(),e=B(),y(t.$$.fragment),n=B(),y(s.$$.fragment),r=B(),y(c.$$.fragment),i=B(),y(a.$$.fragment),l=B(),k&&k.c()},m(f,I){d&&d.m(f,I),v(f,e,I),C(t,f,I),v(f,n,I),C(s,f,I),v(f,r,I),C(c,f,I),v(f,i,I),C(a,f,I),v(f,l,I),k&&k.m(f,I),u=!0},p(f,I){var J;f[9].flags.enableChatMultimodal?d?(d.p(f,I),512&I[0]&&$(d,1)):(d=an(f),d.c(),$(d,1),d.m(e.parentNode,e)):d&&(U(),p(d,1,1,()=>{d=null}),V());const A={};8192&I[0]&&(A.shortcuts=f[13]),t.$set(A),s.$set({});const P={};4&I[0]&&(P.placeholder=f[2]),c.$set(P);const E={};136&I[0]&&(E.content=((J=f[7])==null?void 0:J.richTextJsonRepr)??f[3]),a.$set(E),k&&k.p&&(!u||64&I[1])&&ve(k,g,f,f[37],u?xe(g,f[37],I,null):we(f[37]),null)},i(f){u||($(d),$(t.$$.fragment,f),$(s.$$.fragment,f),$(c.$$.fragment,f),$(a.$$.fragment,f),$(k,f),u=!0)},o(f){p(d),p(t.$$.fragment,f),p(s.$$.fragment,f),p(c.$$.fragment,f),p(a.$$.fragment,f),p(k,f),u=!1},d(f){f&&(h(e),h(n),h(r),h(i),h(l)),d&&d.d(f),_(t,f),o[34](null),_(s,f),_(c,f),_(a,f),k&&k.d(f)}}}function un(o){let e,t;return e=new Jt({props:{chatModel:o[16]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function ms(o){let e,t,n=o[6]&&un(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[6]?n?(n.p(s,r),64&r[0]&&$(n,1)):(n=un(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function dn(o){let e,t,n,s=o[6]&&$n(o),r=o[4]&&mn(o);return{c(){s&&s.c(),e=Q(),r&&r.c(),t=Q()},m(c,i){s&&s.m(c,i),v(c,e,i),r&&r.m(c,i),v(c,t,i),n=!0},p(c,i){c[6]?s?(s.p(c,i),64&i[0]&&$(s,1)):(s=$n(c),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(U(),p(s,1,1,()=>{s=null}),V()),c[4]?r?(r.p(c,i),16&i[0]&&$(r,1)):(r=mn(c),r.c(),$(r,1),r.m(t.parentNode,t)):r&&(U(),p(r,1,1,()=>{r=null}),V())},i(c){n||($(s),$(r),n=!0)},o(c){p(s),p(r),n=!1},d(c){c&&(h(e),h(t)),s&&s.d(c),r&&r.d(c)}}}function $n(o){let e,t,n;return e=new et.Root({props:{$$slots:{rightAlign:[fs],leftAlign:[ps]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment),t=B()},m(s,r){C(e,s,r),v(s,t,r),n=!0},p(s,r){const c={};17408&r[0]|64&r[1]&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){s&&h(t),_(e,s)}}}function ps(o){var n;let e,t;return e=new et.ContextMenu({props:{slot:"leftAlign",onCloseDropdown:o[22],onInsertMentionable:(n=o[10])==null?void 0:n.insertMentionNode}}),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),t=!0},p(s,r){var i;const c={};1024&r[0]&&(c.onInsertMentionable=(i=s[10])==null?void 0:i.insertMentionNode),e.$set(c)},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){_(e,s)}}}function gs(o){let e,t;return e=new jt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function fs(o){let e,t;return e=new nt({props:{size:1,variant:"solid",disabled:!o[14],$$slots:{default:[gs]},$$scope:{ctx:o}}}),e.$on("click",o[18]),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16384&s[0]&&(r.disabled=!n[14]),64&s[1]&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function mn(o){let e,t,n;return t=new nt({props:{variant:"solid",color:"neutral",size:1,$$slots:{iconLeft:[vs],default:[hs]},$$scope:{ctx:o}}}),t.$on("click",o[31]),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-user-msg__collapse-button svelte-q0brxu")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const c={};64&r[1]&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function hs(o){let e;return{c(){e=M("span"),e.textContent="Expand"},m(t,n){v(t,e,n)},p:N,d(t){t&&h(e)}}}function vs(o){let e,t;return e=new Kt({props:{slot:"iconLeft"}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function ws(o){let e,t,n=(o[6]||!o[4])&&dn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[6]||!s[4]?n?(n.p(s,r),80&r[0]&&$(n,1)):(n=dn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function xs(o){let e,t,n,s,r,c={editable:o[6],$$slots:{footer:[ws],header:[ms],default:[$s]},$$scope:{ctx:o}};return t=new Ne.Root({props:c}),o[35](t),t.$on("click",ys),t.$on("dblclick",o[17]),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-chat-input svelte-q0brxu"),w(e,"role","button"),w(e,"tabindex","-1"),D(e,"is-collapsed",o[4]),D(e,"is-editing",o[6])},m(i,a){v(i,e,a),C(t,e,null),o[36](e),n=!0,s||(r=[ee(window,"mousedown",o[19]),ee(e,"mousedown",vt(o[30]))],s=!0)},p(i,a){const l={};64&a[0]&&(l.editable=i[6]),26623&a[0]|64&a[1]&&(l.$$scope={dirty:a,ctx:i}),t.$set(l),(!n||16&a[0])&&D(e,"is-collapsed",i[4]),(!n||64&a[0])&&D(e,"is-editing",i[6])},i(i){n||($(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&h(e),o[35](null),_(t),o[36](null),s=!1,Fe(r)}}}const ys=o=>o.stopPropagation();function Cs(o,e,t){let n,s,r,c,i,a,l,u=N;o.$$.on_destroy.push(()=>u());let{$$slots:d={},$$scope:m}=e;const g=ce("chatModel");le(o,g,b=>t(9,l=b));const k=ce(ze.key);let{requestId:L}=e,{placeholder:f="Edit your message..."}=e,{content:I}=e,{collapsed:A=!1}=e,{onSubmitEdit:P}=e,{onCancelEdit:E}=e,{setIsCollapsed:J}=e,{userExpanded:T}=e,S,H,F,x=!1,X=[];async function q(){r&&(t(0,T=!0),A&&J(!1),t(6,x=!0),await Jn(),$e())}function G(){return!(!c||!S)&&(P(S,X),!0)}function O(){return t(0,T=!1),t(6,x=!1),t(7,S=void 0),E(),!0}const $e=()=>F==null?void 0:F.forceFocus();let se;return o.$$set=b=>{"requestId"in b&&t(1,L=b.requestId),"placeholder"in b&&t(2,f=b.placeholder),"content"in b&&t(3,I=b.content),"collapsed"in b&&t(4,A=b.collapsed),"onSubmitEdit"in b&&t(23,P=b.onSubmitEdit),"onCancelEdit"in b&&t(24,E=b.onCancelEdit),"setIsCollapsed"in b&&t(5,J=b.setIsCollapsed),"userExpanded"in b&&t(0,T=b.userExpanded),"$$scope"in b&&t(37,m=b.$$scope)},o.$$.update=()=>{512&o.$$.dirty[0]&&(t(15,n=l.currentConversationModel),u(),u=fe(n,b=>t(8,a=b))),268435968&o.$$.dirty[0]&&t(27,r=l.flags.enableEditableHistory&&!s),134218184&o.$$.dirty[0]&&t(14,c=x&&r&&S!==void 0&&S.rawText.trim()!==""&&S.rawText!==I&&S.richTextJsonRepr!==I&&!a.awaitingReply&&!Dt.hasLoadingImages(S.richTextJsonRepr))},t(28,s=!!(k!=null&&k.isActive)),t(13,i={Enter:G,Escape:O}),[T,L,f,I,A,J,x,S,a,l,H,F,se,i,c,n,g,q,G,O,function(b){b!==S&&t(7,S=b)},function(b){X=b.current},()=>F==null?void 0:F.requestFocus(),P,E,()=>{q()},function(){return se},r,s,d,function(b){wt.call(this,o,b)},()=>{t(0,T=!0),J(!1)},b=>{L&&b&&a.updateChatItem(L,{rich_text_json_repr:b})},()=>x,function(b){re[b?"unshift":"push"](()=>{H=b,t(10,H)})},function(b){re[b?"unshift":"push"](()=>{F=b,t(11,F)})},function(b){re[b?"unshift":"push"](()=>{se=b,t(12,se)})},m]}class _s extends Z{constructor(e){super(),j(this,e,Cs,xs,W,{requestId:1,placeholder:2,content:3,collapsed:4,onSubmitEdit:23,onCancelEdit:24,setIsCollapsed:5,userExpanded:0,requestStartEdit:25,getEditorContainer:26},null,[-1,-1])}get requestStartEdit(){return this.$$.ctx[25]}get getEditorContainer(){return this.$$.ctx[26]}}const ae=class ae{constructor(e){te(this,"_tipTapExtension");te(this,"_resizeObserver");te(this,"_checkHeight",e=>{var n,s;const t=e.getBoundingClientRect().height;(s=(n=this._options).onResize)==null||s.call(n,t)});te(this,"_setResizeObserver",e=>{var n;const t=(n=e.view)==null?void 0:n.dom;t&&(this._resizeObserver=new ResizeObserver(s=>{for(const r of s)this._checkHeight(r.target)}),this._resizeObserver.observe(t),this._checkHeight(t))});te(this,"_clearResizeObserver",()=>{var e;(e=this._resizeObserver)==null||e.disconnect(),this._resizeObserver=void 0});te(this,"updateOptions",e=>{this._options={...this._options,...e}});this._options=e;const t=ae._getNextPluginId(),n=this._setResizeObserver,s=this._clearResizeObserver,r=this._checkHeight;this._tipTapExtension=Ht.create({name:t,onCreate:function(){var i;((i=this.editor.view)==null?void 0:i.dom)&&(n(this.editor),this.editor.on("destroy",s))},onUpdate:function(){var i;const c=(i=this.editor.view)==null?void 0:i.dom;c&&r(c)},onDestroy:()=>{var c;(c=this._resizeObserver)==null||c.disconnect(),this._resizeObserver=void 0}})}get tipTapExtension(){return this._tipTapExtension}};te(ae,"_sequenceId",0),te(ae,"RESIZE_OBSERVER_PLUGIN_KEY_BASE","augment-resize-observer-plugin-{}"),te(ae,"_getSequenceId",()=>ae._sequenceId++),te(ae,"_getNextPluginId",()=>{const e=ae._getSequenceId().toString();return ae.RESIZE_OBSERVER_PLUGIN_KEY_BASE.replace("{}",e)});let Ve=ae;function ks(o,e,t){let{height:n=0}=e;const s=a=>{t(0,n=a)},r=ce(Ot.CONTEXT_KEY),c=new Ve({onResize:s}),i=r.pluginManager.registerPlugin(c);return Gn(i),o.$$set=a=>{"height"in a&&t(0,n=a.height)},c.updateOptions({onResize:s}),[n]}let bs=class extends Z{constructor(o){super(),j(this,o,ks,null,W,{height:0})}};function Is(o){let e,t,n;function s(c){o[21](c)}let r={};return o[6]!==void 0&&(r.height=o[6]),e=new bs({props:r}),re.push(()=>qe(e,"height",s)),{c(){y(e.$$.fragment)},m(c,i){C(e,c,i),n=!0},p(c,i){const a={};!t&&64&i&&(t=!0,a.height=c[6],Be(()=>t=!1)),e.$set(a)},i(c){n||($(e.$$.fragment,c),n=!0)},o(c){p(e.$$.fragment,c),n=!1},d(c){_(e,c)}}}function Ss(o){let e,t,n;function s(c){o[23](c)}let r={collapsed:o[7],content:o[3]??o[1],requestId:o[2],onSubmitEdit:o[13],onCancelEdit:o[5],setIsCollapsed:o[11],$$slots:{default:[Is]},$$scope:{ctx:o}};return o[8]!==void 0&&(r.userExpanded=o[8]),e=new _s({props:r}),o[22](e),re.push(()=>qe(e,"userExpanded",s)),{c(){y(e.$$.fragment)},m(c,i){C(e,c,i),n=!0},p(c,i){const a={};128&i&&(a.collapsed=c[7]),10&i&&(a.content=c[3]??c[1]),4&i&&(a.requestId=c[2]),32&i&&(a.onCancelEdit=c[5]),134217792&i&&(a.$$scope={dirty:i,ctx:c}),!t&&256&i&&(t=!0,a.userExpanded=c[8],Be(()=>t=!1)),e.$set(a)},i(c){n||($(e.$$.fragment,c),n=!0)},o(c){p(e.$$.fragment,c),n=!1},d(c){o[22](null),_(e,c)}}}function Ts(o){let e,t,n;return t=new Tt({props:{items:o[10]}}),{c(){e=M("div"),y(t.$$.fragment),w(e,"slot","edit"),w(e,"class","c-user-msg__actions svelte-1dv083n")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const c={};1024&r&&(c.items=s[10]),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Rs(o){let e,t,n,s;return e=new Ge({props:{timestamp:o[4],$$slots:{edit:[Ts],content:[Ss]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(r,c){C(e,r,c),t=!0,n||(s=[ee(window,"keydown",Wn("Escape",o[12])),ee(window,"mousedown",o[12])],n=!0)},p(r,[c]){const i={};16&c&&(i.timestamp=r[4]),134219758&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){p(e.$$.fragment,r),t=!1},d(r){_(e,r),n=!1,Fe(s)}}}function Es(o,e,t){let n,s,r,c,i,a,l,u=N,d=()=>(u(),u=fe(m,x=>t(20,l=x)),m);o.$$.on_destroy.push(()=>u());let{chatModel:m}=e;d();let{msg:g}=e,{requestId:k}=e,{richTextJsonRepr:L}=e,{timestamp:f}=e,{onStartEdit:I=()=>{}}=e,{onAcceptEdit:A=()=>{}}=e,{onCancelEdit:P=()=>{}}=e;const E=ce(ze.key);let J=!1,T=!1;async function S(x){await Jn(),t(7,J=x&&r&&!T)}const H=x=>{i&&k&&(S(!1),F==null||F.requestStartEdit(),I(),x.stopPropagation())};let F;return o.$$set=x=>{"chatModel"in x&&d(t(0,m=x.chatModel)),"msg"in x&&t(1,g=x.msg),"requestId"in x&&t(2,k=x.requestId),"richTextJsonRepr"in x&&t(3,L=x.richTextJsonRepr),"timestamp"in x&&t(4,f=x.timestamp),"onStartEdit"in x&&t(14,I=x.onStartEdit),"onAcceptEdit"in x&&t(15,A=x.onAcceptEdit),"onCancelEdit"in x&&t(5,P=x.onCancelEdit)},o.$$.update=()=>{var x,X;1048580&o.$$.dirty&&t(19,s=k===void 0||k===((X=(x=l==null?void 0:l.currentConversationModel)==null?void 0:x.lastExchange)==null?void 0:X.request_id)),524288&o.$$.dirty&&t(16,r=!s&&!0),1310724&o.$$.dirty&&t(17,i=k!==void 0&&l.flags.fullFeatured&&l.flags.enableEditableHistory&&!n),131072&o.$$.dirty&&t(10,a=[...i?[{label:"Edit message",action:H,id:"edit-message",disabled:!1,icon:Yt}]:[]]),65600&o.$$.dirty&&c&&r&&(F!=null&&F.getEditorContainer())&&c&&r&&S(!(J&&c<120)&&c>120)},t(18,n=!!(E!=null&&E.isActive)),t(6,c=0),[m,g,k,L,f,P,c,J,T,F,a,S,()=>{},function(x,X){if(!k)return;m.currentConversationModel.clearHistoryFrom(k);const q=l.flags.enableChatMultimodal&&x.richTextJsonRepr?m.currentConversationModel.createStructuredRequestNodes(x.richTextJsonRepr):void 0;m.currentConversationModel.sendExchange({request_message:x.rawText,rich_text_json_repr:x.richTextJsonRepr,status:oe.draft,mentioned_items:X,structured_request_nodes:q}),A()},I,A,r,i,n,s,l,function(x){c=x,t(6,c)},function(x){re[x?"unshift":"push"](()=>{F=x,t(9,F)})},function(x){T=x,t(8,T)}]}class Ms extends Z{constructor(e){super(),j(this,e,Es,Rs,W,{chatModel:0,msg:1,requestId:2,richTextJsonRepr:3,timestamp:4,onStartEdit:14,onAcceptEdit:15,onCancelEdit:5})}}function pn(o){let e,t;return e=new Ms({props:{msg:o[1].request_message??"",richTextJsonRepr:o[11],chatModel:o[0],requestId:o[9],timestamp:o[1].timestamp}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};2&s&&(r.msg=n[1].request_message??""),2048&s&&(r.richTextJsonRepr=n[11]),1&s&&(r.chatModel=n[0]),512&s&&(r.requestId=n[9]),2&s&&(r.timestamp=n[1].timestamp),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function gn(o){let e,t,n;function s(a,l){return a[1].display_error_message?zs:a[1].response_text&&a[1].response_text.length>0?As:Ls}let r=s(o),c=r(o),i=o[9]&&fn(o);return{c(){e=M("div"),c.c(),t=B(),i&&i.c(),w(e,"class","c-msg-list__turn-response-failure svelte-1d1manc")},m(a,l){v(a,e,l),c.m(e,null),R(e,t),i&&i.m(e,null),n=!0},p(a,l){r===(r=s(a))&&c?c.p(a,l):(c.d(1),c=r(a),c&&(c.c(),c.m(e,t))),a[9]?i?(i.p(a,l),512&l&&$(i,1)):(i=fn(a),i.c(),$(i,1),i.m(e,null)):i&&(U(),p(i,1,1,()=>{i=null}),V())},i(a){n||($(i),n=!0)},o(a){p(i),n=!1},d(a){a&&h(e),c.d(),i&&i.d()}}}function Ls(o){let e,t,n,s;return{c(){e=z(`We encountered an issue sending your message. Please
        `),t=M("button"),t.textContent="try again",w(t,"class","svelte-1d1manc")},m(r,c){v(r,e,c),v(r,t,c),n||(s=ee(t,"click",ye(o[15])),n=!0)},p:N,d(r){r&&(h(e),h(t)),n=!1,s()}}}function As(o){let e,t,n,s,r;return{c(){e=z(`Connection lost. Please
        `),t=M("button"),t.textContent="try again",n=z(`
        to restart the conversation!`),w(t,"class","svelte-1d1manc")},m(c,i){v(c,e,i),v(c,t,i),v(c,n,i),s||(r=ee(t,"click",ye(o[15])),s=!0)},p:N,d(c){c&&(h(e),h(t),h(n)),s=!1,r()}}}function zs(o){let e,t=o[1].display_error_message+"";return{c(){e=z(t)},m(n,s){v(n,e,s)},p(n,s){2&s&&t!==(t=n[1].display_error_message+"")&&K(e,t)},d(n){n&&h(e)}}}function fn(o){let e,t,n,s,r,c,i,a;function l(d){o[21](d)}let u={onOpenChange:o[16],content:o[7],triggerOn:[Pe.Hover],$$slots:{default:[qs]},$$scope:{ctx:o}};return o[8]!==void 0&&(u.requestClose=o[8]),c=new Se({props:u}),re.push(()=>qe(c,"requestClose",l)),{c(){e=M("div"),t=M("span"),n=z("Request ID: "),s=z(o[9]),r=B(),y(c.$$.fragment),w(e,"class","c-msg-list__request-id svelte-1d1manc")},m(d,m){v(d,e,m),R(e,t),R(t,n),R(t,s),R(e,r),C(c,e,null),a=!0},p(d,m){(!a||512&m)&&K(s,d[9]);const g={};128&m&&(g.content=d[7]),16777216&m&&(g.$$scope={dirty:m,ctx:d}),!i&&256&m&&(i=!0,g.requestClose=d[8],Be(()=>i=!1)),c.$set(g)},i(d){a||($(c.$$.fragment,d),a=!0)},o(d){p(c.$$.fragment,d),a=!1},d(d){d&&h(e),_(c)}}}function Fs(o){let e,t;return e=new Wt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function qs(o){let e,t;return e=new ke({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Fs]},$$scope:{ctx:o}}}),e.$on("click",o[17]),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16777216&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Bs(o){let e,t,n,s,r,c=!o[10]&&!De(o[1])&&!o[12],i=c&&pn(o);n=new Rt({props:{chatModel:o[6],turn:o[1],turnIndex:o[3],requestId:o[9],isLastTurn:o[2],showName:!o[10],group:o[5],showFooter:!Ke(o[1])||o[1].status===oe.cancelled,markdown:o[1].response_text??"",timestamp:o[1].timestamp,messageListContainer:o[4]}});let a=o[1].status===oe.failed&&gn(o);return{c(){e=M("div"),i&&i.c(),t=B(),y(n.$$.fragment),s=B(),a&&a.c(),w(e,"class","c-msg-list__turn svelte-1d1manc")},m(l,u){v(l,e,u),i&&i.m(e,null),R(e,t),C(n,e,null),R(e,s),a&&a.m(e,null),r=!0},p(l,[u]){5122&u&&(c=!l[10]&&!De(l[1])&&!l[12]),c?i?(i.p(l,u),5122&u&&$(i,1)):(i=pn(l),i.c(),$(i,1),i.m(e,t)):i&&(U(),p(i,1,1,()=>{i=null}),V());const d={};64&u&&(d.chatModel=l[6]),2&u&&(d.turn=l[1]),8&u&&(d.turnIndex=l[3]),512&u&&(d.requestId=l[9]),4&u&&(d.isLastTurn=l[2]),1024&u&&(d.showName=!l[10]),32&u&&(d.group=l[5]),2&u&&(d.showFooter=!Ke(l[1])||l[1].status===oe.cancelled),2&u&&(d.markdown=l[1].response_text??""),2&u&&(d.timestamp=l[1].timestamp),16&u&&(d.messageListContainer=l[4]),n.$set(d),l[1].status===oe.failed?a?(a.p(l,u),2&u&&$(a,1)):(a=gn(l),a.c(),$(a,1),a.m(e,null)):a&&(U(),p(a,1,1,()=>{a=null}),V())},i(l){r||($(i),$(n.$$.fragment,l),$(a),r=!0)},o(l){p(i),p(n.$$.fragment,l),p(a),r=!1},d(l){l&&h(e),i&&i.d(),_(n),a&&a.d()}}}function Ps(o,e,t){let n,s,r,c,i,a,l,u,d,m,g=N,k=()=>(g(),g=fe(f,x=>t(6,u=x)),f),L=N;o.$$.on_destroy.push(()=>g()),o.$$.on_destroy.push(()=>L());let{chatModel:f}=e;k();let{turn:I}=e,{isLastTurn:A=!1}=e,{turnIndex:P=0}=e,{messageListContainer:E}=e,{group:J}=e;const T=ce(ze.key);le(o,T,x=>t(20,m=x));let S,H="Copy request ID",F=()=>{};return o.$$set=x=>{"chatModel"in x&&k(t(0,f=x.chatModel)),"turn"in x&&t(1,I=x.turn),"isLastTurn"in x&&t(2,A=x.isLastTurn),"turnIndex"in x&&t(3,P=x.turnIndex),"messageListContainer"in x&&t(4,E=x.messageListContainer),"group"in x&&t(5,J=x.group)},o.$$.update=()=>{var x;2&o.$$.dirty&&t(9,n=I.request_id),64&o.$$.dirty&&(t(13,s=u==null?void 0:u.currentConversationModel),L(),L=fe(s,X=>t(23,d=X))),1048576&o.$$.dirty&&t(19,r=(m==null?void 0:m.isActive)&&((x=m==null?void 0:m.currentAgent)==null?void 0:x.is_setup_script_agent)===!0),8&o.$$.dirty&&t(18,c=P===0),786432&o.$$.dirty&&t(12,i=r&&c),66&o.$$.dirty&&t(11,a=u.flags.enableRichTextHistory?I.rich_text_json_repr:void 0),2&o.$$.dirty&&t(10,l=Vn(I))},[f,I,A,P,E,J,u,H,F,n,l,a,i,s,T,()=>{d.resendTurn(I)},function(x){x||(clearTimeout(S),S=void 0,t(7,H="Copy request ID"))},async function(){n&&(await navigator.clipboard.writeText(n),t(7,H="Copied!"),clearTimeout(S),S=setTimeout(F,1500))},c,r,m,function(x){F=x,t(8,F)}]}class Ic extends Z{constructor(e){super(),j(this,e,Ps,Bs,W,{chatModel:0,turn:1,isLastTurn:2,turnIndex:3,messageListContainer:4,group:5})}}function Ns(o){let e,t,n,s,r,c,i;const a=o[15].default,l=he(a,o,o[14],null);return{c(){e=M("div"),l&&l.c(),w(e,"class",t=en(`c-msg-list__item ${o[5]}`)+" svelte-1s0uz2w"),w(e,"style",n=`min-height: calc(${o[4]}px - (var(--msg-list-item-spacing) * 2));`),w(e,"data-request-id",o[6])},m(u,d){v(u,e,d),l&&l.m(e,null),o[16](e),r=!0,c||(i=Zn(s=Et.call(null,e,{follow:!o[2]&&o[1],scrollContainer:o[3],disableScrollUp:!0,smooth:!0,bottom:!0})),c=!0)},p(u,[d]){l&&l.p&&(!r||16384&d)&&ve(l,a,u,u[14],r?xe(a,u[14],d,null):we(u[14]),null),(!r||32&d&&t!==(t=en(`c-msg-list__item ${u[5]}`)+" svelte-1s0uz2w"))&&w(e,"class",t),(!r||16&d&&n!==(n=`min-height: calc(${u[4]}px - (var(--msg-list-item-spacing) * 2));`))&&w(e,"style",n),(!r||64&d)&&w(e,"data-request-id",u[6]),s&&pe(s.update)&&14&d&&s.update.call(null,{follow:!u[2]&&u[1],scrollContainer:u[3],disableScrollUp:!0,smooth:!0,bottom:!0})},i(u){r||($(l,u),r=!0)},o(u){p(l,u),r=!1},d(u){u&&h(e),l&&l.d(u),o[16](null),c=!1,i()}}}function Hs(o,e,t){let n,s,r,c,i=N,a=N,l=()=>(a(),a=fe(g,T=>t(13,c=T)),g);o.$$.on_destroy.push(()=>i()),o.$$.on_destroy.push(()=>a());let{$$slots:u={},$$scope:d}=e,{requestId:m}=e,{chatModel:g}=e;l();let k,{isLastItem:L=!1}=e,{userControlsScroll:f=!1}=e,{releaseScroll:I=()=>{}}=e,{messageListContainer:A}=e,{minHeight:P}=e,{class:E=""}=e,{dataRequestId:J}=e;return Ce(()=>{A&&L&&Kn(A,{smooth:!0,onScrollFinish:I})}),o.$$set=T=>{"requestId"in T&&t(9,m=T.requestId),"chatModel"in T&&l(t(0,g=T.chatModel)),"isLastItem"in T&&t(1,L=T.isLastItem),"userControlsScroll"in T&&t(2,f=T.userControlsScroll),"releaseScroll"in T&&t(10,I=T.releaseScroll),"messageListContainer"in T&&t(3,A=T.messageListContainer),"minHeight"in T&&t(4,P=T.minHeight),"class"in T&&t(5,E=T.class),"dataRequestId"in T&&t(6,J=T.dataRequestId),"$$scope"in T&&t(14,d=T.$$scope)},o.$$.update=()=>{var T,S;8192&o.$$.dirty&&(t(8,n=(T=c==null?void 0:c.currentConversationModel)==null?void 0:T.focusModel),i(),i=fe(n,H=>t(12,r=H))),4608&o.$$.dirty&&t(11,s=((S=r.focusedItem)==null?void 0:S.request_id)===m),2048&o.$$.dirty&&s&&A&&k&&Mt(A,k,{topBuffer:0,smooth:!0,scrollDuration:100,onScrollFinish:I})},[g,L,f,A,P,E,J,k,n,m,I,s,r,c,d,u,function(T){re[T?"unshift":"push"](()=>{k=T,t(7,k)})}]}class Sc extends Z{constructor(e){super(),j(this,e,Hs,Ns,W,{requestId:9,chatModel:0,isLastItem:1,userControlsScroll:2,releaseScroll:10,messageListContainer:3,minHeight:4,class:5,dataRequestId:6})}}function Os(o){let e;return{c(){e=z("Generating response...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function hn(o){let e,t;return e=new ot.Root({props:{color:"neutral",size:1,$$slots:{default:[Us]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Ds(o){let e,t;return{c(){e=M("span"),t=z(o[2]),w(e,"class","c-gen-response__timer svelte-148snxl"),D(e,"is_minutes",o[0]>=60)},m(n,s){v(n,e,s),R(e,t)},p(n,s){4&s&&K(t,n[2]),1&s&&D(e,"is_minutes",n[0]>=60)},d(n){n&&h(e)}}}function Us(o){let e,t;return e=new ne({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[Ds]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Vs(o){let e,t,n,s,r,c,i;n=new _e({props:{size:1}}),r=new ne({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[Os]},$$scope:{ctx:o}}});let a=o[1]&&hn(o);return{c(){e=M("div"),t=M("span"),y(n.$$.fragment),s=B(),y(r.$$.fragment),c=B(),a&&a.c(),w(t,"class","c-gen-response__text svelte-148snxl"),w(e,"class","c-gen-response svelte-148snxl")},m(l,u){v(l,e,u),R(e,t),C(n,t,null),R(t,s),C(r,t,null),R(e,c),a&&a.m(e,null),i=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=hn(l),a.c(),$(a,1),a.m(e,null)):a&&(U(),p(a,1,1,()=>{a=null}),V())},i(l){i||($(n.$$.fragment,l),$(r.$$.fragment,l),$(a),i=!0)},o(l){p(n.$$.fragment,l),p(r.$$.fragment,l),p(a),i=!1},d(l){l&&h(e),_(n),_(r),a&&a.d()}}}function Js(o,e,t){let n,s,r,{timeToTimerMs:c=5e3}=e,i=0,a=Date.now(),l=!1;function u(){t(0,i=Math.floor((Date.now()-a)/1e3))}function d(){t(1,l=!0),u(),r=setInterval(u,1e3)}return Ce(function(){return s=setTimeout(d,c),a=Date.now(),()=>{t(0,i=0),t(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&t(3,c=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&t(2,n=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(i))},[i,l,n,c]}class Gs extends Z{constructor(e){super(),j(this,e,Js,Vs,W,{timeToTimerMs:3})}}class de{constructor(e){te(this,"type","plainText");this.text=e}}class Re{constructor(e){te(this,"type","specialBlock");this.text=e}}function Je(o){return o.map(e=>e.text).join("")}function Zs(o){let e,t,n,s,r=(!o[0].status||o[0].status===oe.success)&&o[4]===Je(o[3]);e=new es({props:{renderers:o[5],markdown:o[1]+o[4]}});let c=r&&vn(o);return{c(){y(e.$$.fragment),t=B(),c&&c.c(),n=Q()},m(i,a){C(e,i,a),v(i,t,a),c&&c.m(i,a),v(i,n,a),s=!0},p(i,a){const l={};18&a&&(l.markdown=i[1]+i[4]),e.$set(l),25&a&&(r=(!i[0].status||i[0].status===oe.success)&&i[4]===Je(i[3])),r?c?(c.p(i,a),25&a&&$(c,1)):(c=vn(i),c.c(),$(c,1),c.m(n.parentNode,n)):c&&(U(),p(c,1,1,()=>{c=null}),V())},i(i){s||($(e.$$.fragment,i),$(c),s=!0)},o(i){p(e.$$.fragment,i),p(c),s=!1},d(i){i&&(h(t),h(n)),_(e,i),c&&c.d(i)}}}function js(o){let e;function t(r,c){return r[0].display_error_message?Xs:r[0].response_text&&r[0].response_text.length>0?Ys:Ks}let n=t(o),s=n(o);return{c(){e=M("div"),s.c(),w(e,"class","c-msg-failure svelte-9a9fi8")},m(r,c){v(r,e,c),s.m(e,null)},p(r,c){n===(n=t(r))&&s?s.p(r,c):(s.d(1),s=n(r),s&&(s.c(),s.m(e,null)))},i:N,o:N,d(r){r&&h(e),s.d()}}}function Ws(o){let e,t;return e=new Gs({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function vn(o){let e;const t=o[7].default,n=he(t,o,o[8],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||256&r)&&ve(n,t,s,s[8],e?xe(t,s[8],r,null):we(s[8]),null)},i(s){e||($(n,s),e=!0)},o(s){p(n,s),e=!1},d(s){n&&n.d(s)}}}function Ks(o){let e,t,n=o[2]&&wn(o);return{c(){e=z("We encountered an issue sending your message."),n&&n.c(),t=z(".")},m(s,r){v(s,e,r),n&&n.m(s,r),v(s,t,r)},p(s,r){s[2]?n?n.p(s,r):(n=wn(s),n.c(),n.m(t.parentNode,t)):n&&(n.d(1),n=null)},d(s){s&&(h(e),h(t)),n&&n.d(s)}}}function Ys(o){let e,t,n=o[2]&&xn(o);return{c(){e=z("Connection lost."),n&&n.c(),t=Q()},m(s,r){v(s,e,r),n&&n.m(s,r),v(s,t,r)},p(s,r){s[2]?n?n.p(s,r):(n=xn(s),n.c(),n.m(t.parentNode,t)):n&&(n.d(1),n=null)},d(s){s&&(h(e),h(t)),n&&n.d(s)}}}function Xs(o){let e,t=o[0].display_error_message+"";return{c(){e=z(t)},m(n,s){v(n,e,s)},p(n,s){1&s&&t!==(t=n[0].display_error_message+"")&&K(e,t)},d(n){n&&h(e)}}}function wn(o){let e,t,n,s;return{c(){e=z(`Please
            `),t=M("button"),t.textContent="try again",w(t,"class","svelte-9a9fi8")},m(r,c){v(r,e,c),v(r,t,c),n||(s=ee(t,"click",ye(function(){pe(o[2])&&o[2].apply(this,arguments)})),n=!0)},p(r,c){o=r},d(r){r&&(h(e),h(t)),n=!1,s()}}}function xn(o){let e,t,n,s,r;return{c(){e=z(`Please
            `),t=M("button"),t.textContent="try again",n=z("."),w(t,"class","svelte-9a9fi8")},m(c,i){v(c,e,i),v(c,t,i),v(c,n,i),s||(r=ee(t,"click",ye(function(){pe(o[2])&&o[2].apply(this,arguments)})),s=!0)},p(c,i){o=c},d(c){c&&(h(e),h(t),h(n)),s=!1,r()}}}function Qs(o){let e,t,n,s;const r=[Ws,js,Zs],c=[];function i(a,l){return(!a[0].status||a[0].status===oe.sent)&&a[4].length<=0?0:a[0].status===oe.failed?1:2}return e=i(o),t=c[e]=r[e](o),{c(){t.c(),n=Q()},m(a,l){c[e].m(a,l),v(a,n,l),s=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(U(),p(c[u],1,1,()=>{c[u]=null}),V(),t=c[e],t?t.p(a,l):(t=c[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n))},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),c[e].d(a)}}}function eo(o){let e,t;return e=new Ge({props:{isAugment:!0,$$slots:{content:[Qs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,[s]){const r={};287&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function no(o,e,t){let n,s,{$$slots:r={},$$scope:c}=e,{turn:i}=e,{preamble:a=""}=e,{resendTurn:l}=e,{markdownBlocks:u=[]}=e,d={code:Xt,codespan:Qt,link:Lt};return Ce(()=>{if(i.seen_state===ft.seen)return void t(0,i.response_text=Je(n),i);let m=Date.now();const g=function*(f){for(const I of f)if(I.type==="specialBlock")yield I.text;else for(const A of I.text)yield A}(n);let k=g.next();const L=()=>{let f=Date.now();const I=Math.round((f-m)/8);let A="";for(let P=0;P<I&&!k.done;P++)A+=k.value,k=g.next();t(0,i.response_text+=A,i),m=f,k.done||requestAnimationFrame(L)};L()}),o.$$set=m=>{"turn"in m&&t(0,i=m.turn),"preamble"in m&&t(1,a=m.preamble),"resendTurn"in m&&t(2,l=m.resendTurn),"markdownBlocks"in m&&t(6,u=m.markdownBlocks),"$$scope"in m&&t(8,c=m.$$scope)},o.$$.update=()=>{1&o.$$.dirty&&t(0,i.response_text=i.response_text??"",i),65&o.$$.dirty&&t(3,n=i.response_text?[new de(i.response_text)]:u),1&o.$$.dirty&&t(4,s=i.response_text??"")},[i,a,l,n,s,d,u,r,c]}class to extends Z{constructor(e){super(),j(this,e,no,eo,W,{turn:0,preamble:1,resendTurn:2,markdownBlocks:6})}}function so(o){let e,t,n,s;return e=new Ne.Content({props:{content:o[2]}}),n=new Qn({props:{requestEditorFocus:o[4]}}),{c(){y(e.$$.fragment),t=B(),y(n.$$.fragment)},m(r,c){C(e,r,c),v(r,t,c),C(n,r,c),s=!0},p:N,i(r){s||($(e.$$.fragment,r),$(n.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(n.$$.fragment,r),s=!1},d(r){r&&h(t),_(e,r),_(n,r)}}}function oo(o){let e,t,n={slot:"content",$$slots:{default:[so]},$$scope:{ctx:o}};return e=new Ne.Root({props:n}),o[7](e),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),t=!0},p(s,r){const c={};512&r&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){o[7](null),_(e,s)}}}function ro(o){let e,t,n,s;return e=new Ge({props:{$$slots:{content:[oo]},$$scope:{ctx:o}}}),n=new to({props:{turn:o[1],markdownBlocks:o[3]}}),{c(){y(e.$$.fragment),t=B(),y(n.$$.fragment)},m(r,c){C(e,r,c),v(r,t,c),C(n,r,c),s=!0},p(r,[c]){const i={};513&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){s||($(e.$$.fragment,r),$(n.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(n.$$.fragment,r),s=!1},d(r){r&&h(t),_(e,r),_(n,r)}}}function co(o,e,t){let{flagsModel:n}=e,{turn:s}=e;const r={seen_state:s.seen_state,status:oe.success},c=[[new Re("[**Chat**](https://docs.augmentcode.com/using-augment/chat)"),new de(": Explore your codebase, get up to speed on unfamiliar code, and work through technical problems using natural language.")],[new Re("[**Code Completions**](https://docs.augmentcode.com/using-augment/completions)"),new de(": Receive intelligent code suggestions that take your entire codebase into account as you type.")],[new Re("[**Instructions**](https://docs.augmentcode.com/using-augment/instructions)"),new de(": Use natural language prompts to write or modify code, applied as a diff for your review.")]];n.suggestedEditsAvailable&&c.push([new Re("[**Suggested Edits**](https://docs.augmentcode.com/using-augment/suggested-edits)"),new de(": Take your completions beyond the cursor and across your workspace.")]);let i,a=[new de(`Welcome to Augment!

Augment can help you understand code, debug issues, and ship faster with its deep understanding of your codebase. Here is what Augment can do for you:

`),...c.flatMap((l,u)=>[new de(`${u+1}. `),...l,new de(`

`)]),new de("Ask questions to learn more! Just remember to tag **@Augment** when asking about Augment's capabilities.")];return o.$$set=l=>{"flagsModel"in l&&t(5,n=l.flagsModel),"turn"in l&&t(6,s=l.turn)},[i,r,{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"What can "},{type:"mention",attrs:{id:"Augment",label:"Augment",data:{id:"Augment",label:"Augment"}}},{type:"text",text:" do?"}]}]},a,function(){i==null||i.requestFocus()},n,s,function(l){re[l?"unshift":"push"](()=>{i=l,t(0,i)})}]}class Tc extends Z{constructor(e){super(),j(this,e,co,ro,W,{flagsModel:5,turn:6})}}function io(o){let e,t;return{c(){e=me("svg"),t=me("path"),w(t,"d","M6.04995 2.74998C6.04995 2.44623 5.80371 2.19998 5.49995 2.19998C5.19619 2.19998 4.94995 2.44623 4.94995 2.74998V12.25C4.94995 12.5537 5.19619 12.8 5.49995 12.8C5.80371 12.8 6.04995 12.5537 6.04995 12.25V2.74998ZM10.05 2.74998C10.05 2.44623 9.80371 2.19998 9.49995 2.19998C9.19619 2.19998 8.94995 2.44623 8.94995 2.74998V12.25C8.94995 12.5537 9.19619 12.8 9.49995 12.8C9.80371 12.8 10.05 12.5537 10.05 12.25V2.74998Z"),w(t,"fill","currentColor"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){v(n,e,s),R(e,t)},p:N,i:N,o:N,d(n){n&&h(e)}}}class ao extends Z{constructor(e){super(),j(this,e,null,io,W,{})}}function lo(o){let e,t,n,s,r;return t=new ao({}),{c(){e=M("span"),y(t.$$.fragment),n=z(`
  Waiting for user input`),s=z(o[0]),w(e,"class","c-gen-response svelte-5is5us")},m(c,i){v(c,e,i),C(t,e,null),R(e,n),R(e,s),r=!0},p(c,[i]){(!r||1&i)&&K(s,c[0])},i(c){r||($(t.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),r=!1},d(c){c&&h(e),_(t)}}}function uo(o,e,t){let n=".";return Ce(()=>{const s=setInterval(()=>{t(0,n=n.length>=3?".":n+".")},500);return()=>clearInterval(s)}),[n]}class Rc extends Z{constructor(e){super(),j(this,e,uo,lo,W,{})}}function $o(o){let e;return{c(){e=z("Resuming remote agent...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function yn(o){let e,t;return e=new ot.Root({props:{color:"neutral",size:1,$$slots:{default:[po]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function mo(o){let e,t;return{c(){e=M("span"),t=z(o[2]),w(e,"class","c-resuming-agent__timer svelte-16pyinb"),D(e,"is_minutes",o[0]>=60)},m(n,s){v(n,e,s),R(e,t)},p(n,s){4&s&&K(t,n[2]),1&s&&D(e,"is_minutes",n[0]>=60)},d(n){n&&h(e)}}}function po(o){let e,t;return e=new ne({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[mo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function go(o){let e,t,n,s,r,c,i;n=new _e({props:{size:1}}),r=new ne({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[$o]},$$scope:{ctx:o}}});let a=o[1]&&yn(o);return{c(){e=M("div"),t=M("span"),y(n.$$.fragment),s=B(),y(r.$$.fragment),c=B(),a&&a.c(),w(t,"class","c-resuming-agent__text svelte-16pyinb"),w(e,"class","c-resuming-agent svelte-16pyinb")},m(l,u){v(l,e,u),R(e,t),C(n,t,null),R(t,s),C(r,t,null),R(e,c),a&&a.m(e,null),i=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=yn(l),a.c(),$(a,1),a.m(e,null)):a&&(U(),p(a,1,1,()=>{a=null}),V())},i(l){i||($(n.$$.fragment,l),$(r.$$.fragment,l),$(a),i=!0)},o(l){p(n.$$.fragment,l),p(r.$$.fragment,l),p(a),i=!1},d(l){l&&h(e),_(n),_(r),a&&a.d()}}}function fo(o,e,t){let n,s,r,{timeToTimerMs:c=5e3}=e,i=0,a=Date.now(),l=!1;function u(){t(0,i=Math.floor((Date.now()-a)/1e3))}function d(){t(1,l=!0),u(),r=setInterval(u,1e3)}return Ce(function(){return s=setTimeout(d,c),a=Date.now(),()=>{t(0,i=0),t(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&t(3,c=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&t(2,n=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(i))},[i,l,n,c]}class Ec extends Z{constructor(e){super(),j(this,e,fo,go,W,{timeToTimerMs:3})}}function ho(o){let e,t,n,s,r;return t=new _e({props:{size:1}}),{c(){e=M("span"),y(t.$$.fragment),n=B(),s=z(o[0]),w(e,"class","c-retry-response svelte-1lxm8qk")},m(c,i){v(c,e,i),C(t,e,null),R(e,n),R(e,s),r=!0},p(c,[i]){(!r||1&i)&&K(s,c[0])},i(c){r||($(t.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),r=!1},d(c){c&&h(e),_(t)}}}function vo(o,e,t){let{message:n="Retrying..."}=e;return o.$$set=s=>{"message"in s&&t(0,n=s.message)},[n]}class Mc extends Z{constructor(e){super(),j(this,e,vo,ho,W,{message:0})}}function wo(o){let e,t,n;return e=new Gt({}),{c(){y(e.$$.fragment),t=z(`
    Stopped`)},m(s,r){C(e,s,r),v(s,t,r),n=!0},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){s&&h(t),_(e,s)}}}function xo(o){let e,t,n;return t=new ne({props:{size:1,$$slots:{default:[wo]},$$scope:{ctx:o}}}),{c(){e=M("span"),y(t.$$.fragment),w(e,"class","c-stopped svelte-lv19x6")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,[r]){const c={};1&r&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}class Lc extends Z{constructor(e){super(),j(this,e,null,xo,W,{})}}function yo(o){let e,t;return{c(){e=me("svg"),t=me("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M4.85355 2.14645C5.04882 2.34171 5.04882 2.65829 4.85355 2.85355L3.70711 4H9C11.4853 4 13.5 6.01472 13.5 8.5C13.5 10.9853 11.4853 13 9 13H5C4.72386 13 4.5 12.7761 4.5 12.5C4.5 12.2239 4.72386 12 5 12H9C10.933 12 12.5 10.433 12.5 8.5C12.5 6.567 10.933 5 9 5H3.70711L4.85355 6.14645C5.04882 6.34171 5.04882 6.65829 4.85355 6.85355C4.65829 7.04882 4.34171 7.04882 4.14645 6.85355L2.14645 4.85355C1.95118 4.65829 1.95118 4.34171 2.14645 4.14645L4.14645 2.14645C4.34171 1.95118 4.65829 1.95118 4.85355 2.14645Z"),w(t,"fill","currentColor"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){v(n,e,s),R(e,t)},p:N,i:N,o:N,d(n){n&&h(e)}}}class rt extends Z{constructor(e){super(),j(this,e,null,yo,W,{})}}function Co(o){let e,t,n;return{c(){e=me("svg"),t=me("path"),n=me("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M3.1784 5.56111C3.17842 5.85569 3.41722 6.09449 3.71173 6.09444L9.92275 6.09447C10.0585 6.09447 10.1929 6.06857 10.3189 6.01818L13.9947 4.54786C14.1973 4.46681 14.33 4.27071 14.3301 4.05261C14.33 3.83458 14.1973 3.63846 13.9948 3.55744L10.3189 2.08711C10.1929 2.0367 10.0584 2.01083 9.92278 2.01079L3.71173 2.01079C3.41722 2.01084 3.17844 2.24962 3.1784 2.54412L3.1784 5.56111ZM9.92275 5.0278L4.2451 5.02781L4.24509 3.07749L9.92278 3.07745L11.5339 3.72196L12.2527 4.05263C12.2527 4.05263 11.8167 4.25864 11.534 4.38331C10.9139 4.65675 9.92275 5.0278 9.92275 5.0278Z"),w(t,"fill","currentColor"),w(n,"fill-rule","evenodd"),w(n,"clip-rule","evenodd"),w(n,"d","M8.53346 1.59998C8.53346 1.30543 8.29468 1.06665 8.00013 1.06665C7.70558 1.06665 7.4668 1.30543 7.4668 1.59998V3.07746L8.53346 3.07745V1.59998ZM8.53346 5.0278L7.4668 5.0278V14.4C7.4668 14.6945 7.70558 14.9333 8.00013 14.9333C8.29468 14.9333 8.53346 14.6945 8.53346 14.4V5.0278Z"),w(n,"fill","currentColor"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(s,r){v(s,e,r),R(e,t),R(e,n)},p:N,i:N,o:N,d(s){s&&h(e)}}}class ct extends Z{constructor(e){super(),j(this,e,null,Co,W,{})}}function Cn(o){let e,t,n,s,r;return t=new ns({props:{size:1,insetContent:!0,variant:"ghost",class:"c-checkpoint-tag","data-testid":"checkpoint-version-tag",$$slots:{default:[To]},$$scope:{ctx:o}}}),t.$on("click",o[17]),s=new ke({props:{variant:o[6]?"soft":"ghost-block",color:"neutral",size:1,disabled:o[6]||o[4],class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Eo]},$$scope:{ctx:o}}}),s.$on("click",o[12]),{c(){e=M("div"),y(t.$$.fragment),n=B(),y(s.$$.fragment),w(e,"class","c-checkpoint-container svelte-q20gs5"),w(e,"data-checkpoint-number",o[0]),D(e,"c-checkpoint-container--target-checkpoint",o[6]),D(e,"c-checkpoint-container--dimmed-marker",o[5])},m(c,i){v(c,e,i),C(t,e,null),R(e,n),C(s,e,null),r=!0},p(c,i){const a={};1048778&i&&(a.$$scope={dirty:i,ctx:c}),t.$set(a);const l={};64&i&&(l.variant=c[6]?"soft":"ghost-block"),80&i&&(l.disabled=c[6]||c[4]),1048656&i&&(l.$$scope={dirty:i,ctx:c}),s.$set(l),(!r||1&i)&&w(e,"data-checkpoint-number",c[0]),(!r||64&i)&&D(e,"c-checkpoint-container--target-checkpoint",c[6]),(!r||32&i)&&D(e,"c-checkpoint-container--dimmed-marker",c[5])},i(c){r||($(t.$$.fragment,c),$(s.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),_(t),_(s)}}}function _o(o){let e,t;return e=new ct({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function ko(o){let e,t;return{c(){e=z("Checkpoint "),t=z(o[3])},m(n,s){v(n,e,s),v(n,t,s)},p(n,s){8&s&&K(t,n[3])},d(n){n&&(h(e),h(t))}}}function bo(o){let e,t=Le(o[7])+"";return{c(){e=z(t)},m(n,s){v(n,e,s)},p(n,s){128&s&&t!==(t=Le(n[7])+"")&&K(e,t)},d(n){n&&h(e)}}}function Io(o){let e;return{c(){e=z(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function So(o){let e;function t(r,c){return r[1]?Io:r[6]?bo:void 0}let n=t(o),s=n&&n(o);return{c(){s&&s.c(),e=Q()},m(r,c){s&&s.m(r,c),v(r,e,c)},p(r,c){n===(n=t(r))&&s?s.p(r,c):(s&&s.d(1),s=n&&n(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function To(o){let e,t;return e=new Xn({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[So],text:[ko],leftIcon:[_o]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};1048778&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Ro(o){let e,t;return e=new rt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Eo(o){let e,t;return e=new Se({props:{triggerOn:[Pe.Hover],content:o[6]||o[4]?"Cannot revert to current version":"Revert to this version",$$slots:{default:[Ro]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};80&s&&(r.content=n[6]||n[4]?"Cannot revert to current version":"Revert to this version"),1048576&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Mo(o){let e,t,n=(!o[4]||o[2])&&Cn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,[r]){!s[4]||s[2]?n?(n.p(s,r),20&r&&$(n,1)):(n=Cn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Lo(o,e,t){let n,s,r,c,i,a,l,u,d,m,g,{turn:k}=e;const L=ce("checkpointStore"),{targetCheckpointIdx:f,totalCheckpointCount:I,uuidToIdx:A}=L;function P(E){xt(f,m=E,m)}return le(o,f,E=>t(15,m=E)),le(o,I,E=>t(14,d=E)),le(o,A,E=>t(16,g=E)),o.$$set=E=>{"turn"in E&&t(13,k=E.turn)},o.$$.update=()=>{var E,J,T;73728&o.$$.dirty&&t(0,n=g.get(k.uuid)??-1),8192&o.$$.dirty&&t(7,s=k.toTimestamp),49153&o.$$.dirty&&t(6,(J=d,r=(E=n)===(T=m)||T===void 0&&E===J-1)),49153&o.$$.dirty&&t(5,c=function(S,H,F){return S===F&&F!==void 0&&F<H-1}(n,d,m)),16385&o.$$.dirty&&t(4,i=n===d-1),1&o.$$.dirty&&t(3,a=n+1),8192&o.$$.dirty&&t(2,l=Me(k)),8192&o.$$.dirty&&t(1,u=Me(k)?function(S){var H,F;if((H=S.revertTarget)!=null&&H.uuid){const x=g.get(S.revertTarget.uuid);return x===void 0?void 0:`Reverted to Checkpoint ${x+1}`}return(F=S.revertTarget)!=null&&F.filePath?`Undid changes to ${S.revertTarget.filePath.relPath}`:void 0}(k):void 0)},[n,u,l,a,i,c,r,s,f,I,A,P,async function(){await L.revertToCheckpoint(k.uuid)},k,d,m,g,()=>P(n)]}class Ac extends Z{constructor(e){super(),j(this,e,Lo,Mo,W,{turn:13})}}function Ao(o){let e,t;return e=new ct({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p:N,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function _n(o){let e,t,n,s,r,c,i=o[1]===1?"":"s";return{c(){e=M("span"),t=z("("),n=z(o[1]),s=z(" file"),r=z(i),c=z(")"),w(e,"class","c-checkpoint-files-count")},m(a,l){v(a,e,l),R(e,t),R(e,n),R(e,s),R(e,r),R(e,c)},p(a,l){2&l&&K(n,a[1]),2&l&&i!==(i=a[1]===1?"":"s")&&K(r,i)},d(a){a&&h(e)}}}function zo(o){let e,t,n,s,r=o[1]>0&&_n(o);return{c(){e=z("Checkpoint "),t=z(o[0]),n=B(),r&&r.c(),s=Q()},m(c,i){v(c,e,i),v(c,t,i),v(c,n,i),r&&r.m(c,i),v(c,s,i)},p(c,i){1&i&&K(t,c[0]),c[1]>0?r?r.p(c,i):(r=_n(c),r.c(),r.m(s.parentNode,s)):r&&(r.d(1),r=null)},d(c){c&&(h(e),h(t),h(n),h(s)),r&&r.d(c)}}}function Fo(o){let e;return{c(){e=z(o[2])},m(t,n){v(t,e,n)},p(t,n){4&n&&K(e,t[2])},d(t){t&&h(e)}}}function qo(o){let e;return{c(){e=z(o[3])},m(t,n){v(t,e,n)},p(t,n){8&n&&K(e,t[3])},d(t){t&&h(e)}}}function Bo(o){let e;function t(r,c){return r[3]?qo:r[2]?Fo:void 0}let n=t(o),s=n&&n(o);return{c(){s&&s.c(),e=Q()},m(r,c){s&&s.m(r,c),v(r,e,c)},p(r,c){n===(n=t(r))&&s?s.p(r,c):(s&&s.d(1),s=n&&n(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function kn(o){let e,t,n;return t=new qt({props:{totalAddedLines:o[4].totalAddedLines,totalRemovedLines:o[4].totalRemovedLines}}),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-checkpoint-summary")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const c={};16&r&&(c.totalAddedLines=s[4].totalAddedLines),16&r&&(c.totalRemovedLines=s[4].totalRemovedLines),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function bn(o){let e,t;return e=new ke({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[No]},$$scope:{ctx:o}}}),e.$on("click",function(){pe(o[7])&&o[7].apply(this,arguments)}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){o=n;const r={};256&s&&(r.$$scope={dirty:s,ctx:o}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Po(o){let e,t;return e=new rt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function No(o){let e,t;return e=new Se({props:{triggerOn:[Pe.Hover],content:"Revert to this Checkpoint",$$slots:{default:[Po]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};256&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Ho(o){let e,t,n,s,r,c,i,a;n=new tt({}),r=new Xn({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[Bo],text:[zo],leftIcon:[Ao]},$$scope:{ctx:o}}});let l=o[5]&&kn(o),u=!o[6]&&bn(o);return{c(){e=M("div"),t=M("div"),y(n.$$.fragment),s=B(),y(r.$$.fragment),c=B(),l&&l.c(),i=B(),u&&u.c(),w(t,"class","c-checkpoint-tag"),w(e,"class","c-checkpoint-header svelte-htx8xt")},m(d,m){v(d,e,m),R(e,t),C(n,t,null),R(t,s),C(r,t,null),R(e,c),l&&l.m(e,null),R(e,i),u&&u.m(e,null),a=!0},p(d,[m]){const g={};271&m&&(g.$$scope={dirty:m,ctx:d}),r.$set(g),d[5]?l?(l.p(d,m),32&m&&$(l,1)):(l=kn(d),l.c(),$(l,1),l.m(e,i)):l&&(U(),p(l,1,1,()=>{l=null}),V()),d[6]?u&&(U(),p(u,1,1,()=>{u=null}),V()):u?(u.p(d,m),64&m&&$(u,1)):(u=bn(d),u.c(),$(u,1),u.m(e,null))},i(d){a||($(n.$$.fragment,d),$(r.$$.fragment,d),$(l),$(u),a=!0)},o(d){p(n.$$.fragment,d),p(r.$$.fragment,d),p(l),p(u),a=!1},d(d){d&&h(e),_(n),_(r),l&&l.d(),u&&u.d()}}}function Oo(o,e,t){let{displayCheckpointIdx:n}=e,{filesCount:s=0}=e,{timestamp:r=""}=e,{revertMessage:c}=e,{diffSummary:i={totalAddedLines:0,totalRemovedLines:0}}=e,{hasChanges:a=!1}=e,{isTarget:l=!1}=e,{onRevertClick:u}=e;return o.$$set=d=>{"displayCheckpointIdx"in d&&t(0,n=d.displayCheckpointIdx),"filesCount"in d&&t(1,s=d.filesCount),"timestamp"in d&&t(2,r=d.timestamp),"revertMessage"in d&&t(3,c=d.revertMessage),"diffSummary"in d&&t(4,i=d.diffSummary),"hasChanges"in d&&t(5,a=d.hasChanges),"isTarget"in d&&t(6,l=d.isTarget),"onRevertClick"in d&&t(7,u=d.onRevertClick)},[n,s,r,c,i,a,l,u]}class Do extends Z{constructor(e){super(),j(this,e,Oo,Ho,W,{displayCheckpointIdx:0,filesCount:1,timestamp:2,revertMessage:3,diffSummary:4,hasChanges:5,isTarget:6,onRevertClick:7})}}function In(o,e,t){const n=o.slice();return n[33]=e[t],n}function Sn(o){let e,t,n,s,r,c,i;function a(u){o[27](u)}let l={class:"c-checkpoint-collapsible",stickyHeader:!0,$$slots:{header:[Zo],default:[Go]},$$scope:{ctx:o}};return o[3]!==void 0&&(l.collapsed=o[3]),t=new st({props:l}),re.push(()=>qe(t,"collapsed",a)),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-checkpoint-container svelte-mxd32u"),w(e,"data-checkpoint-number",o[2]),D(e,"c-checkpoint-container--target-checkpoint",o[11]),D(e,"c-checkpoint-container--dimmed-marker",o[10])},m(u,d){v(u,e,d),C(t,e,null),r=!0,c||(i=Zn(s=At.call(null,e,{onVisible:o[28],scrollTarget:document.body})),c=!0)},p(u,d){const m={};6522&d[0]|32&d[1]&&(m.$$scope={dirty:d,ctx:u}),!n&&8&d[0]&&(n=!0,m.collapsed=u[3],Be(()=>n=!1)),t.$set(m),(!r||4&d[0])&&w(e,"data-checkpoint-number",u[2]),s&&pe(s.update)&&1&d[0]&&s.update.call(null,{onVisible:u[28],scrollTarget:document.body}),(!r||2048&d[0])&&D(e,"c-checkpoint-container--target-checkpoint",u[11]),(!r||1024&d[0])&&D(e,"c-checkpoint-container--dimmed-marker",u[10])},i(u){r||($(t.$$.fragment,u),r=!0)},o(u){p(t.$$.fragment,u),r=!1},d(u){u&&h(e),_(t),c=!1,i()}}}function Uo(o){let e,t,n;return t=new ne({props:{size:1,color:"neutral",$$slots:{default:[Jo]},$$scope:{ctx:o}}}),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-edits-list c-edits-list--empty svelte-mxd32u")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const c={};32&r[1]&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Vo(o){let e,t,n=Ae(o[1]),s=[];for(let c=0;c<n.length;c+=1)s[c]=Tn(In(o,n,c));const r=c=>p(s[c],1,1,()=>{s[c]=null});return{c(){e=M("div");for(let c=0;c<s.length;c+=1)s[c].c();w(e,"class","c-edits-list svelte-mxd32u")},m(c,i){v(c,e,i);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);t=!0},p(c,i){if(196610&i[0]){let a;for(n=Ae(c[1]),a=0;a<n.length;a+=1){const l=In(c,n,a);s[a]?(s[a].p(l,i),$(s[a],1)):(s[a]=Tn(l),s[a].c(),$(s[a],1),s[a].m(e,null))}for(U(),a=n.length;a<s.length;a+=1)r(a);V()}},i(c){if(!t){for(let i=0;i<n.length;i+=1)$(s[i]);t=!0}},o(c){s=s.filter(Boolean);for(let i=0;i<s.length;i+=1)p(s[i]);t=!1},d(c){c&&h(e),jn(s,c)}}}function Jo(o){let e;return{c(){e=z("No changes to show")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function Tn(o){let e,t;function n(){return o[25](o[33])}function s(){return o[26](o[33])}return e=new Zt({props:{qualifiedPathName:o[33].qualifiedPathName,lineChanges:o[33].changesSummary,onClickFile:n,onClickReview:s}}),{c(){y(e.$$.fragment)},m(r,c){C(e,r,c),t=!0},p(r,c){o=r;const i={};2&c[0]&&(i.qualifiedPathName=o[33].qualifiedPathName),2&c[0]&&(i.lineChanges=o[33].changesSummary),2&c[0]&&(i.onClickFile=n),2&c[0]&&(i.onClickReview=s),e.$set(i)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){p(e.$$.fragment,r),t=!1},d(r){_(e,r)}}}function Go(o){let e,t,n,s;const r=[Vo,Uo],c=[];function i(a,l){return a[4]?0:a[3]?-1:1}return~(e=i(o))&&(t=c[e]=r[e](o)),{c(){t&&t.c(),n=Q()},m(a,l){~e&&c[e].m(a,l),v(a,n,l),s=!0},p(a,l){let u=e;e=i(a),e===u?~e&&c[e].p(a,l):(t&&(U(),p(c[u],1,1,()=>{c[u]=null}),V()),~e?(t=c[e],t?t.p(a,l):(t=c[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n)):t=null)},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),~e&&c[e].d(a)}}}function Zo(o){let e,t;return e=new Do({props:{slot:"header",displayCheckpointIdx:o[8],filesCount:o[1].length,timestamp:Le(o[12]),revertMessage:o[6],diffSummary:o[5],hasChanges:o[4],isTarget:o[11],onRevertClick:o[18]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};256&s[0]&&(r.displayCheckpointIdx=n[8]),2&s[0]&&(r.filesCount=n[1].length),4096&s[0]&&(r.timestamp=Le(n[12])),64&s[0]&&(r.revertMessage=n[6]),32&s[0]&&(r.diffSummary=n[5]),16&s[0]&&(r.hasChanges=n[4]),2048&s[0]&&(r.isTarget=n[11]),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function jo(o){let e,t,n=(!o[9]||o[7])&&Sn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){!s[9]||s[7]?n?(n.p(s,r),640&r[0]&&$(n,1)):(n=Sn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Wo(o,e,t){let n,s,r,c,i,a,l,u,d,m,g,k,L,f,I,{turn:A}=e;const P=ce("checkpointStore"),E=ce("chatModel"),{targetCheckpointIdx:J,totalCheckpointCount:T,uuidToIdx:S}=P;le(o,J,O=>t(23,f=O)),le(o,T,O=>t(22,L=O)),le(o,S,O=>t(24,I=O));let H=!0;function F(O){E==null||E.extensionClient.openFile({repoRoot:O.rootPath,pathName:O.relPath,allowOutOfWorkspace:!0})}function x(O){E==null||E.extensionClient.showAgentReview(O,r,s,!1)}let X=[],q=!1,G=!1;return o.$$set=O=>{"turn"in O&&t(19,A=O.turn)},o.$$.update=()=>{var O,$e,se;17301504&o.$$.dirty[0]&&t(2,n=I.get(A.uuid)??-1),524288&o.$$.dirty[0]&&t(12,s=A.toTimestamp),524288&o.$$.dirty[0]&&(r=A.fromTimestamp),12582916&o.$$.dirty[0]&&t(11,($e=L,c=(O=n)===(se=f)||se===void 0&&O===$e-1)),12582916&o.$$.dirty[0]&&t(10,i=function(b,ie,ue){return b===ue&&ue!==void 0&&ue<ie-1}(n,L,f)),4194308&o.$$.dirty[0]&&t(9,a=n===L-1),4&o.$$.dirty[0]&&t(8,l=n+1),524288&o.$$.dirty[0]&&t(7,u=Me(A)),524288&o.$$.dirty[0]&&t(6,d=Me(A)?function(b){var ie,ue;if((ie=b.revertTarget)!=null&&ie.uuid){const be=I.get(b.revertTarget.uuid);return be===void 0?void 0:`Reverted to Checkpoint ${be+1}`}return(ue=b.revertTarget)!=null&&ue.filePath?`Undid changes to ${b.revertTarget.filePath.relPath}`:void 0}(A):void 0),2621441&o.$$.dirty[0]&&q&&A&&!G&&P.getCheckpointSummary(A).then(b=>{t(20,X=b),t(21,G=!0)}),1048576&o.$$.dirty[0]&&t(1,m=X.filter(b=>b.changesSummary&&(b.changesSummary.totalAddedLines>0||b.changesSummary.totalRemovedLines>0))),2&o.$$.dirty[0]&&t(5,g=m.reduce((b,ie)=>{var ue,be;return b.totalAddedLines+=((ue=ie.changesSummary)==null?void 0:ue.totalAddedLines)??0,b.totalRemovedLines+=((be=ie.changesSummary)==null?void 0:be.totalRemovedLines)??0,b},{totalAddedLines:0,totalRemovedLines:0})),2&o.$$.dirty[0]&&t(4,k=m.length>0)},[q,m,n,H,k,g,d,u,l,a,i,c,s,J,T,S,F,x,function(){P.revertToCheckpoint(A.uuid)},A,X,G,L,f,I,O=>F(O.qualifiedPathName),O=>x(O.qualifiedPathName),function(O){H=O,t(3,H)},()=>t(0,q=!0)]}class zc extends Z{constructor(e){super(),j(this,e,Wo,jo,W,{turn:19},null,[-1,-1])}}const Rn={[Oe.SUCCESS]:"success",[Oe.FAILED]:"error",[Oe.SKIPPED]:"skipped"},En={[ge.success]:"success",[ge.failure]:"error",[ge.running]:null,[ge.unknown]:"unknown",[ge.skipped]:"skipped"};function Mn(o){return o in Rn?Rn[o]:o in En?En[o]:null}function Ln(o){switch(o){case"success":return"Success";case"error":return"Failed";case"skipped":return"Skipped";case"unknown":return"Unknown";case null:return"Running"}}function An(o){let e,t,n;return t=new ne({props:{size:1,type:"monospace",$$slots:{default:[Ko]},$$scope:{ctx:o}}}),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-command-output__code-block svelte-1t0700a")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const c={};16386&r&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Ko(o){let e;return{c(){e=z(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function zn(o){let e,t,n,s;const r=[Xo,Yo],c=[];function i(a,l){return a[5]?0:1}return t=i(o),n=c[t]=r[t](o),{c(){e=M("div"),n.c(),w(e,"class","c-command-output__code-block c-command-output__code-block--output svelte-1t0700a")},m(a,l){v(a,e,l),c[t].m(e,null),s=!0},p(a,l){let u=t;t=i(a),t===u?c[t].p(a,l):(U(),p(c[u],1,1,()=>{c[u]=null}),V(),n=c[t],n?n.p(a,l):(n=c[t]=r[t](a),n.c()),$(n,1),n.m(e,null))},i(a){s||($(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(e),c[t].d()}}}function Yo(o){let e,t;return e=new ne({props:{size:1,type:"monospace",$$slots:{default:[Qo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16388&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Xo(o){let e,t;return e=new ts.Root({props:{$$slots:{default:[er]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16452&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Qo(o){let e;return{c(){e=z(o[2])},m(t,n){v(t,e,n)},p(t,n){4&n&&K(e,t[2])},d(t){t&&h(e)}}}function er(o){let e,t;return e=new ss({props:{text:o[2],lang:o[6]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};4&s&&(r.text=n[2]),64&s&&(r.lang=n[6]),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function nr(o){let e,t,n,s,r=o[1]&&!o[0]&&An(o),c=o[2]&&(!o[3]||o[3]!=="skipped")&&zn(o);const i=o[12].default,a=he(i,o,o[14],null);return{c(){e=M("div"),r&&r.c(),t=B(),c&&c.c(),n=B(),a&&a.c(),w(e,"class","c-command-output__command-details")},m(l,u){v(l,e,u),r&&r.m(e,null),R(e,t),c&&c.m(e,null),R(e,n),a&&a.m(e,null),s=!0},p(l,u){l[1]&&!l[0]?r?(r.p(l,u),3&u&&$(r,1)):(r=An(l),r.c(),$(r,1),r.m(e,t)):r&&(U(),p(r,1,1,()=>{r=null}),V()),!l[2]||l[3]&&l[3]==="skipped"?c&&(U(),p(c,1,1,()=>{c=null}),V()):c?(c.p(l,u),12&u&&$(c,1)):(c=zn(l),c.c(),$(c,1),c.m(e,n)),a&&a.p&&(!s||16384&u)&&ve(a,i,l,l[14],s?xe(i,l[14],u,null):we(l[14]),null)},i(l){s||($(r),$(c),$(a,l),s=!0)},o(l){p(r),p(c),p(a,l),s=!1},d(l){l&&h(e),r&&r.d(),c&&c.d(),a&&a.d(l)}}}function tr(o){let e;return{c(){e=M("div"),w(e,"class","c-command-output__collapsible-header__spacer svelte-1t0700a")},m(t,n){v(t,e,n)},i:N,o:N,d(t){t&&h(e)}}}function sr(o){let e,t;return e=new tt({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function or(o){let e,t;return e=new ne({props:{size:1,type:"monospace",$$slots:{default:[cr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16386&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function rr(o){let e,t;return e=new ne({props:{size:1,weight:"medium",$$slots:{default:[ir]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16385&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function cr(o){let e;return{c(){e=z(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function ir(o){let e;return{c(){e=z(o[0])},m(t,n){v(t,e,n)},p(t,n){1&n&&K(e,t[0])},d(t){t&&h(e)}}}function ar(o){let e,t,n,s=o[3]==="skipped"&&Fn(o);return t=new Se({props:{content:Ln(o[3]),triggerOn:[Pe.Hover],$$slots:{default:[dr]},$$scope:{ctx:o}}}),{c(){s&&s.c(),e=B(),y(t.$$.fragment)},m(r,c){s&&s.m(r,c),v(r,e,c),C(t,r,c),n=!0},p(r,c){r[3]==="skipped"?s?8&c&&$(s,1):(s=Fn(r),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(U(),p(s,1,1,()=>{s=null}),V());const i={};8&c&&(i.content=Ln(r[3])),16392&c&&(i.$$scope={dirty:c,ctx:r}),t.$set(i)},i(r){n||($(s),$(t.$$.fragment,r),n=!0)},o(r){p(s),p(t.$$.fragment,r),n=!1},d(r){r&&h(e),s&&s.d(r),_(t,r)}}}function lr(o){let e,t,n;return t=new _e({props:{size:1}}),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-command-output__status-icon c-command-output__status-icon--loading svelte-1t0700a")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p:N,i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Fn(o){let e,t;return e=new ne({props:{size:1,$$slots:{default:[ur]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function ur(o){let e;return{c(){e=z("Skipped")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function dr(o){let e,t,n;var s=o[11](o[3]);return s&&(t=nn(s,{})),{c(){e=M("div"),t&&y(t.$$.fragment),w(e,"class","c-command-output__status-icon svelte-1t0700a"),D(e,"c-command-output__status-icon--success",o[3]==="success"),D(e,"c-command-output__status-icon--error",o[3]==="error"),D(e,"c-command-output__status-icon--warning",o[3]==="skipped")},m(r,c){v(r,e,c),t&&C(t,e,null),n=!0},p(r,c){if(8&c&&s!==(s=r[11](r[3]))){if(t){U();const i=t;p(i.$$.fragment,1,0,()=>{_(i,1)}),V()}s?(t=nn(s,{}),y(t.$$.fragment),$(t.$$.fragment,1),C(t,e,null)):t=null}(!n||8&c)&&D(e,"c-command-output__status-icon--success",r[3]==="success"),(!n||8&c)&&D(e,"c-command-output__status-icon--error",r[3]==="error"),(!n||8&c)&&D(e,"c-command-output__status-icon--warning",r[3]==="skipped")},i(r){n||(t&&$(t.$$.fragment,r),n=!0)},o(r){t&&p(t.$$.fragment,r),n=!1},d(r){r&&h(e),t&&_(t)}}}function qn(o){let e,t;return e=new Se({props:{content:o[7],align:"end",$$slots:{default:[mr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};128&s&&(r.content=n[7]),17412&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function $r(o){let e,t;return e=new os({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function mr(o){let e,t;return e=new ke({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[$r]},$$scope:{ctx:o}}}),e.$on("click",o[13]),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};16384&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function pr(o){let e,t,n,s,r,c,i,a,l,u,d,m;const g=[sr,tr],k=[];function L(S,H){return S[8]&&S[3]!=="skipped"?0:1}t=L(o),n=k[t]=g[t](o);const f=[rr,or],I=[];function A(S,H){return S[0]?0:1}r=A(o),c=I[r]=f[r](o);const P=[lr,ar],E=[];function J(S,H){return S[9]?0:S[3]!==null?1:-1}~(l=J(o))&&(u=E[l]=P[l](o));let T=o[2]&&(!o[3]||o[3]!=="skipped")&&!o[9]&&qn(o);return{c(){e=M("div"),n.c(),s=B(),c.c(),i=B(),a=M("div"),u&&u.c(),d=B(),T&&T.c(),w(a,"class","c-command-output__status-indicator svelte-1t0700a"),w(e,"slot","header"),w(e,"class","c-command-output__collapsible-header svelte-1t0700a")},m(S,H){v(S,e,H),k[t].m(e,null),R(e,s),I[r].m(e,null),R(e,i),R(e,a),~l&&E[l].m(a,null),R(a,d),T&&T.m(a,null),m=!0},p(S,H){let F=t;t=L(S),t!==F&&(U(),p(k[F],1,1,()=>{k[F]=null}),V(),n=k[t],n||(n=k[t]=g[t](S),n.c()),$(n,1),n.m(e,s));let x=r;r=A(S),r===x?I[r].p(S,H):(U(),p(I[x],1,1,()=>{I[x]=null}),V(),c=I[r],c?c.p(S,H):(c=I[r]=f[r](S),c.c()),$(c,1),c.m(e,i));let X=l;l=J(S),l===X?~l&&E[l].p(S,H):(u&&(U(),p(E[X],1,1,()=>{E[X]=null}),V()),~l?(u=E[l],u?u.p(S,H):(u=E[l]=P[l](S),u.c()),$(u,1),u.m(a,d)):u=null),!S[2]||S[3]&&S[3]==="skipped"||S[9]?T&&(U(),p(T,1,1,()=>{T=null}),V()):T?(T.p(S,H),524&H&&$(T,1)):(T=qn(S),T.c(),$(T,1),T.m(a,null))},i(S){m||($(n),$(c),$(u),$(T),m=!0)},o(S){p(n),p(c),p(u),p(T),m=!1},d(S){S&&h(e),k[t].d(),I[r].d(),~l&&E[l].d(),T&&T.d()}}}function gr(o){let e,t,n;return t=new st({props:{collapsed:o[4],$$slots:{header:[pr],default:[nr]},$$scope:{ctx:o}}}),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-command-output__container svelte-1t0700a")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,[r]){const c={};16&r&&(c.collapsed=s[4]),18415&r&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function fr(o,e,t){let{$$slots:n={},$$scope:s}=e,{title:r=""}=e,{command:c=""}=e,{output:i=null}=e,{status:a=null}=e,{collapsed:l=!1}=e,{useMonaco:u=!1}=e,{monacoLang:d="bash"}=e,{viewButtonTooltip:m="View full output in editor"}=e,{showCollapseButton:g=!0}=e,{isLoading:k=!1}=e,{onViewOutput:L}=e;return o.$$set=f=>{"title"in f&&t(0,r=f.title),"command"in f&&t(1,c=f.command),"output"in f&&t(2,i=f.output),"status"in f&&t(3,a=f.status),"collapsed"in f&&t(4,l=f.collapsed),"useMonaco"in f&&t(5,u=f.useMonaco),"monacoLang"in f&&t(6,d=f.monacoLang),"viewButtonTooltip"in f&&t(7,m=f.viewButtonTooltip),"showCollapseButton"in f&&t(8,g=f.showCollapseButton),"isLoading"in f&&t(9,k=f.isLoading),"onViewOutput"in f&&t(10,L=f.onViewOutput),"$$scope"in f&&t(14,s=f.$$scope)},[r,c,i,a,l,u,d,m,g,k,L,function(f){return f==="success"||f==="skipped"?Yn:Bt},n,f=>L(f,i),s]}class hr extends Z{constructor(e){super(),j(this,e,fr,gr,W,{title:0,command:1,output:2,status:3,collapsed:4,useMonaco:5,monacoLang:6,viewButtonTooltip:7,showCollapseButton:8,isLoading:9,onViewOutput:10})}}function Bn(o,e,t){const n=o.slice();return n[15]=e[t],n}function vr(o){let e,t,n,s,r;return t=new ne({props:{size:1,color:"secondary",$$slots:{default:[xr]},$$scope:{ctx:o}}}),s=new _e({props:{size:1}}),{c(){e=M("div"),y(t.$$.fragment),n=B(),y(s.$$.fragment),w(e,"class","c-agent-no-setup-logs svelte-12293rd")},m(c,i){v(c,e,i),C(t,e,null),R(e,n),C(s,e,null),r=!0},p(c,i){const a={};262144&i&&(a.$$scope={dirty:i,ctx:c}),t.$set(a)},i(c){r||($(t.$$.fragment,c),$(s.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),_(t),_(s)}}}function wr(o){let e,t,n,s,r,c,i,a,l,u,d,m,g,k,L,f,I,A;r=new ke({props:{variant:"ghost",color:"neutral",size:1,class:"c-agent-setup-logs-toggle-button "+(o[2]?"c-agent-setup-logs-toggle-button--expanded":""),$$slots:{default:[yr]},$$scope:{ctx:o}}}),r.$on("click",o[7]);const P=[_r,Cr],E=[];function J(q,G){return q[0]?0:1}i=J(o),a=E[i]=P[i](o);const T=[Sr,Ir],S=[];function H(q,G){return q[0]?0:1}d=H(o),m=S[d]=T[d](o);let F=Ae(o[3].steps),x=[];for(let q=0;q<F.length;q+=1)x[q]=Pn(Bn(o,F,q));const X=q=>p(x[q],1,1,()=>{x[q]=null});return{c(){e=M("div"),t=M("div"),n=M("div"),s=M("div"),y(r.$$.fragment),c=B(),a.c(),l=B(),u=M("div"),m.c(),g=B(),k=M("div"),L=M("div");for(let q=0;q<x.length;q+=1)x[q].c();w(s,"class","c-agent-setup-logs-summary-left svelte-12293rd"),w(u,"class","c-agent-setup-logs-summary-icon svelte-12293rd"),w(n,"class","c-agent-setup-logs-summary-content svelte-12293rd"),w(t,"class","c-agent-setup-logs-summary svelte-12293rd"),w(t,"role","button"),w(t,"tabindex","0"),w(t,"aria-expanded",o[2]),w(t,"aria-controls","agent-setup-logs-details"),w(L,"class","c-agent-setup-logs svelte-12293rd"),w(k,"class","c-agent-setup-logs-wrapper svelte-12293rd"),D(k,"is-hidden",!o[2]),w(e,"class","c-agent-setup-logs-container svelte-12293rd"),D(e,"c-agent-setup-logs-container--loading",!o[0])},m(q,G){v(q,e,G),R(e,t),R(t,n),R(n,s),C(r,s,null),R(s,c),E[i].m(s,null),R(n,l),R(n,u),S[d].m(u,null),R(e,g),R(e,k),R(k,L);for(let O=0;O<x.length;O+=1)x[O]&&x[O].m(L,null);o[13](e),f=!0,I||(A=[ee(t,"click",o[6]),ee(t,"keydown",o[8])],I=!0)},p(q,G){const O={};4&G&&(O.class="c-agent-setup-logs-toggle-button "+(q[2]?"c-agent-setup-logs-toggle-button--expanded":"")),262144&G&&(O.$$scope={dirty:G,ctx:q}),r.$set(O);let $e=i;i=J(q),i!==$e&&(U(),p(E[$e],1,1,()=>{E[$e]=null}),V(),a=E[i],a||(a=E[i]=P[i](q),a.c()),$(a,1),a.m(s,null));let se=d;if(d=H(q),d!==se&&(U(),p(S[se],1,1,()=>{S[se]=null}),V(),m=S[d],m||(m=S[d]=T[d](q),m.c()),$(m,1),m.m(u,null)),(!f||4&G)&&w(t,"aria-expanded",q[2]),552&G){let b;for(F=Ae(q[3].steps),b=0;b<F.length;b+=1){const ie=Bn(q,F,b);x[b]?(x[b].p(ie,G),$(x[b],1)):(x[b]=Pn(ie),x[b].c(),$(x[b],1),x[b].m(L,null))}for(U(),b=F.length;b<x.length;b+=1)X(b);V()}(!f||4&G)&&D(k,"is-hidden",!q[2]),(!f||1&G)&&D(e,"c-agent-setup-logs-container--loading",!q[0])},i(q){if(!f){$(r.$$.fragment,q),$(a),$(m);for(let G=0;G<F.length;G+=1)$(x[G]);f=!0}},o(q){p(r.$$.fragment,q),p(a),p(m),x=x.filter(Boolean);for(let G=0;G<x.length;G+=1)p(x[G]);f=!1},d(q){q&&h(e),_(r),E[i].d(),S[d].d(),jn(x,q),o[13](null),I=!1,Fe(A)}}}function xr(o){let e;return{c(){e=z("Waiting to start agent environment...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function yr(o){let e,t;return e=new rs({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Cr(o){let e,t;return e=new ne({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[kr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function _r(o){let e,t;return e=new ne({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[br]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function kr(o){let e;return{c(){e=z("Environment is being created...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function br(o){let e;return{c(){e=z("Environment created")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function Ir(o){let e,t;return e=new _e({props:{size:1}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Sr(o){let e,t;return e=new Yn({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Pn(o){let e,t;return e=new hr({props:{title:o[15].step_description,output:o[15].logs,status:Mn(o[15].status),isLoading:o[9](o[15].status),collapsed:!o[9](o[15].status),showCollapseButton:!!o[15].logs,viewButtonTooltip:"View full output in editor",onViewOutput:o[12]}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,s){const r={};8&s&&(r.title=n[15].step_description),8&s&&(r.output=n[15].logs),8&s&&(r.status=Mn(n[15].status)),8&s&&(r.isLoading=n[9](n[15].status)),8&s&&(r.collapsed=!n[9](n[15].status)),8&s&&(r.showCollapseButton=!!n[15].logs),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Tr(o){let e,t,n,s;const r=[wr,vr],c=[];function i(a,l){return a[3]&&a[3].steps&&a[3].steps.length>0?0:1}return e=i(o),t=c[e]=r[e](o),{c(){t.c(),n=Q()},m(a,l){c[e].m(a,l),v(a,n,l),s=!0},p(a,[l]){let u=e;e=i(a),e===u?c[e].p(a,l):(U(),p(c[u],1,1,()=>{c[u]=null}),V(),t=c[e],t?t.p(a,l):(t=c[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n))},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),c[e].d(a)}}}function Rr(o,e,t){let n,s,r,c,i;const a=ce(ze.key);le(o,a,g=>t(11,c=g));const l=ce("chatModel");let u=!1;function d(g){g&&l&&l.extensionClient.openScratchFile(g,"plaintext")}function m(){t(2,u=!u)}return o.$$.update=()=>{var g;2048&o.$$.dirty&&t(10,n=((g=c==null?void 0:c.currentAgent)==null?void 0:g.status)||Te.agentUnspecified),1024&o.$$.dirty&&t(0,s=[Te.agentIdle,Te.agentRunning,Te.agentFailed].includes(n)),2048&o.$$.dirty&&t(3,r=c==null?void 0:c.agentSetupLogs),1&o.$$.dirty&&t(2,u=!s)},[s,i,u,r,a,d,m,function(g){g.stopPropagation(),m()},function(g){g.key!=="Enter"&&g.key!==" "||(g.preventDefault(),m())},function(g){return g===ge.running&&!s},n,c,(g,k)=>d(k),function(g){re[g?"unshift":"push"](()=>{i=g,t(1,i)})}]}class Fc extends Z{constructor(e){super(),j(this,e,Rr,Tr,W,{})}}function Er(o){let e;const t=o[3].default,n=he(t,o,o[4],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||16&r)&&ve(n,t,s,s[4],e?xe(t,s[4],r,null):we(s[4]),null)},i(s){e||($(n,s),e=!0)},o(s){p(n,s),e=!1},d(s){n&&n.d(s)}}}function Mr(o){let e,t;return e=new zt({props:{class:"c-chat-floating-container c-chat-floating-container--"+o[0],xPos:o[1],yPos:o[2],$$slots:{default:[Er]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,[s]){const r={};1&s&&(r.class="c-chat-floating-container c-chat-floating-container--"+n[0]),2&s&&(r.xPos=n[1]),4&s&&(r.yPos=n[2]),16&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Lr(o,e,t){let{$$slots:n={},$$scope:s}=e,{position:r="bottom"}=e,{xPos:c="middle"}=e,{yPos:i=r==="top"?"top":"bottom"}=e;return o.$$set=a=>{"position"in a&&t(0,r=a.position),"xPos"in a&&t(1,c=a.xPos),"yPos"in a&&t(2,i=a.yPos),"$$scope"in a&&t(4,s=a.$$scope)},[r,c,i,n,s]}class Ar extends Z{constructor(e){super(),j(this,e,Lr,Mr,W,{position:0,xPos:1,yPos:2})}}function zr(o){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},o[0]],s={};for(let r=0;r<n.length;r+=1)s=Ie(s,n[r]);return{c(){e=me("svg"),t=new yt(!0),this.h()},l(r){e=Ct(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=_t(e);t=kt(c,!0),c.forEach(h),this.h()},h(){t.a=null,tn(e,s)},m(r,c){bt(r,e,c),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M174.6 472.6c4.5 4.7 10.8 7.4 17.4 7.4s12.8-2.7 17.4-7.4l168-176c9.2-9.6 8.8-24.8-.8-33.9s-24.8-8.8-33.9.8L216 396.1V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v340.1L41.4 263.4c-9.2-9.6-24.3-9.9-33.9-.8s-9.9 24.3-.8 33.9l168 176z"/>',e)},p(r,[c]){tn(e,s=Ue(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&c&&r[0]]))},i:N,o:N,d(r){r&&h(e)}}}function Fr(o,e,t){return o.$$set=n=>{t(0,e=Ie(Ie({},e),sn(n)))},[e=sn(e)]}class qr extends Z{constructor(e){super(),j(this,e,Fr,zr,W,{})}}function Nn(o){let e,t,n;return t=new ke({props:{class:"c-chat-floating-button",variant:"solid",color:"neutral",size:1,radius:"full",$$slots:{default:[Br]},$$scope:{ctx:o}}}),t.$on("click",o[1]),{c(){e=M("div"),y(t.$$.fragment),w(e,"class","c-msg-list-bottom-button svelte-rg7wt6")},m(s,r){v(s,e,r),C(t,e,null),n=!0},p(s,r){const c={};8&r&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),_(t)}}}function Br(o){let e,t;return e=new qr({}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Pr(o){let e,t,n=o[0]&&Nn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[0]?n?(n.p(s,r),1&r&&$(n,1)):(n=Nn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(U(),p(n,1,1,()=>{n=null}),V())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Nr(o){let e,t;return e=new Ar({props:{position:"bottom",$$slots:{default:[Pr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(n,s){C(e,n,s),t=!0},p(n,[s]){const r={};9&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){_(e,n)}}}function Hr(o,e,t){let{showScrollDown:n=!1}=e,{messageListElement:s=null}=e;return o.$$set=r=>{"showScrollDown"in r&&t(0,n=r.showScrollDown),"messageListElement"in r&&t(2,s=r.messageListElement)},[n,()=>{s&&Kn(s,{smooth:!0})},s]}class qc extends Z{constructor(e){super(),j(this,e,Hr,Nr,W,{showScrollDown:0,messageListElement:2})}}function Or(o){let e;return{c(){e=M("div"),e.innerHTML='<span class="c-paused-remote-agent__text svelte-av8nea">This agent is paused and will resume when you send a new message</span>',w(e,"class","c-paused-remote-agent svelte-av8nea")},m(t,n){v(t,e,n)},p:N,i:N,o:N,d(t){t&&h(e)}}}class Bc extends Z{constructor(e){super(),j(this,e,null,Or,W,{})}}function Hn(o){let e,t;return{c(){e=z("Retrying in "),t=z(o[3])},m(n,s){v(n,e,s),v(n,t,s)},p(n,s){8&s&&K(t,n[3])},d(n){n&&(h(e),h(t))}}}function On(o){let e,t,n,s,r,c=o[6]?" now":"";return{c(){e=M("button"),t=z("Retry"),n=z(c),w(e,"class","c-remote-agent-error__button c-remote-agent-error__button--retry")},m(i,a){v(i,e,a),R(e,t),R(e,n),s||(r=ee(e,"click",ye(function(){pe(o[0])&&o[0].apply(this,arguments)})),s=!0)},p(i,a){o=i,64&a&&c!==(c=o[6]?" now":"")&&K(n,c)},d(i){i&&h(e),s=!1,r()}}}function Dn(o){let e,t,n;return{c(){e=M("button"),e.textContent="Delete Agent",w(e,"class","c-remote-agent-error__button c-remote-agent-error__button--delete")},m(s,r){v(s,e,r),t||(n=ee(e,"click",ye(function(){pe(o[1])&&o[1].apply(this,arguments)})),t=!0)},p(s,r){o=s},d(s){s&&h(e),t=!1,n()}}}function Dr(o){let e,t,n,s,r,c,i,a,l=o[6]&&o[3]&&Hn(o),u=o[4]&&On(o),d=o[5]&&Dn(o);return{c(){e=M("div"),t=M("div"),n=M("span"),s=z(o[7]),r=B(),l&&l.c(),c=B(),i=M("div"),u&&u.c(),a=B(),d&&d.c(),w(n,"class","c-remote-agent-error__message"),w(i,"class","c-remote-agent-error__actions"),w(t,"class","c-remote-agent-error__content svelte-g0g7z3"),w(e,"class","c-remote-agent-error svelte-g0g7z3"),D(e,"c-remote-agent-error--unrecoverable",o[2])},m(m,g){v(m,e,g),R(e,t),R(t,n),R(n,s),R(n,r),l&&l.m(n,null),R(t,c),R(t,i),u&&u.m(i,null),R(i,a),d&&d.m(i,null)},p(m,[g]){128&g&&K(s,m[7]),m[6]&&m[3]?l?l.p(m,g):(l=Hn(m),l.c(),l.m(n,null)):l&&(l.d(1),l=null),m[4]?u?u.p(m,g):(u=On(m),u.c(),u.m(i,a)):u&&(u.d(1),u=null),m[5]?d?d.p(m,g):(d=Dn(m),d.c(),d.m(i,null)):d&&(d.d(1),d=null),4&g&&D(e,"c-remote-agent-error--unrecoverable",m[2])},i:N,o:N,d(m){m&&h(e),l&&l.d(),u&&u.d(),d&&d.d()}}}function Ur(o,e,t){let n,s,r,c,i,a,l,u,d,{error:m}=e,{onRetry:g}=e,{onDelete:k}=e;function L(){d&&(d(),d=void 0),c&&(d=cs(c,f=>{t(3,u=f)}))}return Ce(L),Gn(()=>{d==null||d()}),o.$$set=f=>{"error"in f&&t(8,m=f.error),"onRetry"in f&&t(0,g=f.onRetry),"onDelete"in f&&t(1,k=f.onDelete)},o.$$.update=()=>{256&o.$$.dirty&&t(10,n="type"in m),1280&o.$$.dirty&&t(2,s=n&&m.type===Un.agentFailed),256&o.$$.dirty&&t(7,r=m.errorMessage),1280&o.$$.dirty&&t(9,c=n?void 0:m.retryAt),512&o.$$.dirty&&t(6,i=c!==void 0),6&o.$$.dirty&&t(5,a=s&&k),1&o.$$.dirty&&t(4,l=g),512&o.$$.dirty&&c&&L()},[g,k,s,u,l,a,i,r,m,c,n]}class Pc extends Z{constructor(e){super(),j(this,e,Ur,Dr,W,{error:8,onRetry:0,onDelete:1})}}export{Fc as A,Ic as C,Tc as E,_c as G,qc as M,Bc as P,Pc as R,Lc as S,Ms as U,kc as a,Sc as b,bc as c,Rc as d,Gs as e,Ec as f,vc as g,Mc as h,wc as i,to as j,Ac as k,zc as l,cn as m,yc as n,xc as o,Cc as t};

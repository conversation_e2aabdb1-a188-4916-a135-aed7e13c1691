import{p as L}from"./chunk-TMUBEWPD-Cfx1Bhvm.js";import{X as y,O as z,aH as N,D as P,q as V,r as q,s as H,g as I,c as X,b as _,_ as m,l as F,x as G,d as J,E as K,I as Q,a5 as U,k as Y}from"./AugmentMessage-tJmvlmov.js";import{p as Z}from"./gitGraph-YCYPL57B-D98JNnVW.js";import{d as B}from"./arc-O8JIIyzi.js";import{o as tt}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-BGEGncoZ.js";import"./pen-to-square-TjBwLyJp.js";import"./TextTooltipAugment-Cor0M5Er.js";import"./BaseButton-Bbk8_XKh.js";import"./IconButtonAugment-DR78svzs.js";import"./Content-CTqTUTf_.js";import"./globals-D0QH3NT1.js";import"./lodash-D9Au3xFg.js";import"./rules-parser-D8-cU5vK.js";import"./chat-types-NgqNgjwU.js";import"./file-paths-BcSg4gks.js";import"./folder-CJRvA1r9.js";import"./github-DSqbVTgM.js";import"./folder-opened-CVKdq1wC.js";import"./types-BSMhNRWH.js";import"./check-DCGJZZwz.js";import"./types-Cgd-nZOV.js";import"./index-BS_CDetd.js";import"./types-DlPx64PZ.js";import"./index-BDfvUmRL.js";import"./CardAugment-oII5PndH.js";import"./TextAreaAugment-MYqAlrEH.js";import"./diff-utils-DVDPX5m5.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-D3MPLNkT.js";import"./keypress-DD1aQVr0.js";import"./await_block-gmDD0p1L.js";import"./ButtonAugment-DZMhbPz9.js";import"./expand-BMjiP8tK.js";import"./mcp-logo-DrDhW48o.js";import"./open-in-new-window-BWJckjMw.js";import"./ellipsis-DiLEQXmz.js";import"./IconFilePath-87D86vFi.js";import"./LanguageIcon-Ct6C9T4k.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-BYgnDWrw.js";import"./MaterialIcon-MCURCZji.js";import"./Filespan-CprfUNtE.js";import"./chevron-down-D5_sJEjT.js";import"./terminal-CdGA9F9C.js";import"./augment-logo-iIh97Yc5.js";import"./_baseUniq-DriQ5x2c.js";import"./_basePickBy-lj2Cty22.js";import"./clone--VpTwrEC.js";import"./init-g68aIKmP.js";function et(t,r){return r<t?-1:r>t?1:r>=t?0:NaN}function rt(t){return t}var W=P.pie,R={sections:new Map,showData:!1,config:W},M=R.sections,O=R.showData,at=structuredClone(W),j={getConfig:m(()=>structuredClone(at),"getConfig"),clear:m(()=>{M=new Map,O=R.showData,G()},"clear"),setDiagramTitle:V,getDiagramTitle:q,setAccTitle:H,getAccTitle:I,setAccDescription:X,getAccDescription:_,addSection:m(({label:t,value:r})=>{M.has(t)||(M.set(t,r),F.debug(`added new section: ${t}, with value: ${r}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{O=t},"setShowData"),getShowData:m(()=>O,"getShowData")},it=m((t,r)=>{L(t,r),r.setShowData(t.showData),t.sections.map(r.addSection)},"populateDb"),nt={parse:m(async t=>{const r=await Z("pie",t);F.debug(r),it(r,j)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),pt=m(t=>{const r=[...t.entries()].map(p=>({label:p[0],value:p[1]})).sort((p,u)=>u.value-p.value);return function(){var p=rt,u=et,c=null,w=y(0),S=y(z),$=y(0);function a(e){var i,l,n,A,g,s=(e=N(e)).length,v=0,D=new Array(s),d=new Array(s),f=+w.apply(this,arguments),C=Math.min(z,Math.max(-z,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/s,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<s;++i)(g=d[D[i]=i]=+p(e[i],i,e))>0&&(v+=g);for(u!=null?D.sort(function(x,T){return u(d[x],d[T])}):c!=null&&D.sort(function(x,T){return c(e[x],e[T])}),i=0,n=v?(C-s*b)/v:0;i<s;++i,f=A)l=D[i],A=f+((g=d[l])>0?g*n:0)+b,d[l]={data:e[l],index:i,value:g,startAngle:f,endAngle:A,padAngle:h};return d}return a.value=function(e){return arguments.length?(p=typeof e=="function"?e:y(+e),a):p},a.sortValues=function(e){return arguments.length?(u=e,c=null,a):u},a.sort=function(e){return arguments.length?(c=e,u=null,a):c},a.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),a):w},a.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),a):S},a.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),a):$},a}().value(p=>p.value)(r)},"createPieArcs"),ne={parser:nt,db:j,renderer:{draw:m((t,r,p,u)=>{F.debug(`rendering pie chart
`+t);const c=u.db,w=J(),S=K(c.getConfig(),w.pie),$=18,a=450,e=a,i=Q(r),l=i.append("g");l.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[A]=U(n.pieOuterStrokeWidth);A??(A=2);const g=S.textPosition,s=Math.min(e,a)/2-40,v=B().innerRadius(0).outerRadius(s),D=B().innerRadius(s*g).outerRadius(s*g);l.append("circle").attr("cx",0).attr("cy",0).attr("r",s+A/2).attr("class","pieOuterCircle");const d=c.getSections(),f=pt(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=tt(C);l.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),l.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+D.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),l.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=l.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:E}=o.data;return c.getShowData()?`${k} [${E}]`:k});const T=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${T} 450`),Y(i,a,T,S.useMaxWidth)},"draw")},styles:ot};export{ne as diagram};

var W=Object.defineProperty;var O=(t,e,r)=>e in t?W(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var $=(t,e,r)=>O(t,typeof e!="symbol"?e+"":e,r);import{W as C,B}from"./BaseButton-Bbk8_XKh.js";import{S as z,i as _,s as P,a as g,V as U,y as q,c as M,W as I,e as D,z as E,g as T,_ as k,u as b,t as x,h as L,B as F,$ as w,j,a8 as m,R,X as A,Y as N,Z as S}from"./SpinnerAugment-BGEGncoZ.js";class te{constructor(e,r=1e3){$(this,"_idToPromiseFns",new Map);$(this,"registerPromiseContext",e=>new Promise((r,o)=>{this._idToPromiseFns.set(e.requestId,{resolve:r,reject:o})}));$(this,"resolveAsyncMsg",e=>{if(e.type!==C.asyncWrapper)return!1;const r=e,o=this._idToPromiseFns.get(r.requestId);return!!o&&(this._idToPromiseFns.delete(r.requestId),r.error?o.reject(new Error(r.error)):o.resolve(r),!0)});$(this,"rejectAsyncMsg",(e,r)=>{const o=this._idToPromiseFns.get(e.requestId);o&&(this._idToPromiseFns.delete(e.requestId),console.debug(`AsyncMsgSender: Rejecting request, reason: ${r}`,e),o.reject(r))});$(this,"sendOrTimeout",(e,r=this._timeoutMs)=>{this._postMsgFn(e),r>0&&setTimeout(()=>{var o;return this.rejectAsyncMsg(e,new Error(`Request timed out: ${(o=e==null?void 0:e.baseMsg)==null?void 0:o.type}, id: ${e==null?void 0:e.requestId}`))},r)});$(this,"send",async(e,r=this._timeoutMs)=>{const o=y(e),n=this.registerPromiseContext(o);this.sendOrTimeout(o,r);const i=await n;if(i.error)throw new Error(i.error);if(!i.baseMsg)throw new Error("No response or error message");return i.baseMsg});$(this,"sendToSidecar",async(e,r=this._timeoutMs)=>{const o=y(e,"sidecar"),n=this.registerPromiseContext(o);this.sendOrTimeout(o,r);const i=await n;if(i.error)throw new Error(i.error);if(!i.baseMsg)throw new Error("No response or error message");return i.baseMsg});this._postMsgFn=e,this._timeoutMs=r,window.addEventListener("message",o=>{this.resolveAsyncMsg(o.data)})}async*stream(e,r=this._timeoutMs,o=this._timeoutMs){let n=y(e);n.streamCtx={streamMsgIdx:0,streamNextRequestId:""};let i=0,l=!1;try{let a=this.registerPromiseContext(n);this.sendOrTimeout(n,r);const u=new Promise((d,h)=>{o<=0||setTimeout(()=>h(new Error("Stream timed out")),o)});for(;!l;){const d=await Promise.race([a,u]);if((d==null?void 0:d.type)!==C.asyncWrapper)throw new Error(`Received unexpected message: ${d}`);if(d.error)throw new Error(d.error);if(!d.streamCtx||d.streamCtx.isStreamComplete)return;if(!d.baseMsg)throw new Error("No response or error message");if(d.streamCtx.streamMsgIdx!==i){const h=d.streamCtx.streamMsgIdx;throw new Error(`Received out of order stream chunk. Expected ${i} but got ${h}`)}i=d.streamCtx.streamMsgIdx+1,n={...n,streamCtx:{streamMsgIdx:i,streamNextRequestId:""},requestId:d.streamCtx.streamNextRequestId},a=this.registerPromiseContext(n),yield d.baseMsg}}finally{if(!l){l=!0;try{this._idToPromiseFns.delete(n.requestId)}catch(a){console.warn("Error sending stream cancellation message:",a)}}}}}function y(t,e="host"){return{type:C.asyncWrapper,requestId:crypto.randomUUID(),error:null,baseMsg:t,destination:e}}function V(t){let e;const r=t[8].default,o=R(r,t,t[18],null);return{c(){o&&o.c()},m(n,i){o&&o.m(n,i),e=!0},p(n,i){o&&o.p&&(!e||262144&i)&&A(o,r,n,n[18],e?S(r,n[18],i,null):N(n[18]),null)},i(n){e||(b(o,n),e=!0)},o(n){x(o,n),e=!1},d(n){o&&o.d(n)}}}function X(t){let e,r,o,n;const i=[{size:t[0]},{variant:t[1]},{color:t[2]},{highContrast:t[3]},{disabled:t[4]},{radius:t[5]},{class:t[7]},t[6]];let l={$$slots:{default:[V]},$$scope:{ctx:t}};for(let a=0;a<i.length;a+=1)l=g(l,i[a]);return r=new B({props:l}),r.$on("click",t[9]),r.$on("keyup",t[10]),r.$on("keydown",t[11]),r.$on("mousedown",t[12]),r.$on("mouseover",t[13]),r.$on("focus",t[14]),r.$on("mouseleave",t[15]),r.$on("blur",t[16]),r.$on("contextmenu",t[17]),{c(){e=U("div"),q(r.$$.fragment),M(e,"class",o=I(`c-icon-btn c-icon-btn--size-${t[0]}`)+" svelte-1f69byk")},m(a,u){D(a,e,u),E(r,e,null),n=!0},p(a,[u]){const d=255&u?T(i,[1&u&&{size:a[0]},2&u&&{variant:a[1]},4&u&&{color:a[2]},8&u&&{highContrast:a[3]},16&u&&{disabled:a[4]},32&u&&{radius:a[5]},128&u&&{class:a[7]},64&u&&k(a[6])]):{};262144&u&&(d.$$scope={dirty:u,ctx:a}),r.$set(d),(!n||1&u&&o!==(o=I(`c-icon-btn c-icon-btn--size-${a[0]}`)+" svelte-1f69byk"))&&M(e,"class",o)},i(a){n||(b(r.$$.fragment,a),n=!0)},o(a){x(r.$$.fragment,a),n=!1},d(a){a&&L(e),F(r)}}}function Y(t,e,r){let o,n;const i=["size","variant","color","highContrast","disabled","radius"];let l=w(e,i),{$$slots:a={},$$scope:u}=e,{size:d=2}=e,{variant:h="solid"}=e,{color:f="accent"}=e,{highContrast:p=!1}=e,{disabled:s=!1}=e,{radius:v="medium"}=e;return t.$$set=c=>{e=g(g({},e),j(c)),r(19,l=w(e,i)),"size"in c&&r(0,d=c.size),"variant"in c&&r(1,h=c.variant),"color"in c&&r(2,f=c.color),"highContrast"in c&&r(3,p=c.highContrast),"disabled"in c&&r(4,s=c.disabled),"radius"in c&&r(5,v=c.radius),"$$scope"in c&&r(18,u=c.$$scope)},t.$$.update=()=>{r(7,{class:o,...n}=l,o,(r(6,n),r(19,l)))},[d,h,f,p,s,v,n,o,a,function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},u]}class Z extends z{constructor(e){super(),_(this,e,Y,X,P,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5})}}function G(t){let e;const r=t[7].default,o=R(r,t,t[17],null);return{c(){o&&o.c()},m(n,i){o&&o.m(n,i),e=!0},p(n,i){o&&o.p&&(!e||131072&i)&&A(o,r,n,n[17],e?S(r,n[17],i,null):N(n[17]),null)},i(n){e||(b(o,n),e=!0)},o(n){x(o,n),e=!1},d(n){o&&o.d(n)}}}function H(t){let e,r;const o=[{size:t[0]},{variant:t[1]},{color:t[2]},{highContrast:t[3]},{disabled:t[4]},{radius:t[5]},t[6]];let n={$$slots:{default:[G]},$$scope:{ctx:t}};for(let i=0;i<o.length;i+=1)n=g(n,o[i]);return e=new Z({props:n}),e.$on("click",t[8]),e.$on("keyup",t[9]),e.$on("keydown",t[10]),e.$on("mousedown",t[11]),e.$on("mouseover",t[12]),e.$on("focus",t[13]),e.$on("mouseleave",t[14]),e.$on("blur",t[15]),e.$on("contextmenu",t[16]),{c(){q(e.$$.fragment)},m(i,l){E(e,i,l),r=!0},p(i,[l]){const a=127&l?T(o,[1&l&&{size:i[0]},2&l&&{variant:i[1]},4&l&&{color:i[2]},8&l&&{highContrast:i[3]},16&l&&{disabled:i[4]},32&l&&{radius:i[5]},64&l&&k(i[6])]):{};131072&l&&(a.$$scope={dirty:l,ctx:i}),e.$set(a)},i(i){r||(b(e.$$.fragment,i),r=!0)},o(i){x(e.$$.fragment,i),r=!1},d(i){F(e,i)}}}function J(t,e,r){const o=["size","variant","color","highContrast","disabled","radius"];let n=w(e,o),{$$slots:i={},$$scope:l}=e,{size:a=2}=e,{variant:u="solid"}=e,{color:d="accent"}=e,{highContrast:h=!1}=e,{disabled:f=!1}=e,{radius:p="medium"}=e;return t.$$set=s=>{e=g(g({},e),j(s)),r(6,n=w(e,o)),"size"in s&&r(0,a=s.size),"variant"in s&&r(1,u=s.variant),"color"in s&&r(2,d=s.color),"highContrast"in s&&r(3,h=s.highContrast),"disabled"in s&&r(4,f=s.disabled),"radius"in s&&r(5,p=s.radius),"$$scope"in s&&r(17,l=s.$$scope)},[a,u,d,h,f,p,n,i,function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},function(s){m.call(this,t,s)},l]}class re extends z{constructor(e){super(),_(this,e,J,H,P,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5})}}export{te as A,Z as C,re as I};

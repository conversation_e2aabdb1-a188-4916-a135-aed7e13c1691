import{S as ie,i as ae,s as le,a as se,b as Ge,K as Oe,L as je,M as He,N as Ve,h as b,d as xe,O as qe,g as Ke,n as U,j as _e,R as Z,V as k,D as P,E as me,c as y,e as x,f as N,X as J,Y as Q,Z as ee,u as p,q as O,t as d,r as j,aa as $t,T as Me,y as _,z as L,B as C,G,H as te,ao as Pe,a5 as Ae,a7 as mt,w as ve,x as be,A as ye,a3 as we,af as Re,a1 as ot,a0 as sn,am as Xe,a8 as Je,ab as wn,a4 as bt,ax as Qe,ag as it,ad as cn,ae as et}from"./SpinnerAugment-BGEGncoZ.js";import{B as vn,A as an,a as at,b as lt,C as bn,g as yn}from"./main-panel-CdQtCUjm.js";import{d as yt,T as pt}from"./Content-CTqTUTf_.js";import"./lodash-D9Au3xFg.js";import{R as xn}from"./types-BSMhNRWH.js";import{G as dt}from"./folder-CJRvA1r9.js";import{B as Ue}from"./ButtonAugment-DZMhbPz9.js";import{C as rt,P as _n}from"./pen-to-square-TjBwLyJp.js";import{T as Fe}from"./TextTooltipAugment-Cor0M5Er.js";import{f as tt}from"./index-jTYqnJCw.js";import{M as ft,R as ln}from"./magnifying-glass-BYgnDWrw.js";import{C as un,G as gt,T as Ln}from"./github-DSqbVTgM.js";import{e as nt,u as $n,o as mn}from"./BaseButton-Bbk8_XKh.js";import{D as $e,C as Cn,T as An}from"./index-BDfvUmRL.js";import{R as st}from"./check-DCGJZZwz.js";import{I as ct}from"./IconButtonAugment-DR78svzs.js";import{T as pn}from"./terminal-CdGA9F9C.js";import{A as Rn}from"./arrow-up-right-from-square-DWPtT-Jf.js";import{T as dn}from"./Keybindings-BaohEaHR.js";import{R as xt}from"./types-Cgd-nZOV.js";import{E as kn}from"./exclamation-triangle-DimRUUrg.js";import{T as Sn}from"./StatusIndicator-C7Lr9tXQ.js";import"./layer-group-CxYvF2MG.js";import"./design-system-init-BFmnHByk.js";import"./rules-parser-D8-cU5vK.js";import"./chat-types-NgqNgjwU.js";import"./diff-utils-DVDPX5m5.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-D3MPLNkT.js";import"./index-BS_CDetd.js";import"./isObjectLike-CcXmxn1r.js";import"./globals-D0QH3NT1.js";import"./await_block-gmDD0p1L.js";import"./CardAugment-oII5PndH.js";import"./ellipsis-DiLEQXmz.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-CprfUNtE.js";import"./folder-opened-CVKdq1wC.js";import"./MaterialIcon-MCURCZji.js";import"./types-DlPx64PZ.js";import"./open-in-new-window-BWJckjMw.js";import"./TextAreaAugment-MYqAlrEH.js";import"./autofix-state-d-ymFdyn.js";import"./VSCodeCodicon-Ci_TiUhH.js";import"./augment-logo-iIh97Yc5.js";import"./chat-flags-model-B_v7LnHp.js";function _t(r){const e=r.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(e)return{owner:e[1],name:e[2]}}function Ze(r){const e=r.match(/^[^/]+\/HEAD\s*->\s*(.+)$/);if(e)return Ze(e[1]);const n=function(t){const o=t.match(/^refs\/remotes\/([^/]+)\/(.+)$/);if(o)return{remote:o[1],branch:o[2]};const s=t.match(/^([^/]+)\/(.+)$/);if(s){const[,c,a]=s;if(["origin","upstream","fork","github","gitlab","bitbucket"].includes(c)||c.includes("."))return{remote:c,branch:a}}return null}(r);return n?n.branch:r}function In(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=se(o,t[s]);return{c(){e=Ge("svg"),n=new Oe(!0),this.h()},l(s){e=je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=He(e);n=Ve(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){qe(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',e)},p(s,[c]){xe(e,o=Ke(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function Fn(r,e,n){return r.$$set=t=>{n(0,e=se(se({},e),_e(t)))},[e=_e(e)]}class Nn extends ie{constructor(e){super(),ae(this,e,Fn,In,le,{})}}function En(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=se(o,t[s]);return{c(){e=Ge("svg"),n=new Oe(!0),this.h()},l(s){e=je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=He(e);n=Ve(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){qe(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 465c9.4 9.4 24.6 9.4 33.9 0L465 273c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9zM47 81l192 192c9.4 9.4 24.6 9.4 33.9 0L465 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>',e)},p(s,[c]){xe(e,o=Ke(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function Bn(r,e,n){return r.$$set=t=>{n(0,e=se(se({},e),_e(t)))},[e=_e(e)]}class Dn extends ie{constructor(e){super(),ae(this,e,Bn,En,le,{})}}const zn=r=>({}),Lt=r=>({}),Mn=r=>({}),Ct=r=>({}),Pn=r=>({}),At=r=>({}),Un=r=>({}),Rt=r=>({});function Tn(r){let e;return{c(){e=G(r[0])},m(n,t){x(n,e,t)},p(n,t){1&t&&te(e,n[0])},d(n){n&&b(e)}}}function kt(r){let e,n;const t=r[3].subtitle,o=Z(t,r,r[4],Ct),s=o||function(c){let a,i;return a=new Me({props:{size:2,$$slots:{default:[Gn]},$$scope:{ctx:c}}}),{c(){_(a.$$.fragment)},m(l,m){L(a,l,m),i=!0},p(l,m){const u={};18&m&&(u.$$scope={dirty:m,ctx:l}),a.$set(u)},i(l){i||(p(a.$$.fragment,l),i=!0)},o(l){d(a.$$.fragment,l),i=!1},d(l){C(a,l)}}}(r);return{c(){e=k("div"),s&&s.c(),y(e,"class","c-card-button__subtitle svelte-z367s9")},m(c,a){x(c,e,a),s&&s.m(e,null),n=!0},p(c,a){o?o.p&&(!n||16&a)&&J(o,t,c,c[4],n?ee(t,c[4],a,Mn):Q(c[4]),Ct):s&&s.p&&(!n||2&a)&&s.p(c,n?a:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Gn(r){let e;return{c(){e=G(r[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&te(e,n[1])},d(n){n&&b(e)}}}function St(r){let e,n;const t=r[3].iconRight,o=Z(t,r,r[4],Lt);return{c(){e=k("div"),o&&o.c(),y(e,"class","c-card-button__icon-right svelte-z367s9")},m(s,c){x(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||16&c)&&J(o,t,s,s[4],n?ee(t,s[4],c,zn):Q(s[4]),Lt)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function On(r){let e,n,t,o,s,c,a,i;const l=r[3].iconLeft,m=Z(l,r,r[4],Rt),u=r[3].title,g=Z(u,r,r[4],At),w=g||function($){let v,A;return v=new Me({props:{size:2,$$slots:{default:[Tn]},$$scope:{ctx:$}}}),{c(){_(v.$$.fragment)},m(I,D){L(v,I,D),A=!0},p(I,D){const z={};17&D&&(z.$$scope={dirty:D,ctx:I}),v.$set(z)},i(I){A||(p(v.$$.fragment,I),A=!0)},o(I){d(v.$$.fragment,I),A=!1},d(I){C(v,I)}}}(r);let h=r[1]&&kt(r),f=r[2].iconRight&&St(r);return{c(){e=k("div"),m&&m.c(),n=P(),t=k("div"),o=k("div"),w&&w.c(),s=P(),h&&h.c(),c=P(),f&&f.c(),a=me(),y(e,"class","c-card-button__icon-left svelte-z367s9"),y(o,"class","c-card-button__title svelte-z367s9"),y(t,"class","c-card-button__content svelte-z367s9")},m($,v){x($,e,v),m&&m.m(e,null),x($,n,v),x($,t,v),N(t,o),w&&w.m(o,null),N(t,s),h&&h.m(t,null),x($,c,v),f&&f.m($,v),x($,a,v),i=!0},p($,[v]){m&&m.p&&(!i||16&v)&&J(m,l,$,$[4],i?ee(l,$[4],v,Un):Q($[4]),Rt),g?g.p&&(!i||16&v)&&J(g,u,$,$[4],i?ee(u,$[4],v,Pn):Q($[4]),At):w&&w.p&&(!i||1&v)&&w.p($,i?v:-1),$[1]?h?(h.p($,v),2&v&&p(h,1)):(h=kt($),h.c(),p(h,1),h.m(t,null)):h&&(O(),d(h,1,1,()=>{h=null}),j()),$[2].iconRight?f?(f.p($,v),4&v&&p(f,1)):(f=St($),f.c(),p(f,1),f.m(a.parentNode,a)):f&&(O(),d(f,1,1,()=>{f=null}),j())},i($){i||(p(m,$),p(w,$),p(h),p(f),i=!0)},o($){d(m,$),d(w,$),d(h),d(f),i=!1},d($){$&&(b(e),b(n),b(t),b(c),b(a)),m&&m.d($),w&&w.d($),h&&h.d(),f&&f.d($)}}}function jn(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=$t(t);let{title:c="Select an option"}=e,{subtitle:a=""}=e;return r.$$set=i=>{"title"in i&&n(0,c=i.title),"subtitle"in i&&n(1,a=i.subtitle),"$$scope"in i&&n(4,o=i.$$scope)},[c,a,s,t,o]}class fn extends ie{constructor(e){super(),ae(this,e,jn,On,le,{title:0,subtitle:1})}}const Hn=r=>({}),It=r=>({slot:"iconLeft"}),Vn=r=>({}),Ft=r=>({slot:"iconRight"});function Nt(r,e,n){const t=r.slice();return t[19]=e[n],t}const qn=r=>({}),Et=r=>({}),Kn=r=>({}),Bt=r=>({}),Xn=r=>({}),Dt=r=>({slot:"iconLeft"}),Wn=r=>({}),zt=r=>({slot:"title"}),Yn=r=>({}),Mt=r=>({slot:"iconRight"});function Zn(r){let e,n,t,o,s;return n=new fn({props:{title:r[3],subtitle:r[4],$$slots:{iconRight:[eo],iconLeft:[Qn]},$$scope:{ctx:r}}}),{c(){e=k("button"),_(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"type","button"),e.disabled=r[10]},m(c,a){x(c,e,a),L(n,e,null),t=!0,o||(s=[Ae(e,"click",r[16]),Ae(e,"keydown",r[17])],o=!0)},p(c,a){const i={};8&a&&(i.title=c[3]),16&a&&(i.subtitle=c[4]),262144&a&&(i.$$scope={dirty:a,ctx:c}),n.$set(i),(!t||1024&a)&&(e.disabled=c[10])},i(c){t||(p(n.$$.fragment,c),t=!0)},o(c){d(n.$$.fragment,c),t=!1},d(c){c&&b(e),C(n),o=!1,mt(s)}}}function Jn(r){let e,n,t;function o(c){r[15](c)}let s={onOpenChange:r[9],$$slots:{default:[uo]},$$scope:{ctx:r}};return r[1]!==void 0&&(s.requestClose=r[1]),e=new $e.Root({props:s}),ve.push(()=>be(e,"requestClose",o)),{c(){_(e.$$.fragment)},m(c,a){L(e,c,a),t=!0},p(c,a){const i={};512&a&&(i.onOpenChange=c[9]),263641&a&&(i.$$scope={dirty:a,ctx:c}),!n&&2&a&&(n=!0,i.requestClose=c[1],ye(()=>n=!1)),e.$set(i)},i(c){t||(p(e.$$.fragment,c),t=!0)},o(c){d(e.$$.fragment,c),t=!1},d(c){C(e,c)}}}function Qn(r){let e;const n=r[13].iconLeft,t=Z(n,r,r[18],It);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&J(t,n,o,o[18],e?ee(n,o[18],s,Hn):Q(o[18]),It)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function eo(r){let e;const n=r[13].iconRight,t=Z(n,r,r[18],Ft);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&J(t,n,o,o[18],e?ee(n,o[18],s,Vn):Q(o[18]),Ft)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function to(r){let e;const n=r[13].iconLeft,t=Z(n,r,r[18],Dt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&J(t,n,o,o[18],e?ee(n,o[18],s,Xn):Q(o[18]),Dt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function no(r){let e;const n=r[13].title,t=Z(n,r,r[18],zt),o=t||function(s){let c;return{c(){c=G(s[3])},m(a,i){x(a,c,i)},p(a,i){8&i&&te(c,a[3])},d(a){a&&b(c)}}}(r);return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t?t.p&&(!e||262144&c)&&J(t,n,s,s[18],e?ee(n,s[18],c,Wn):Q(s[18]),zt):o&&o.p&&(!e||8&c)&&o.p(s,e?c:-1)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function oo(r){let e;const n=r[13].iconRight,t=Z(n,r,r[18],Mt),o=t||function(s){let c,a;return c=new un({}),{c(){_(c.$$.fragment)},m(i,l){L(c,i,l),a=!0},i(i){a||(p(c.$$.fragment,i),a=!0)},o(i){d(c.$$.fragment,i),a=!1},d(i){C(c,i)}}}();return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t&&t.p&&(!e||262144&c)&&J(t,n,s,s[18],e?ee(n,s[18],c,Yn):Q(s[18]),Mt)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function ro(r){let e,n,t,o;return n=new fn({props:{subtitle:r[4],$$slots:{iconRight:[oo],title:[no],iconLeft:[to]},$$scope:{ctx:r}}}),{c(){e=k("div"),_(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"role","button"),y(e,"tabindex",t=r[10]?-1:0),we(e,"disabled",r[10])},m(s,c){x(s,e,c),L(n,e,null),o=!0},p(s,c){const a={};16&c&&(a.subtitle=s[4]),262152&c&&(a.$$scope={dirty:c,ctx:s}),n.$set(a),(!o||1024&c&&t!==(t=s[10]?-1:0))&&y(e,"tabindex",t),(!o||1024&c)&&we(e,"disabled",s[10])},i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),C(n)}}}function so(r){let e,n;return e=new $e.Label({props:{$$slots:{default:[io]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};262400&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function co(r){let e,n,t=[],o=new Map,s=nt(r[6]);const c=a=>a[7](a[19]);for(let a=0;a<s.length;a+=1){let i=Nt(r,s,a),l=c(i);o.set(l,t[a]=Pt(l,i))}return{c(){for(let a=0;a<t.length;a+=1)t[a].c();e=me()},m(a,i){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(a,i);x(a,e,i),n=!0},p(a,i){2241&i&&(s=nt(a[6]),O(),t=$n(t,i,c,1,a,s,o,e.parentNode,mn,Pt,e,Nt),j())},i(a){if(!n){for(let i=0;i<s.length;i+=1)p(t[i]);n=!0}},o(a){for(let i=0;i<t.length;i+=1)d(t[i]);n=!1},d(a){a&&b(e);for(let i=0;i<t.length;i+=1)t[i].d(a)}}}function io(r){let e;return{c(){e=G(r[8])},m(n,t){x(n,e,t)},p(n,t){256&t&&te(e,n[8])},d(n){n&&b(e)}}}function ao(r){let e,n,t=r[7](r[19])+"";return{c(){e=G(t),n=P()},m(o,s){x(o,e,s),x(o,n,s)},p(o,s){192&s&&t!==(t=o[7](o[19])+"")&&te(e,t)},d(o){o&&(b(e),b(n))}}}function Pt(r,e){let n,t,o;function s(){return e[14](e[19])}return t=new $e.Item({props:{onSelect:s,highlight:e[0]===e[19],$$slots:{default:[ao]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=me(),_(t.$$.fragment),this.first=n},m(c,a){x(c,n,a),L(t,c,a),o=!0},p(c,a){e=c;const i={};64&a&&(i.onSelect=s),65&a&&(i.highlight=e[0]===e[19]),262336&a&&(i.$$scope={dirty:a,ctx:e}),t.$set(i)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),C(t,c)}}}function lo(r){let e,n,t,o,s;const c=r[13]["dropdown-top"],a=Z(c,r,r[18],Bt),i=r[13]["dropdown-content"],l=Z(i,r,r[18],Et),m=l||function(u){let g,w,h,f;const $=[co,so],v=[];function A(I,D){return I[6].length>0?0:1}return g=A(u),w=v[g]=$[g](u),{c(){w.c(),h=me()},m(I,D){v[g].m(I,D),x(I,h,D),f=!0},p(I,D){let z=g;g=A(I),g===z?v[g].p(I,D):(O(),d(v[z],1,1,()=>{v[z]=null}),j(),w=v[g],w?w.p(I,D):(w=v[g]=$[g](I),w.c()),p(w,1),w.m(h.parentNode,h))},i(I){f||(p(w),f=!0)},o(I){d(w),f=!1},d(I){I&&b(h),v[g].d(I)}}}(r);return{c(){e=k("div"),n=k("div"),a&&a.c(),t=P(),o=k("div"),m&&m.c(),y(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),y(o,"class","c-card-button__dropdown-content svelte-1km5ln2"),y(e,"class","c-card__dropdown-contents svelte-1km5ln2")},m(u,g){x(u,e,g),N(e,n),a&&a.m(n,null),N(e,t),N(e,o),m&&m.m(o,null),s=!0},p(u,g){a&&a.p&&(!s||262144&g)&&J(a,c,u,u[18],s?ee(c,u[18],g,Kn):Q(u[18]),Bt),l?l.p&&(!s||262144&g)&&J(l,i,u,u[18],s?ee(i,u[18],g,qn):Q(u[18]),Et):m&&m.p&&(!s||449&g)&&m.p(u,s?g:-1)},i(u){s||(p(a,u),p(m,u),s=!0)},o(u){d(a,u),d(m,u),s=!1},d(u){u&&b(e),a&&a.d(u),m&&m.d(u)}}}function uo(r){let e,n,t,o;return e=new $e.Trigger({props:{$$slots:{default:[ro]},$$scope:{ctx:r}}}),t=new $e.Content({props:{align:"start",side:"bottom",$$slots:{default:[lo]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment),n=P(),_(t.$$.fragment)},m(s,c){L(e,s,c),x(s,n,c),L(t,s,c),o=!0},p(s,c){const a={};263192&c&&(a.$$scope={dirty:c,ctx:s}),e.$set(a);const i={};262593&c&&(i.$$scope={dirty:c,ctx:s}),t.$set(i)},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),C(e,s),C(t,s)}}}function $o(r){let e,n,t,o;const s=[Jn,Zn],c=[];function a(i,l){return i[2]==="dropdown"?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"class","c-card-button svelte-1km5ln2")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,[l]){let m=n;n=a(i),n===m?c[n].p(i,l):(O(),d(c[m],1,1,()=>{c[m]=null}),j(),t=c[n],t?t.p(i,l):(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){d(t),o=!1},d(i){i&&b(e),c[n].d()}}}function mo(r,e,n){let{$$slots:t={},$$scope:o}=e,{type:s="button"}=e,{title:c="Select an option"}=e,{subtitle:a=""}=e,{onClick:i=()=>{}}=e,{items:l=[]}=e,{selectedItem:m}=e,{formatItemLabel:u=A=>(A==null?void 0:A.toString())||""}=e,{noItemsLabel:g="No items found"}=e,{onDropdownOpenChange:w=()=>{}}=e,{requestClose:h=()=>{}}=e,{disabled:f=!1}=e;function $(A){n(0,m=A),v("select",A)}const v=Pe();return r.$$set=A=>{"type"in A&&n(2,s=A.type),"title"in A&&n(3,c=A.title),"subtitle"in A&&n(4,a=A.subtitle),"onClick"in A&&n(5,i=A.onClick),"items"in A&&n(6,l=A.items),"selectedItem"in A&&n(0,m=A.selectedItem),"formatItemLabel"in A&&n(7,u=A.formatItemLabel),"noItemsLabel"in A&&n(8,g=A.noItemsLabel),"onDropdownOpenChange"in A&&n(9,w=A.onDropdownOpenChange),"requestClose"in A&&n(1,h=A.requestClose),"disabled"in A&&n(10,f=A.disabled),"$$scope"in A&&n(18,o=A.$$scope)},[m,h,s,c,a,i,l,u,g,w,f,$,v,t,A=>$(A),function(A){h=A,n(1,h)},()=>{i(),v("click")},A=>{A.key!=="Enter"&&A.key!==" "||(i(),v("click"))},o]}class gn extends ie{constructor(e){super(),ae(this,e,mo,$o,le,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function po(r){let e,n;return e=new gn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[xo],iconLeft:[go]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};4100&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function fo(r){let e,n;return e=new gn({props:{type:"button",title:r[1]?"Cancel":"Connect to GitHub",onClick:r[4],$$slots:{iconRight:[Ao],iconLeft:[_o]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};2&o&&(s.title=t[1]?"Cancel":"Connect to GitHub"),4098&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function go(r){let e,n;return e=new gt({props:{slot:"iconLeft"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ho(r){let e,n;return e=new Me({props:{size:1,weight:"medium",$$slots:{default:[vo]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function wo(r){let e,n,t,o;return e=new Xe({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),t=new Me({props:{size:1,weight:"medium",$$slots:{default:[bo]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment),n=P(),_(t.$$.fragment)},m(s,c){L(e,s,c),x(s,n,c),L(t,s,c),o=!0},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),C(e,s),C(t,s)}}}function vo(r){let e;return{c(){e=G("Revoke Access")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function bo(r){let e;return{c(){e=G("Revoking...")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function yo(r){let e,n,t,o;const s=[wo,ho],c=[];function a(i,l){return i[2]?0:1}return e=a(r),n=c[e]=s[e](r),{c(){n.c(),t=me()},m(i,l){c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let m=e;e=a(i),e!==m&&(O(),d(c[m],1,1,()=>{c[m]=null}),j(),n=c[e],n||(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t))},i(i){o||(p(n),o=!0)},o(i){d(n),o=!1},d(i){i&&b(t),c[e].d(i)}}}function xo(r){let e,n,t;return n=new $e.Item({props:{color:"error",onSelect:r[6],$$slots:{default:[yo]},$$scope:{ctx:r}}}),{c(){e=k("div"),_(n.$$.fragment),y(e,"slot","dropdown-content")},m(o,s){x(o,e,s),L(n,e,null),t=!0},p(o,s){const c={};4100&s&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),C(n)}}}function _o(r){let e,n;return e=new gt({props:{slot:"iconLeft"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Lo(r){let e,n;return e=new Cn({}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Co(r){let e,n;return e=new Xe({props:{size:1,useCurrentColor:!0}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Ao(r){let e,n,t,o;const s=[Co,Lo],c=[];function a(i,l){return i[1]?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"slot","iconRight")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,l){let m=n;n=a(i),n!==m&&(O(),d(c[m],1,1,()=>{c[m]=null}),j(),t=c[n],t||(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){d(t),o=!1},d(i){i&&b(e),c[n].d()}}}function Ro(r){let e,n,t,o,s;const c=[fo,po],a=[];function i(l,m){return l[0]?1:0}return t=i(r),o=a[t]=c[t](r),{c(){e=k("div"),n=k("div"),o.c(),y(n,"class","github-auth-button"),y(e,"class","github-auth-card svelte-zdlnsr")},m(l,m){x(l,e,m),N(e,n),a[t].m(n,null),s=!0},p(l,[m]){let u=t;t=i(l),t===u?a[t].p(l,m):(O(),d(a[u],1,1,()=>{a[u]=null}),j(),o=a[t],o?o.p(l,m):(o=a[t]=c[t](l),o.c()),p(o,1),o.m(n,null))},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),a[t].d()}}}function ko(r,e,n){const t=Pe(),o=Re(dt.key);let s=!1,c=!1,a=!1,i=null,l=null;async function m(){if(!a){n(2,a=!0);try{const u=await o.revokeGithubAccess();u.success?(n(0,s=!1),t("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",u.message)}catch(u){console.error("Error revoking GitHub access:",u)}finally{n(2,a=!1)}}}return ot(async()=>{await async function(){try{const u=await o.isGithubAuthenticated();u!==s?(n(0,s=u),t("authStateChange",{isAuthenticated:s})):n(0,s=u)}catch(u){console.error("Failed to check GitHub authentication status:",u),n(0,s=!1),t("authStateChange",{isAuthenticated:!1})}}()}),sn(()=>{i&&(clearTimeout(i),i=null),l&&(clearInterval(l),l=null)}),[s,c,a,()=>{},async function(){if(c)return n(1,c=!1),void(i&&(clearTimeout(i),i=null));n(1,c=!0);try{await o.authenticateGithub(),l=setInterval(async()=>{await o.isGithubAuthenticated()&&(n(0,s=!0),n(1,c=!1),t("authStateChange",{isAuthenticated:!0}),l&&clearInterval(l),i&&(clearTimeout(i),i=null))},5e3),i=setTimeout(()=>{l&&clearInterval(l),n(1,c=!1),i=null},6e4)}catch(u){console.error("Failed to authenticate with GitHub:",u),n(1,c=!1)}},m,()=>{m()}]}class So extends ie{constructor(e){super(),ae(this,e,ko,Ro,le,{})}}const Io=r=>({}),Ut=r=>({});function Tt(r,e,n){const t=r.slice();return t[27]=e[n],t[29]=n,t}const Fo=r=>({item:64&r}),Gt=r=>({item:r[27]}),No=r=>({}),Ot=r=>({}),Eo=r=>({}),jt=r=>({}),Bo=r=>({}),Ht=r=>({}),Do=r=>({}),Vt=r=>({}),zo=r=>({}),qt=r=>({});function Mo(r){let e,n,t,o,s,c,a,i,l,m,u,g,w,h;const f=[To,Uo],$=[];function v(z,B){return z[4]?0:1}o=v(r),s=$[o]=f[o](r);const A=[Oo,Go],I=[];function D(z,B){return z[17].title?0:1}return i=D(r),l=I[i]=A[i](r),g=new un({}),{c(){e=k("div"),n=k("div"),t=k("div"),s.c(),c=P(),a=k("span"),l.c(),m=P(),u=k("div"),_(g.$$.fragment),y(t,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(a,"class","c-searchable-dropdown__button-text svelte-jowwyu"),y(n,"class","c-searchable-dropdown__icon-text svelte-jowwyu"),y(u,"class","c-searchable-dropdown__chevron svelte-jowwyu"),y(e,"class","c-searchable-dropdown__button svelte-jowwyu"),y(e,"role","button"),y(e,"tabindex",w=r[5]?-1:0),we(e,"c-searchable-dropdown__button--disabled",r[5])},m(z,B){x(z,e,B),N(e,n),N(n,t),$[o].m(t,null),N(n,c),N(n,a),I[i].m(a,null),N(e,m),N(e,u),L(g,u,null),h=!0},p(z,B){let S=o;o=v(z),o===S?$[o].p(z,B):(O(),d($[S],1,1,()=>{$[S]=null}),j(),s=$[o],s?s.p(z,B):(s=$[o]=f[o](z),s.c()),p(s,1),s.m(t,null));let Y=i;i=D(z),i===Y?I[i].p(z,B):(O(),d(I[Y],1,1,()=>{I[Y]=null}),j(),l=I[i],l?l.p(z,B):(l=I[i]=A[i](z),l.c()),p(l,1),l.m(a,null)),(!h||32&B&&w!==(w=z[5]?-1:0))&&y(e,"tabindex",w),(!h||32&B)&&we(e,"c-searchable-dropdown__button--disabled",z[5])},i(z){h||(p(s),p(l),p(g.$$.fragment,z),h=!0)},o(z){d(s),d(l),d(g.$$.fragment,z),h=!1},d(z){z&&b(e),$[o].d(),I[i].d(),C(g)}}}function Po(r){let e,n,t,o,s,c,a,i;const l=r[18].searchIcon,m=Z(l,r,r[25],qt),u=m||function(w){let h;const f=w[18].icon,$=Z(f,w,w[25],Vt);return{c(){$&&$.c()},m(v,A){$&&$.m(v,A),h=!0},p(v,A){$&&$.p&&(!h||33554432&A)&&J($,f,v,v[25],h?ee(f,v[25],A,Do):Q(v[25]),Vt)},i(v){h||(p($,v),h=!0)},o(v){d($,v),h=!1},d(v){$&&$.d(v)}}}(r);let g=r[17].inputButton&&Kt(r);return{c(){e=k("div"),n=k("div"),u&&u.c(),t=P(),o=k("input"),s=P(),g&&g.c(),y(n,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(o,"type","text"),y(o,"class","c-searchable-dropdown__trigger-input svelte-jowwyu"),y(o,"placeholder",r[3]),y(e,"class","c-searchable-dropdown__input-container svelte-jowwyu")},m(w,h){x(w,e,h),N(e,n),u&&u.m(n,null),N(e,t),N(e,o),bt(o,r[0]),N(e,s),g&&g.m(e,null),c=!0,a||(i=[Ae(o,"input",r[21]),Ae(o,"input",r[22]),Ae(o,"click",Qe(r[19])),Ae(o,"mousedown",Qe(r[20]))],a=!0)},p(w,h){m?m.p&&(!c||33554432&h)&&J(m,l,w,w[25],c?ee(l,w[25],h,zo):Q(w[25]),qt):u&&u.p&&(!c||33554432&h)&&u.p(w,c?h:-1),(!c||8&h)&&y(o,"placeholder",w[3]),1&h&&o.value!==w[0]&&bt(o,w[0]),w[17].inputButton?g?(g.p(w,h),131072&h&&p(g,1)):(g=Kt(w),g.c(),p(g,1),g.m(e,null)):g&&(O(),d(g,1,1,()=>{g=null}),j())},i(w){c||(p(u,w),p(g),c=!0)},o(w){d(u,w),d(g),c=!1},d(w){w&&b(e),u&&u.d(w),g&&g.d(),a=!1,mt(i)}}}function Uo(r){let e;const n=r[18].icon,t=Z(n,r,r[25],jt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&J(t,n,o,o[25],e?ee(n,o[25],s,Eo):Q(o[25]),jt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function To(r){let e,n;return e=new Xe({props:{size:1,useCurrentColor:!0}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Go(r){let e,n=(r[4]?r[11]:r[2])+"";return{c(){e=G(n)},m(t,o){x(t,e,o)},p(t,o){2068&o&&n!==(n=(t[4]?t[11]:t[2])+"")&&te(e,n)},i:U,o:U,d(t){t&&b(e)}}}function Oo(r){let e;const n=r[18].title,t=Z(n,r,r[25],Ot);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&J(t,n,o,o[25],e?ee(n,o[25],s,No):Q(o[25]),Ot)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Kt(r){let e;const n=r[18].inputButton,t=Z(n,r,r[25],Ht);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&J(t,n,o,o[25],e?ee(n,o[25],s,Bo):Q(o[25]),Ht)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function jo(r){let e,n,t,o;const s=[Po,Mo],c=[];function a(i,l){return i[12]?0:1}return e=a(r),n=c[e]=s[e](r),{c(){n.c(),t=me()},m(i,l){c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let m=e;e=a(i),e===m?c[e].p(i,l):(O(),d(c[m],1,1,()=>{c[m]=null}),j(),n=c[e],n?n.p(i,l):(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t))},i(i){o||(p(n),o=!0)},o(i){d(n),o=!1},d(i){i&&b(t),c[e].d(i)}}}function Xt(r){let e,n;return e=new $e.Content({props:{side:"bottom",align:"start",$$slots:{default:[Yo]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};33689298&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Ho(r){let e,n;return e=new $e.Item({props:{disabled:!0,$$slots:{default:[Ko]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};33555456&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Vo(r){let e,n,t=[],o=new Map,s=nt(r[6]);const c=a=>a[27]===null?`null-item-${a[29]}`:a[8](a[27],a[29]);for(let a=0;a<s.length;a+=1){let i=Tt(r,s,a),l=c(i);o.set(l,t[a]=Wt(l,i))}return{c(){for(let a=0;a<t.length;a+=1)t[a].c();e=me()},m(a,i){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(a,i);x(a,e,i),n=!0},p(a,i){33620930&i&&(s=nt(a[6]),O(),t=$n(t,i,c,1,a,s,o,e.parentNode,mn,Wt,e,Tt),j())},i(a){if(!n){for(let i=0;i<s.length;i+=1)p(t[i]);n=!0}},o(a){for(let i=0;i<t.length;i+=1)d(t[i]);n=!1},d(a){a&&b(e);for(let i=0;i<t.length;i+=1)t[i].d(a)}}}function qo(r){let e,n;return e=new $e.Item({props:{disabled:!0,$$slots:{default:[Wo]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};33556480&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Ko(r){let e;return{c(){e=G(r[10])},m(n,t){x(n,e,t)},p(n,t){1024&t&&te(e,n[10])},d(n){n&&b(e)}}}function Xo(r){let e,n;const t=r[18].item,o=Z(t,r,r[25],Gt),s=o||function(c){let a,i=c[7](c[27])+"";return{c(){a=G(i)},m(l,m){x(l,a,m)},p(l,m){192&m&&i!==(i=l[7](l[27])+"")&&te(a,i)},d(l){l&&b(a)}}}(r);return{c(){s&&s.c(),e=P()},m(c,a){s&&s.m(c,a),x(c,e,a),n=!0},p(c,a){o?o.p&&(!n||33554496&a)&&J(o,t,c,c[25],n?ee(t,c[25],a,Fo):Q(c[25]),Gt):s&&s.p&&(!n||192&a)&&s.p(c,n?a:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Wt(r,e){let n,t,o;function s(){return e[23](e[27])}return t=new $e.Item({props:{onSelect:s,highlight:e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27]),$$slots:{default:[Xo]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=me(),_(t.$$.fragment),this.first=n},m(c,a){x(c,n,a),L(t,c,a),o=!0},p(c,a){e=c;const i={};64&a&&(i.onSelect=s),706&a&&(i.highlight=e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27])),33554624&a&&(i.$$scope={dirty:a,ctx:e}),t.$set(i)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),C(t,c)}}}function Wo(r){let e,n,t,o,s,c;return n=new Xe({props:{size:1,useCurrentColor:!0}}),{c(){e=k("div"),_(n.$$.fragment),t=P(),o=k("span"),s=G(r[11]),y(e,"class","c-searchable-dropdown__loading svelte-jowwyu")},m(a,i){x(a,e,i),L(n,e,null),N(e,t),N(e,o),N(o,s),c=!0},p(a,i){(!c||2048&i)&&te(s,a[11])},i(a){c||(p(n.$$.fragment,a),c=!0)},o(a){d(n.$$.fragment,a),c=!1},d(a){a&&b(e),C(n)}}}function Yt(r){let e;const n=r[18].footer,t=Z(n,r,r[25],Ut);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&J(t,n,o,o[25],e?ee(n,o[25],s,Io):Q(o[25]),Ut)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Yo(r){let e,n,t,o,s,c;const a=[qo,Vo,Ho],i=[];function l(u,g){return u[4]?0:u[6].length>0?1:u[10]?2:-1}~(e=l(r))&&(n=i[e]=a[e](r));let m=r[17].footer&&Yt(r);return{c(){n&&n.c(),t=P(),m&&m.c(),o=P(),s=k("div"),wn(s,"margin-bottom","var(--ds-spacing-2)")},m(u,g){~e&&i[e].m(u,g),x(u,t,g),m&&m.m(u,g),x(u,o,g),x(u,s,g),c=!0},p(u,g){let w=e;e=l(u),e===w?~e&&i[e].p(u,g):(n&&(O(),d(i[w],1,1,()=>{i[w]=null}),j()),~e?(n=i[e],n?n.p(u,g):(n=i[e]=a[e](u),n.c()),p(n,1),n.m(t.parentNode,t)):n=null),u[17].footer?m?(m.p(u,g),131072&g&&p(m,1)):(m=Yt(u),m.c(),p(m,1),m.m(o.parentNode,o)):m&&(O(),d(m,1,1,()=>{m=null}),j())},i(u){c||(p(n),p(m),c=!0)},o(u){d(n),d(m),c=!1},d(u){u&&(b(t),b(o),b(s)),~e&&i[e].d(u),m&&m.d(u)}}}function Zo(r){let e,n,t,o;e=new $e.Trigger({props:{$$slots:{default:[jo]},$$scope:{ctx:r}}});let s=!r[5]&&Xt(r);return{c(){_(e.$$.fragment),n=P(),s&&s.c(),t=me()},m(c,a){L(e,c,a),x(c,n,a),s&&s.m(c,a),x(c,t,a),o=!0},p(c,a){const i={};33691709&a&&(i.$$scope={dirty:a,ctx:c}),e.$set(i),c[5]?s&&(O(),d(s,1,1,()=>{s=null}),j()):s?(s.p(c,a),32&a&&p(s,1)):(s=Xt(c),s.c(),p(s,1),s.m(t.parentNode,t))},i(c){o||(p(e.$$.fragment,c),p(s),o=!0)},o(c){d(e.$$.fragment,c),d(s),o=!1},d(c){c&&(b(n),b(t)),C(e,c),s&&s.d(c)}}}function Jo(r){let e,n,t,o;function s(a){r[24](a)}let c={onOpenChange:r[14],$$slots:{default:[Zo]},$$scope:{ctx:r}};return r[13]!==void 0&&(c.requestClose=r[13]),n=new $e.Root({props:c}),ve.push(()=>be(n,"requestClose",s)),{c(){e=k("div"),_(n.$$.fragment),y(e,"class","c-searchable-dropdown svelte-jowwyu")},m(a,i){x(a,e,i),L(n,e,null),o=!0},p(a,[i]){const l={};33693439&i&&(l.$$scope={dirty:i,ctx:a}),!t&&8192&i&&(t=!0,l.requestClose=a[13],ye(()=>t=!1)),n.$set(l)},i(a){o||(p(n.$$.fragment,a),o=!0)},o(a){d(n.$$.fragment,a),o=!1},d(a){a&&b(e),C(n)}}}function Qo(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=$t(t);let{title:c=""}=e,{placeholder:a="Search..."}=e,{isLoading:i=!1}=e,{disabled:l=!1}=e,{searchValue:m=""}=e,{items:u=[]}=e,{selectedItem:g=null}=e,{itemLabelFn:w=S=>(S==null?void 0:S.toString())||""}=e,{itemKeyFn:h=S=>(S==null?void 0:S.toString())||""}=e,{isItemSelected:f}=e,{noItemsLabel:$="No items found"}=e,{loadingLabel:v="Loading..."}=e,A=!1,I=()=>{};const D=Pe();function z(S){n(0,m=S),D("search",S)}function B(S){n(1,g=S),D("select",S),I()}return r.$$set=S=>{"title"in S&&n(2,c=S.title),"placeholder"in S&&n(3,a=S.placeholder),"isLoading"in S&&n(4,i=S.isLoading),"disabled"in S&&n(5,l=S.disabled),"searchValue"in S&&n(0,m=S.searchValue),"items"in S&&n(6,u=S.items),"selectedItem"in S&&n(1,g=S.selectedItem),"itemLabelFn"in S&&n(7,w=S.itemLabelFn),"itemKeyFn"in S&&n(8,h=S.itemKeyFn),"isItemSelected"in S&&n(9,f=S.isItemSelected),"noItemsLabel"in S&&n(10,$=S.noItemsLabel),"loadingLabel"in S&&n(11,v=S.loadingLabel),"$$scope"in S&&n(25,o=S.$$scope)},[m,g,c,a,i,l,u,w,h,f,$,v,A,I,function(S){if(!l){if(n(12,A=S),S&&g){const Y=w(g);n(0,m=Y),D("search",""),setTimeout(()=>{const H=document.querySelector(".c-searchable-dropdown__trigger-input");H&&H.select()},0)}else S&&(n(0,m=""),D("search",""));D("openChange",S)}},z,B,s,t,function(S){Je.call(this,r,S)},function(S){Je.call(this,r,S)},function(){m=this.value,n(0,m)},S=>z(S.currentTarget.value),S=>B(S),function(S){I=S,n(13,I)},o]}class ut extends ie{constructor(e){super(),ae(this,e,Qo,Jo,le,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,itemKeyFn:8,isItemSelected:9,noItemsLabel:10,loadingLabel:11})}}function er(r){let e,n,t;return n=new rt({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[cr]},$$scope:{ctx:r}}}),{c(){e=k("div"),_(n.$$.fragment),y(e,"class","c-commit-ref-selector__error svelte-btbfel")},m(o,s){x(o,e,s),L(n,e,null),t=!0},p(o,s){const c={};8195&s[0]|16&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),C(n)}}}function tr(r){var h;let e,n,t,o,s,c,a,i,l;function m(f){r[32](f)}let u={title:r[17],placeholder:"Search repositories...",itemKeyFn:yr,isLoading:r[9],disabled:!r[7].length,items:r[8],selectedItem:r[2],itemLabelFn:xr,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[ar],icon:[ir]},$$scope:{ctx:r}};function g(f){r[37](f)}r[12]!==void 0&&(u.searchValue=r[12]),t=new ut({props:u}),ve.push(()=>be(t,"searchValue",m)),t.$on("openChange",r[33]),t.$on("search",r[34]),t.$on("select",r[35]);let w={title:((h=r[4])==null?void 0:h.name)||"Choose branch...",itemKeyFn:_r,placeholder:"Search branches...",isLoading:r[18],disabled:r[14],items:r[16],selectedItem:r[4],itemLabelFn:Lr,noItemsLabel:r[5]?"":"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[vr],inputButton:[pr],searchIcon:[ur],icon:[lr]},$$scope:{ctx:r}};return r[6]!==void 0&&(w.searchValue=r[6]),a=new ut({props:w}),ve.push(()=>be(a,"searchValue",g)),a.$on("openChange",r[38]),a.$on("search",r[39]),a.$on("select",r[40]),{c(){e=k("div"),n=k("div"),_(t.$$.fragment),s=P(),c=k("div"),_(a.$$.fragment),y(n,"class","c-commit-ref-selector__selector svelte-btbfel"),y(c,"class","c-commit-ref-selector__selector svelte-btbfel"),y(e,"class","c-commit-ref-selector__selectors-container svelte-btbfel")},m(f,$){x(f,e,$),N(e,n),L(t,n,null),N(e,s),N(e,c),L(a,c,null),l=!0},p(f,$){var I;const v={};131072&$[0]&&(v.title=f[17]),512&$[0]&&(v.isLoading=f[9]),128&$[0]&&(v.disabled=!f[7].length),256&$[0]&&(v.items=f[8]),4&$[0]&&(v.selectedItem=f[2]),16&$[2]&&(v.$$scope={dirty:$,ctx:f}),!o&&4096&$[0]&&(o=!0,v.searchValue=f[12],ye(()=>o=!1)),t.$set(v);const A={};16&$[0]&&(A.title=((I=f[4])==null?void 0:I.name)||"Choose branch..."),262144&$[0]&&(A.isLoading=f[18]),16384&$[0]&&(A.disabled=f[14]),65536&$[0]&&(A.items=f[16]),16&$[0]&&(A.selectedItem=f[4]),32&$[0]&&(A.noItemsLabel=f[5]?"":"No branches found"),3112&$[0]|16&$[2]&&(A.$$scope={dirty:$,ctx:f}),!i&&64&$[0]&&(i=!0,A.searchValue=f[6],ye(()=>i=!1)),a.$set(A)},i(f){l||(p(t.$$.fragment,f),p(a.$$.fragment,f),l=!0)},o(f){d(t.$$.fragment,f),d(a.$$.fragment,f),l=!1},d(f){f&&b(e),C(t),C(a)}}}function nr(r){let e,n;return e=new Ue({props:{variant:"ghost",color:"warning",size:1,loading:r[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[sr],default:[rr]},$$scope:{ctx:r}}}),e.$on("click",r[23]),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};2&o[0]&&(s.loading=t[1]),16&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function or(r){let e,n;return e=new So({}),e.$on("authStateChange",r[41]),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function rr(r){let e;return{c(){e=G("Reload available repos and branches")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function sr(r){let e,n,t;return n=new ln({}),{c(){e=k("span"),_(n.$$.fragment),y(e,"slot","iconLeft"),y(e,"class","svelte-btbfel")},m(o,s){x(o,e,s),L(n,e,null),t=!0},p:U,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),C(n)}}}function cr(r){let e,n,t,o,s,c,a;const i=[or,nr],l=[];function m(u,g){return u[13]?1:0}return s=m(r),c=l[s]=i[s](r),{c(){e=k("div"),n=k("div"),t=G(r[0]),o=P(),c.c(),y(n,"class","c-commit-ref-selector__error-message svelte-btbfel"),y(e,"class","c-commit-ref-selector__error-content svelte-btbfel")},m(u,g){x(u,e,g),N(e,n),N(n,t),N(e,o),l[s].m(e,null),a=!0},p(u,g){(!a||1&g[0])&&te(t,u[0]);let w=s;s=m(u),s===w?l[s].p(u,g):(O(),d(l[w],1,1,()=>{l[w]=null}),j(),c=l[s],c?c.p(u,g):(c=l[s]=i[s](u),c.c()),p(c,1),c.m(e,null))},i(u){a||(p(c),a=!0)},o(u){d(c),a=!1},d(u){u&&b(e),l[s].d()}}}function ir(r){let e,n;return e=new gt({props:{slot:"icon"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ar(r){let e,n;return e=new ft({props:{slot:"searchIcon"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function lr(r){let e,n;return e=new vn({props:{slot:"icon"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ur(r){let e,n;return e=new ft({props:{slot:"searchIcon"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function $r(r){let e,n;return e=new Nn({}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function mr(r){let e,n;return e=new ct({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[$r]},$$scope:{ctx:r}}}),e.$on("click",r[28]),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};16&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function pr(r){let e,n,t;return n=new Fe({props:{content:"Refresh branches",triggerOn:[pt.Hover],nested:!1,$$slots:{default:[mr]},$$scope:{ctx:r}}}),{c(){e=k("div"),_(n.$$.fragment),y(e,"slot","inputButton"),y(e,"class","c-commit-ref-selector__refresh-button svelte-btbfel")},m(o,s){x(o,e,s),L(n,e,null),t=!0},p(o,s){const c={};16&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),C(n)}}}function dr(r){let e,n,t,o,s,c,a;return t=new Xe({props:{size:1}}),c=new Me({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[gr]},$$scope:{ctx:r}}}),{c(){e=k("div"),n=k("div"),_(t.$$.fragment),o=P(),s=k("div"),_(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),y(s,"class","c-commit-ref-selector__item-content svelte-btbfel"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading c-commit-ref-selector__item--disabled svelte-btbfel")},m(i,l){x(i,e,l),N(e,n),L(t,n,null),N(e,o),N(e,s),L(c,s,null),a=!0},p(i,l){const m={};16&l[2]&&(m.$$scope={dirty:l,ctx:i}),c.$set(m)},i(i){a||(p(t.$$.fragment,i),p(c.$$.fragment,i),a=!0)},o(i){d(t.$$.fragment,i),d(c.$$.fragment,i),a=!1},d(i){i&&b(e),C(t),C(c)}}}function fr(r){let e,n;return e=new Fe({props:{content:`${r[3].length} branches loaded`,triggerOn:[pt.Hover],nested:!1,$$slots:{default:[wr]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};8&o[0]&&(s.content=`${t[3].length} branches loaded`),1024&o[0]|16&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function gr(r){let e;return{c(){e=G("Loading branches...")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function hr(r){let e;return{c(){e=G("Load more branches")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function wr(r){let e,n,t,o,s,c,a,i,l;return t=new Dn({}),c=new Me({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[hr]},$$scope:{ctx:r}}}),{c(){e=k("button"),n=k("div"),_(t.$$.fragment),o=P(),s=k("div"),_(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-btbfel"),y(s,"class","c-commit-ref-selector__item-content svelte-btbfel"),y(e,"type","button"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-btbfel")},m(m,u){x(m,e,u),N(e,n),L(t,n,null),N(e,o),N(e,s),L(c,s,null),a=!0,i||(l=Ae(e,"click",r[36]),i=!0)},p(m,u){const g={};16&u[2]&&(g.$$scope={dirty:u,ctx:m}),c.$set(g)},i(m){a||(p(t.$$.fragment,m),p(c.$$.fragment,m),a=!0)},o(m){d(t.$$.fragment,m),d(c.$$.fragment,m),a=!1},d(m){m&&b(e),C(t),C(c),i=!1,l()}}}function vr(r){let e,n,t,o;const s=[fr,dr],c=[];function a(i,l){return i[11]&&!i[5]?0:i[5]?1:-1}return~(e=a(r))&&(n=c[e]=s[e](r)),{c(){n&&n.c(),t=me()},m(i,l){~e&&c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let m=e;e=a(i),e===m?~e&&c[e].p(i,l):(n&&(O(),d(c[m],1,1,()=>{c[m]=null}),j()),~e?(n=c[e],n?n.p(i,l):(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t)):n=null)},i(i){o||(p(n),o=!0)},o(i){d(n),o=!1},d(i){i&&b(t),~e&&c[e].d(i)}}}function br(r){let e,n,t,o,s;const c=[tr,er],a=[];function i(l,m){return l[15]?l[15]?1:-1:0}return~(t=i(r))&&(o=a[t]=c[t](r)),{c(){e=k("div"),n=k("div"),o&&o.c(),y(n,"class","c-commit-ref-selector__content svelte-btbfel"),y(e,"class","c-commit-ref-selector svelte-btbfel")},m(l,m){x(l,e,m),N(e,n),~t&&a[t].m(n,null),s=!0},p(l,m){let u=t;t=i(l),t===u?~t&&a[t].p(l,m):(o&&(O(),d(a[u],1,1,()=>{a[u]=null}),j()),~t?(o=a[t],o?o.p(l,m):(o=a[t]=c[t](l),o.c()),p(o,1),o.m(n,null)):o=null)},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),~t&&a[t].d()}}}const yr=r=>`${r.owner}-${r.name}`,xr=r=>`${r.owner}/${r.name}`,_r=(r,e)=>`${r.name}-${r.commit.sha}=${e}`,Lr=r=>r.name.replace("origin/","");function Cr(r,e,n){let t,o,s,c,a;const i=Re(dt.key),l=Pe(),m=Re(st.key);let u,g,{errorMessage:w=""}=e,{isLoading:h=!1}=e,{lastUsedBranchName:f=null}=e,{lastUsedRepoUrl:$=null}=e,v=[],A=v,I=[],D=!1,z=!1,B=0,S=!1;const Y=new Set;let H=!1,X=!1,oe="",ne="";function pe(R){n(12,oe=R),H=!0,hn(R)}const Ne=yt(function(R){n(6,ne=R),n(31,X=!0)},300,{leading:!1,trailing:!0}),de={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};async function V(){var Ye;console.warn("Fetching branches from local git environment.");const{remoteUrl:R,error:T}=await i.getRemoteUrl();n(1,h=!0);const K=_t(R);if(!K||T)return W(T??de.failedToParseRemoteUrl),void n(1,h=!1);n(2,u={name:K.name,owner:K.owner,html_url:R}),n(7,v=[u]),n(8,A=v);const De=function(ge){const Ie=ge.find(re=>re.isCurrentBranch),Ee=ge.find(re=>re.isDefault),Ce=!!Ie&&(Ie==null?void 0:Ie.name)===Ze((Ee==null?void 0:Ee.name)||"");return ge.filter(re=>(!Ce||!re.isDefault)&&(!re.isCurrentBranch||!re.isRemote)&&!!re.isRemote&&re.isRemote)}((await i.listBranches()).branches),ze=De.find(ge=>ge.isDefault);n(4,g={name:Ze(ze!=null&&ze.name?ze.name:((Ye=De[0])==null?void 0:Ye.name)||""),commit:{sha:"",url:""},protected:!1}),n(3,I=De.map(ge=>({name:Ze(ge.name),commit:{sha:"",url:""},protected:!1}))),t||fe(),n(1,h=!1)}async function Be(){n(9,z=!0),n(5,D=!0);const{repos:R,error:T,isDevDeploy:K}=await i.listUserRepos();if(K)return await V(),void n(9,z=!1);if(T)return W(`An error occured while fetching your repos. If this continues, please contact support. Error: ${T}`),n(1,h=!1),void n(9,z=!1);n(7,v=R),n(8,A=v);const{remoteUrl:De,error:ze}=await i.getRemoteUrl(),Ye=_t(De);if(ze)return n(1,h=!1),void n(9,z=!1);const{owner:ge,name:Ie}=Ye||{},Ee=v.find(Ce=>Ce.name===Ie&&Ce.owner===ge);if(Ee&&!u)n(2,u=Ee);else if(!Ee&&Ie&&ge){const Ce={name:Ie,owner:ge,html_url:De};try{const{repo:re,error:vt}=await i.getGithubRepo(Ce);vt?(console.warn("Failed to fetch GitHub repo details:",vt),n(2,u=v[0])):(n(2,u=re),n(7,v=[u,...v]))}catch(re){console.error("Error fetching GitHub repo:",re),n(2,u=v[0])}}else if(!u)return n(1,h=!1),void n(9,z=!1);n(9,z=!1),function(){Y.clear();const Ce=new Set;v.forEach(re=>{Ce.has(re.name)?Y.add(re.name):Ce.add(re.name)})}()}if(ot(async()=>{await M()}),!u&&$){const R=v.find(T=>T.html_url===$);R&&(u=R)}async function ke(R){if(!u)return;n(5,D=!0);const T=u;do{if(T!==u){n(5,D=!1),n(3,I=[]);break}const K=await i.listRepoBranches(u,R);if(K.error)return W(`Failed to fetch branches for the repo ${u.owner}/${u.name}. Please make sure you have access to this repo on GitHub. If this continues, please contact support. Error: ${K.error}`),void n(1,h=!1);if(n(3,I=[...I,...K.branches]),n(11,S=K.hasNextPage),ce(),!S)break;R=K.nextPage,n(10,B++,B)}while(B%20!=0&&S);n(5,D=!1)}function ce(){if(u&&!g){if(f){const R=I.find(T=>T.name===f);if(R)return n(4,g=R),void fe()}if(u.default_branch){const R=u.default_branch;if(I.length===0)return n(4,g={name:R,commit:{sha:"",url:""},protected:!1}),void fe();const T=o.find(K=>K.name===R);if(T)return n(4,g=T),void fe()}D||n(4,g=I[0]),fe()}}function he(){u&&async function(){u&&(n(10,B=0),await ke(B+1))}().then(()=>{n(1,h=!1),t||fe()}).catch(R=>{console.error("Error fetching all branches:",R),W(`Failed to fetch branches: ${R instanceof Error?R.message:String(R)}`)})}let ue=!0;const F=async()=>{try{n(13,ue=await i.isGithubAuthenticated()),ue||W("Please authenticate with GitHub to use this feature.")}catch(R){console.error("Failed to check GitHub authentication status:",R),W("Please authenticate with GitHub to use this feature."),n(13,ue=!1)}};async function E(){n(1,h=!0),n(15,t=!1),n(0,w="");try{if(await F(),!ue)return void n(1,h=!1);await Be(),t||he(),t||fe()}catch(R){console.error("Error fetching git data:",R),W(de.failedToFetchBranches)}finally{n(1,h=!1)}}async function M(){n(1,h=!0);try{await E()}catch(R){console.error("Error fetching and syncing branches:",R),W("Failed to fetch repos and branches. Please try again. If this continues, please contact support.")}finally{n(1,h=!1)}}function W(R){console.error("Error:",R),n(15,t=!0),n(0,w=R)}async function q(R){n(4,g=R),n(31,X=!1),Te((g==null?void 0:g.name)??"");const T=m.creationMetrics;m.setCreationMetrics({changedRepo:(T==null?void 0:T.changedRepo)??!1,changedBranch:!0}),fe()}async function Le(R){n(5,D=!0),n(2,u=R),n(4,g=void 0),n(3,I=[]),H=!1,We(""),n(8,A=v),he();const T=m.creationMetrics;m.setCreationMetrics({changedRepo:!0,changedBranch:(T==null?void 0:T.changedBranch)??!1})}function Se(R,T){R||(T==="repo"?H=!1:(T==="branch"||(H=!1),n(31,X=!1)))}function fe(){if(!(u!=null&&u.html_url)||!g)return;const R={github_commit_ref:{repository_url:u.html_url,git_ref:g.name}};l("commitRefChange",{commitRef:R,selectedBranch:g})}const Te=R=>{n(6,ne=R)},We=R=>{n(12,oe=R)},hn=yt(async function(R=""){n(15,t=!1);try{H?n(8,(T=R||oe,A=v.filter(K=>K.name.includes(T.toLowerCase())||K.owner.includes(T.toLowerCase())))):n(8,A=v)}catch(K){console.error("Error fetching repos:",K),n(8,A=[]),W(de.failedToFetchFromRemote)}var T},300,{leading:!1,trailing:!0});function ht(R){R&&a||Se(R,"branch")}function wt(R){R&&!v.length||Se(R,"repo")}return r.$$set=R=>{"errorMessage"in R&&n(0,w=R.errorMessage),"isLoading"in R&&n(1,h=R.isLoading),"lastUsedBranchName"in R&&n(29,f=R.lastUsedBranchName),"lastUsedRepoUrl"in R&&n(30,$=R.lastUsedRepoUrl)},r.$$.update=()=>{1&r.$$.dirty[0]&&n(15,t=w!==""),76&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(16,o=(()=>{try{return X&&ne.trim()!==""?(R=ne,I.filter(T=>T.name.includes(R.toLowerCase()))):u!=null&&u.default_branch?[I.find(K=>K.name===(u==null?void 0:u.default_branch))||{name:u.default_branch,commit:{sha:"",url:""},protected:!1},...I.filter(K=>K.name!==(u==null?void 0:u.default_branch))]:I}catch(T){return console.error("Error computing displayedBranches:",T),[]}var R})()),52&r.$$.dirty[0]&&n(18,s=u&&D&&!g),4&r.$$.dirty[0]&&n(17,c=u?Y.has(u.name)?`${u.owner}/${u.name}`:u.name:"Choose repository..."),16&r.$$.dirty[0]|1&r.$$.dirty[1]&&Te(X?"":(g==null?void 0:g.name)??""),12&r.$$.dirty[0]|1&r.$$.dirty[1]&&n(14,a=!u||!X&&!I.length)},[w,h,u,I,g,D,ne,v,A,z,B,S,oe,ue,a,t,o,c,s,pe,Ne,ke,F,M,q,Le,ht,wt,function(){n(3,I=[]),he()},f,$,X,function(R){oe=R,n(12,oe)},R=>wt(R.detail),R=>pe(R.detail),R=>Le(R.detail),()=>{ke(B+1)},function(R){ne=R,n(6,ne)},R=>ht(R.detail),R=>Ne(R.detail),R=>q(R.detail),async()=>{await F(),ue&&await M()}]}class Ar extends ie{constructor(e){super(),ae(this,e,Cr,br,le,{errorMessage:0,isLoading:1,lastUsedBranchName:29,lastUsedRepoUrl:30},null,[-1,-1,-1])}}function Rr(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=se(o,t[s]);return{c(){e=Ge("svg"),n=new Oe(!0),this.h()},l(s){e=je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=He(e);n=Ve(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){qe(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',e)},p(s,[c]){xe(e,o=Ke(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function kr(r,e,n){return r.$$set=t=>{n(0,e=se(se({},e),_e(t)))},[e=_e(e)]}class Sr extends ie{constructor(e){super(),ae(this,e,kr,Rr,le,{})}}function Ir(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=se(o,t[s]);return{c(){e=Ge("svg"),n=new Oe(!0),this.h()},l(s){e=je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=He(e);n=Ve(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){qe(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M234.7 42.7 197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1 14.1 37.7c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2l-37.7-14.1L263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5zM461.4 48 496 82.6 386.2 192.3l-34.6-34.6zM80 429.4l237.7-237.7 34.6 34.6L114.6 464zM427.4 14.1 46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0l381.3-381.4c18.7-18.7 18.7-49.1 0-67.9l-34.6-34.5c-18.7-18.7-49.1-18.7-67.9 0M7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L64 96zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352z"/>',e)},p(s,[c]){xe(e,o=Ke(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function Fr(r,e,n){return r.$$set=t=>{n(0,e=se(se({},e),_e(t)))},[e=_e(e)]}class Nr extends ie{constructor(e){super(),ae(this,e,Fr,Ir,le,{})}}const Er=r=>({}),Zt=r=>({});function Jt(r){let e,n;const t=r[12].icon,o=Z(t,r,r[11],Zt);return{c(){e=k("div"),o&&o.c(),y(e,"class","c-setup-script-selector__icon svelte-udt6j8")},m(s,c){x(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||2048&c)&&J(o,t,s,s[11],n?ee(t,s[11],c,Er):Q(s[11]),Zt)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function Br(r){let e,n,t,o,s;return{c(){e=k("span"),n=G(r[0]),t=P(),o=k("span"),s=G(r[1]),y(e,"class","c-setup-script-selector__script-name svelte-udt6j8"),y(o,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,a){x(c,e,a),N(e,n),x(c,t,a),x(c,o,a),N(o,s)},p(c,a){1&a&&te(n,c[0]),2&a&&te(s,c[1])},i:U,o:U,d(c){c&&(b(e),b(t),b(o))}}}function Dr(r){let e,n,t,o,s,c,a,i,l;function m(h){r[15](h)}function u(h){r[16](h)}let g={size:1,variant:"surface"};r[6]!==void 0&&(g.value=r[6]),r[5]!==void 0&&(g.textInput=r[5]),t=new An({props:g}),ve.push(()=>be(t,"value",m)),ve.push(()=>be(t,"textInput",u)),t.$on("keydown",r[8]),t.$on("blur",r[9]);let w=r[7]&&function(h){let f;return{c(){f=k("span"),f.textContent=`${h[7]}`,y(f,"class","c-setup-script-selector__extension svelte-udt6j8")},m($,v){x($,f,v)},p:U,d($){$&&b(f)}}}(r);return{c(){e=k("div"),n=k("div"),_(t.$$.fragment),c=P(),w&&w.c(),y(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),y(n,"role","presentation"),y(e,"class","c-setup-script-selector__rename-input svelte-udt6j8"),y(e,"role","presentation")},m(h,f){x(h,e,f),N(e,n),L(t,n,null),N(n,c),w&&w.m(n,null),a=!0,i||(l=[Ae(e,"click",Qe(r[13])),Ae(e,"mousedown",Qe(r[14]))],i=!0)},p(h,f){const $={};!o&&64&f&&(o=!0,$.value=h[6],ye(()=>o=!1)),!s&&32&f&&(s=!0,$.textInput=h[5],ye(()=>s=!1)),t.$set($),h[7]&&w.p(h,f)},i(h){a||(p(t.$$.fragment,h),a=!0)},o(h){d(t.$$.fragment,h),a=!1},d(h){h&&b(e),C(t),w&&w.d(),i=!1,mt(l)}}}function zr(r){let e,n,t,o,s,c,a,i,l=r[10].icon&&Jt(r);const m=[Dr,Br],u=[];function g(f,$){return f[3]?0:1}o=g(r),s=u[o]=m[o](r);const w=r[12].default,h=Z(w,r,r[11],null);return{c(){e=k("div"),l&&l.c(),n=P(),t=k("div"),s.c(),c=P(),a=k("div"),h&&h.c(),y(t,"class","c-setup-script-selector__script-info svelte-udt6j8"),y(a,"class","c-setup-script-selector__script-actions svelte-udt6j8"),y(e,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),y(e,"role","presentation"),we(e,"c-setup-script-selector__script-item-content--renaming",r[3]),we(e,"c-setup-script-selector__script-item-content--is-path",r[2]),we(e,"c-setup-script-selector__script-item-content--selected",r[4])},m(f,$){x(f,e,$),l&&l.m(e,null),N(e,n),N(e,t),u[o].m(t,null),N(e,c),N(e,a),h&&h.m(a,null),i=!0},p(f,[$]){f[10].icon?l?(l.p(f,$),1024&$&&p(l,1)):(l=Jt(f),l.c(),p(l,1),l.m(e,n)):l&&(O(),d(l,1,1,()=>{l=null}),j());let v=o;o=g(f),o===v?u[o].p(f,$):(O(),d(u[v],1,1,()=>{u[v]=null}),j(),s=u[o],s?s.p(f,$):(s=u[o]=m[o](f),s.c()),p(s,1),s.m(t,null)),h&&h.p&&(!i||2048&$)&&J(h,w,f,f[11],i?ee(w,f[11],$,null):Q(f[11]),null),(!i||8&$)&&we(e,"c-setup-script-selector__script-item-content--renaming",f[3]),(!i||4&$)&&we(e,"c-setup-script-selector__script-item-content--is-path",f[2]),(!i||16&$)&&we(e,"c-setup-script-selector__script-item-content--selected",f[4])},i(f){i||(p(l),p(s),p(h,f),i=!0)},o(f){d(l),d(s),d(h,f),i=!1},d(f){f&&b(e),l&&l.d(),u[o].d(),h&&h.d(f)}}}function Mr(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=$t(t);let{name:c}=e,{path:a}=e,{isPath:i=!1}=e,{isRenaming:l=!1}=e,{isSelected:m=!1}=e;const u=Pe(),{baseName:g,extension:w}=function($){const v=$.lastIndexOf(".");return v===-1?{baseName:$,extension:""}:{baseName:$.substring(0,v),extension:$.substring(v)}}(c);let h,f=g;return r.$$set=$=>{"name"in $&&n(0,c=$.name),"path"in $&&n(1,a=$.path),"isPath"in $&&n(2,i=$.isPath),"isRenaming"in $&&n(3,l=$.isRenaming),"isSelected"in $&&n(4,m=$.isSelected),"$$scope"in $&&n(11,o=$.$$scope)},r.$$.update=()=>{40&r.$$.dirty&&l&&h&&setTimeout(()=>{h==null||h.focus(),h==null||h.select()},0)},[c,a,i,l,m,h,f,w,function($){if($.key!=="ArrowLeft"&&$.key!=="ArrowRight"&&$.key!=="ArrowUp"&&$.key!=="ArrowDown")if($.key==="Enter")if($.preventDefault(),f.trim()&&f!==g){const v=f.trim()+w;u("rename",{oldName:c,newName:v})}else u("cancelRename");else $.key==="Escape"&&($.preventDefault(),$.stopPropagation(),u("cancelRename"));else $.stopPropagation()},function(){u("cancelRename")},s,o,t,function($){Je.call(this,r,$)},function($){Je.call(this,r,$)},function($){f=$,n(6,f)},function($){h=$,n(5,h)}]}class Pr extends ie{constructor(e){super(),ae(this,e,Mr,zr,le,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function Qt(r){let e,n,t,o,s,c,a,i,l,m;function u(w){r[34](w)}let g={placeholder:"Search scripts...",isLoading:r[1],disabled:!1,items:r[7],selectedItem:r[2],itemLabelFn:fs,itemKeyFn:gs,isItemSelected:hs,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[ns,({item:w})=>({45:w}),({item:w})=>[0,w?16384:0]],searchIcon:[qr],icon:[Vr],title:[Hr]},$$scope:{ctx:r}};return r[3]!==void 0&&(g.searchValue=r[3]),t=new ut({props:g}),ve.push(()=>be(t,"searchValue",u)),t.$on("openChange",r[35]),t.$on("search",r[36]),t.$on("select",r[37]),a=new Fe({props:{content:r[10],nested:!1,$$slots:{default:[cs]},$$scope:{ctx:r}}}),l=new Fe({props:{content:"Open a new file for you to write a setup script that you can edit directly.",nested:!1,$$slots:{default:[us]},$$scope:{ctx:r}}}),{c(){e=k("div"),n=k("div"),_(t.$$.fragment),s=P(),c=k("div"),_(a.$$.fragment),i=P(),_(l.$$.fragment),y(c,"class","c-setup-script-selector__action-buttons svelte-3cd2r2"),y(n,"class","c-setup-script-selector__script-line svelte-3cd2r2"),y(e,"class","c-setup-script-selector__script-line-container svelte-3cd2r2")},m(w,h){x(w,e,h),N(e,n),L(t,n,null),N(n,s),N(n,c),L(a,c,null),N(c,i),L(l,c,null),m=!0},p(w,h){const f={};2&h[0]&&(f.isLoading=w[1]),128&h[0]&&(f.items=w[7]),4&h[0]&&(f.selectedItem=w[2]),884&h[0]|49152&h[1]&&(f.$$scope={dirty:h,ctx:w}),!o&&8&h[0]&&(o=!0,f.searchValue=w[3],ye(()=>o=!1)),t.$set(f);const $={};1024&h[0]&&($.content=w[10]),2048&h[0]|32768&h[1]&&($.$$scope={dirty:h,ctx:w}),a.$set($);const v={};32768&h[1]&&(v.$$scope={dirty:h,ctx:w}),l.$set(v)},i(w){m||(p(t.$$.fragment,w),p(a.$$.fragment,w),p(l.$$.fragment,w),m=!0)},o(w){d(t.$$.fragment,w),d(a.$$.fragment,w),d(l.$$.fragment,w),m=!1},d(w){w&&b(e),C(t),C(a),C(l)}}}function Ur(r){let e,n;return e=new dn({props:{$$slots:{text:[Gr]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};16&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Tr(r){let e,n;return e=new dn({props:{$$slots:{grayText:[jr],text:[Or]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};768&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Gr(r){let e,n;return{c(){e=k("span"),n=G(r[4]),y(e,"slot","text")},m(t,o){x(t,e,o),N(e,n)},p(t,o){16&o[0]&&te(n,t[4])},d(t){t&&b(e)}}}function Or(r){let e,n;return{c(){e=k("span"),n=G(r[9]),y(e,"slot","text")},m(t,o){x(t,e,o),N(e,n)},p(t,o){512&o[0]&&te(n,t[9])},d(t){t&&b(e)}}}function jr(r){let e,n;return{c(){e=k("span"),n=G(r[8]),y(e,"slot","grayText")},m(t,o){x(t,e,o),N(e,n)},p(t,o){256&o[0]&&te(n,t[8])},d(t){t&&b(e)}}}function Hr(r){let e,n,t,o;const s=[Tr,Ur],c=[];function a(i,l){return i[5]?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"slot","title")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,l){let m=n;n=a(i),n===m?c[n].p(i,l):(O(),d(c[m],1,1,()=>{c[m]=null}),j(),t=c[n],t?t.p(i,l):(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){d(t),o=!1},d(i){i&&b(e),c[n].d()}}}function Vr(r){let e,n;return e=new pn({props:{slot:"icon"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function qr(r){let e,n;return e=new ft({props:{slot:"searchIcon"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Kr(r){var t;let e,n;return e=new Pr({props:{name:r[45].name,path:r[45].path,isPath:!0,isRenaming:((t=r[6])==null?void 0:t.path)===r[45].path,isSelected:!(!r[2]||r[2].path!==r[45].path),$$slots:{default:[ts]},$$scope:{ctx:r}}}),e.$on("rename",function(...o){return r[33](r[45],...o)}),e.$on("cancelRename",r[21]),{c(){_(e.$$.fragment)},m(o,s){L(e,o,s),n=!0},p(o,s){var a;r=o;const c={};16384&s[1]&&(c.name=r[45].name),16384&s[1]&&(c.path=r[45].path),64&s[0]|16384&s[1]&&(c.isRenaming=((a=r[6])==null?void 0:a.path)===r[45].path),4&s[0]|16384&s[1]&&(c.isSelected=!(!r[2]||r[2].path!==r[45].path)),49152&s[1]&&(c.$$scope={dirty:s,ctx:r}),e.$set(c)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){d(e.$$.fragment,o),n=!1},d(o){C(e,o)}}}function Xr(r){let e,n,t,o;return n=new pn({}),{c(){e=k("div"),_(n.$$.fragment),t=G(`
                  Use basic environment`),y(e,"class","c-setup-script-selector__basic-option svelte-3cd2r2")},m(s,c){x(s,e,c),L(n,e,null),N(e,t),o=!0},p:U,i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),C(n)}}}function Wr(r){let e,n;return e=new Rn({}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Yr(r){let e,n;return e=new ct({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Wr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[30](r[45],...t)}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Zr(r){let e,n;return e=new Sr({}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Jr(r){let e,n;return e=new ct({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Zr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[31](r[45],...t)}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Qr(r){let e,n;return e=new Ln({}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function es(r){let e,n;return e=new ct({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Qr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[32](r[45],...t)}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ts(r){let e,n,t,o,s,c;return e=new Fe({props:{content:"Open script in editor",nested:!1,$$slots:{default:[Yr]},$$scope:{ctx:r}}}),t=new Fe({props:{content:"Rename script",nested:!1,$$slots:{default:[Jr]},$$scope:{ctx:r}}}),s=new Fe({props:{content:"Delete script",nested:!1,$$slots:{default:[es]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment),n=P(),_(t.$$.fragment),o=P(),_(s.$$.fragment)},m(a,i){L(e,a,i),x(a,n,i),L(t,a,i),x(a,o,i),L(s,a,i),c=!0},p(a,i){const l={};49152&i[1]&&(l.$$scope={dirty:i,ctx:a}),e.$set(l);const m={};49152&i[1]&&(m.$$scope={dirty:i,ctx:a}),t.$set(m);const u={};49152&i[1]&&(u.$$scope={dirty:i,ctx:a}),s.$set(u)},i(a){c||(p(e.$$.fragment,a),p(t.$$.fragment,a),p(s.$$.fragment,a),c=!0)},o(a){d(e.$$.fragment,a),d(t.$$.fragment,a),d(s.$$.fragment,a),c=!1},d(a){a&&(b(n),b(o)),C(e,a),C(t,a),C(s,a)}}}function ns(r){let e,n,t,o;const s=[Xr,Kr],c=[];function a(i,l){return i[45]===null?0:1}return e=a(r),n=c[e]=s[e](r),{c(){n.c(),t=me()},m(i,l){c[e].m(i,l),x(i,t,l),o=!0},p(i,l){let m=e;e=a(i),e===m?c[e].p(i,l):(O(),d(c[m],1,1,()=>{c[m]=null}),j(),n=c[e],n?n.p(i,l):(n=c[e]=s[e](i),n.c()),p(n,1),n.m(t.parentNode,t))},i(i){o||(p(n),o=!0)},o(i){d(n),o=!1},d(i){i&&b(t),c[e].d(i)}}}function os(r){let e,n;return{c(){e=G("Auto-generate"),n=k("span"),n.textContent="a script",y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(t,o){x(t,e,o),x(t,n,o)},p:U,d(t){t&&(b(e),b(n))}}}function rs(r){let e,n;return e=new Nr({props:{slot:"iconLeft"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ss(r){let e,n;return e=new an({props:{slot:"iconRight"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function cs(r){let e,n;return e=new Ue({props:{variant:"soft",color:"neutral",size:1,disabled:r[11],$$slots:{iconRight:[ss],iconLeft:[rs],default:[os]},$$scope:{ctx:r}}}),e.$on("click",r[14]),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};2048&o[0]&&(s.disabled=t[11]),32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function is(r){let e,n,t;return{c(){e=G("Write "),n=k("span"),n.textContent="a script",t=G("by hand"),y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(o,s){x(o,e,s),x(o,n,s),x(o,t,s)},p:U,d(o){o&&(b(e),b(n),b(t))}}}function as(r){let e,n;return e=new _n({props:{slot:"iconLeft"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ls(r){let e,n;return e=new an({props:{slot:"iconRight"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function us(r){let e,n;return e=new Ue({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[ls],iconLeft:[as],default:[is]},$$scope:{ctx:r}}}),e.$on("click",r[15]),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function en(r){let e,n,t;return n=new rt({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[ps]},$$scope:{ctx:r}}}),{c(){e=k("div"),_(n.$$.fragment),y(e,"class","c-setup-script-selector__error svelte-3cd2r2")},m(o,s){x(o,e,s),L(n,e,null),t=!0},p(o,s){const c={};3&s[0]|32768&s[1]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),C(n)}}}function $s(r){let e;return{c(){e=G("Refresh")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function ms(r){let e,n,t;return n=new ln({}),{c(){e=k("span"),_(n.$$.fragment),y(e,"slot","iconLeft")},m(o,s){x(o,e,s),L(n,e,null),t=!0},p:U,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),C(n)}}}function ps(r){let e,n,t,o,s,c;return s=new Ue({props:{variant:"ghost",color:"warning",size:1,loading:r[1],$$slots:{iconLeft:[ms],default:[$s]},$$scope:{ctx:r}}}),s.$on("click",r[17]),{c(){e=k("div"),n=k("div"),t=G(r[0]),o=P(),_(s.$$.fragment),y(n,"class","c-setup-script-selector__error-message svelte-3cd2r2"),y(e,"class","c-setup-script-selector__error-content svelte-3cd2r2")},m(a,i){x(a,e,i),N(e,n),N(n,t),N(e,o),L(s,e,null),c=!0},p(a,i){(!c||1&i[0])&&te(t,a[0]);const l={};2&i[0]&&(l.loading=a[1]),32768&i[1]&&(l.$$scope={dirty:i,ctx:a}),s.$set(l)},i(a){c||(p(s.$$.fragment,a),c=!0)},o(a){d(s.$$.fragment,a),c=!1},d(a){a&&b(e),C(s)}}}function ds(r){let e,n,t,o,s=(!r[12]||r[0]===r[16].noScriptsFound)&&Qt(r),c=r[12]&&r[0]!==r[16].noScriptsFound&&en(r);return{c(){e=k("div"),n=k("div"),s&&s.c(),t=P(),c&&c.c(),y(n,"class","c-setup-script-selector__content svelte-3cd2r2"),y(e,"class","c-setup-script-selector svelte-3cd2r2")},m(a,i){x(a,e,i),N(e,n),s&&s.m(n,null),N(n,t),c&&c.m(n,null),o=!0},p(a,i){a[12]&&a[0]!==a[16].noScriptsFound?s&&(O(),d(s,1,1,()=>{s=null}),j()):s?(s.p(a,i),4097&i[0]&&p(s,1)):(s=Qt(a),s.c(),p(s,1),s.m(n,t)),a[12]&&a[0]!==a[16].noScriptsFound?c?(c.p(a,i),4097&i[0]&&p(c,1)):(c=en(a),c.c(),p(c,1),c.m(n,null)):c&&(O(),d(c,1,1,()=>{c=null}),j())},i(a){o||(p(s),p(c),o=!0)},o(a){d(s),d(c),o=!1},d(a){a&&b(e),s&&s.d(),c&&c.d()}}}const fs=r=>(r==null?void 0:r.name)||"",gs=r=>`${r==null?void 0:r.path}-${r==null?void 0:r.location}-${r==null?void 0:r.name}`,hs=(r,e)=>r===null&&e===null||!(!r||!e)&&r.path===e.path;function ws(r,e,n){var ue;let t,o,s,c,a,i,l,m,{errorMessage:u=""}=e,{isLoading:g=!1}=e,{lastUsedScriptPath:w=null}=e,{disableNewAgentCreation:h=!1}=e;const f=Re(st.key),$=Pe(),v=Re("chatModel").extensionClient,A=F=>{v.openFile({repoRoot:"",pathName:F.path,allowOutOfWorkspace:!0,openLocalUri:F.location==="home"})};let I=[],D=((ue=f.newAgentDraft)==null?void 0:ue.setupScript)??null,z="",B=null,S=I,Y=!0;const H={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function X(){n(0,u="");try{const F=D==null?void 0:D.path;if(n(28,I=await f.listSetupScripts()),Y)if(w&&I.length>0){const E=I.find(M=>M.path===w);E&&(n(2,D=E),he())}else w===null&&(n(2,D=null),he());else if(F){const E=I.find(M=>M.path===F);E&&n(2,D=E)}Y=!1,I.length===0?n(0,u=H.noScriptsFound):n(0,u="")}catch(F){console.error("Error fetching setup scripts:",F),n(0,u=H.failedToFetchScripts)}}async function oe(F,E){E&&E.stopPropagation();try{const M=await f.deleteSetupScript(F.name,F.location);M.success?((D==null?void 0:D.path)===F.path&&ce(null),await X()):(console.error("Failed to delete script:",M.error),de(`Failed to delete script: ${M.error||"Unknown error"}`))}catch(M){console.error("Error deleting script:",M),de(`Error deleting script: ${M instanceof Error?M.message:String(M)}`)}}async function ne(F,E){E&&E.stopPropagation(),n(6,B=F)}async function pe(F,E){const{oldName:M,newName:W}=E.detail;try{const q=await f.renameSetupScript(M,W,F.location);if(q.success){await X();const Le=I.find(Se=>Se.path===q.path);Le&&ce(Le)}else console.error("Failed to rename script:",q.error),de(`Failed to rename script: ${q.error||"Unknown error"}`)}catch(q){console.error("Error renaming script:",q),de(`Error renaming script: ${q instanceof Error?q.message:String(q)}`)}finally{Ne()}}function Ne(){n(6,B=null)}function de(F){n(0,u=F)}function V(F){n(3,z=F)}function Be(F){ce(F)}function ke(F){F&&(X(),n(3,z=""))}async function ce(F){n(2,D=F),he(),f.saveLastRemoteAgentSetup(null,null,(D==null?void 0:D.path)||null)}function he(){$("setupScriptChange",{script:D})}return ot(async()=>{var F;await X(),w===null?ce(null):(F=f.newAgentDraft)!=null&&F.setupScript&&!D&&ce(f.newAgentDraft.setupScript)}),r.$$set=F=>{"errorMessage"in F&&n(0,u=F.errorMessage),"isLoading"in F&&n(1,g=F.isLoading),"lastUsedScriptPath"in F&&n(26,w=F.lastUsedScriptPath),"disableNewAgentCreation"in F&&n(27,h=F.disableNewAgentCreation)},r.$$.update=()=>{var F,E;if(1&r.$$.dirty[0]&&n(12,t=u!==""),134217728&r.$$.dirty[0]&&n(11,o=h||((F=f.newAgentDraft)==null?void 0:F.isDisabled)||!f.newAgentDraft),134217728&r.$$.dirty[0]&&n(10,s=f.newAgentDraft?(E=f.newAgentDraft)!=null&&E.isDisabled?"Please resolve the issues with your workspace selection":h?"Agent limit reached or other restrictions apply":"An AI agent will automatically generate a setup script for your project.":"Please select a repository and branch first"),268435464&r.$$.dirty[0])if(z.trim()!==""){const M="Use basic environment".toLowerCase().includes(z.toLowerCase()),W=I.filter(q=>q.name.toLowerCase().includes(z.toLowerCase())||q.path.toLowerCase().includes(z.toLowerCase()));n(7,S=M?[null,...W]:W)}else n(7,S=[null,...I]);6&r.$$.dirty[0]&&n(29,c=()=>g?"...":D?D.isGenerateOption?D.name:D.location==="home"?"~/.augment/env/"+D.name:D.path:"Use basic environment"),536870912&r.$$.dirty[0]&&n(4,a=c()),4&r.$$.dirty[0]&&n(5,i=!!(D!=null&&D.path)),48&r.$$.dirty[0]&&n(9,l=i?a.split("/").pop():a),48&r.$$.dirty[0]&&n(8,m=i?a.slice(0,a.lastIndexOf("/")):"")},[u,g,D,z,a,i,B,S,m,l,s,o,t,A,async()=>{try{const F=f.newAgentDraft;F&&f.setNewAgentDraft({...F,isSetupScriptAgent:!0});const E=await f.createRemoteAgentFromDraft("SETUP_MODE");return E&&f.setCurrentAgent(E),E}catch(F){console.error("Failed to select setup script generation:",F)}},async()=>{try{const F="setup.sh",E=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,M=await f.saveSetupScript(F,E,"home");if(M.success&&M.path){await X();const W=I.find(q=>q.path===M.path);W&&(ce(W),A(W))}else console.error("Failed to create manual setup script:",M.error),n(0,u=`Failed to create manual setup script: ${M.error||"Unknown error"}`)}catch(F){console.error("Error creating manual setup script:",F),n(0,u=`Error creating manual setup script: ${F instanceof Error?F.message:String(F)}`)}},H,X,oe,ne,pe,Ne,V,Be,ke,ce,w,h,I,c,(F,E)=>{E.stopPropagation(),A(F),ce(F)},(F,E)=>{E.stopPropagation(),ne(F)},(F,E)=>{E.stopPropagation(),oe(F)},(F,E)=>pe(F,E),function(F){z=F,n(3,z)},F=>ke(F.detail),F=>V(F.detail),F=>Be(F.detail)]}class vs extends ie{constructor(e){super(),ae(this,e,ws,ds,le,{errorMessage:0,isLoading:1,lastUsedScriptPath:26,disableNewAgentCreation:27},null,[-1,-1])}}function bs(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=se(o,t[s]);return{c(){e=Ge("svg"),n=new Oe(!0),this.h()},l(s){e=je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=He(e);n=Ve(c,!0),c.forEach(b),this.h()},h(){n.a=null,xe(e,o)},m(s,c){qe(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',e)},p(s,[c]){xe(e,o=Ke(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&s[0]]))},i:U,o:U,d(s){s&&b(e)}}}function ys(r,e,n){return r.$$set=t=>{n(0,e=se(se({},e),_e(t)))},[e=_e(e)]}class xs extends ie{constructor(e){super(),ae(this,e,ys,bs,le,{})}}function tn(r){let e,n;return e=new rt({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[ks],default:[Rs]},$$scope:{ctx:r}}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};16414&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function _s(r){let e;return{c(){e=G(r[4])},m(n,t){x(n,e,t)},p(n,t){16&t&&te(e,n[4])},d(n){n&&b(e)}}}function Ls(r){let e,n;return e=new Sn({}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Cs(r){let e,n;return e=new xs({}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function As(r){let e,n,t,o;const s=[Cs,Ls],c=[];function a(i,l){return i[1]?0:1}return n=a(r),t=c[n]=s[n](r),{c(){e=k("div"),t.c(),y(e,"slot","iconLeft")},m(i,l){x(i,e,l),c[n].m(e,null),o=!0},p(i,l){let m=n;n=a(i),n!==m&&(O(),d(c[m],1,1,()=>{c[m]=null}),j(),t=c[n],t||(t=c[n]=s[n](i),t.c()),p(t,1),t.m(e,null))},i(i){o||(p(t),o=!0)},o(i){d(t),o=!1},d(i){i&&b(e),c[n].d()}}}function Rs(r){let e,n,t,o,s,c,a=(r[2]?at:lt).replace("%MAX_AGENTS%",(r[2]?r[3].maxRemoteAgents:r[3].maxActiveRemoteAgents).toString())+"";return s=new Ue({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[As],default:[_s]},$$scope:{ctx:r}}}),s.$on("click",r[11]),{c(){e=k("div"),n=k("p"),t=G(a),o=P(),_(s.$$.fragment),y(n,"class","svelte-f3wuoa"),y(e,"class","agent-limit-message svelte-f3wuoa")},m(i,l){x(i,e,l),N(e,n),N(n,t),N(e,o),L(s,e,null),c=!0},p(i,l){(!c||12&l)&&a!==(a=(i[2]?at:lt).replace("%MAX_AGENTS%",(i[2]?i[3].maxRemoteAgents:i[3].maxActiveRemoteAgents).toString())+"")&&te(t,a);const m={};16402&l&&(m.$$scope={dirty:l,ctx:i}),s.$set(m)},i(i){c||(p(s.$$.fragment,i),c=!0)},o(i){d(s.$$.fragment,i),c=!1},d(i){i&&b(e),C(s)}}}function ks(r){let e,n;return e=new kn({props:{slot:"icon"}}),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p:U,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Ss(r){let e,n,t=!!r[0]&&tn(r);return{c(){t&&t.c(),e=me()},m(o,s){t&&t.m(o,s),x(o,e,s),n=!0},p(o,[s]){o[0]?t?(t.p(o,s),1&s&&p(t,1)):(t=tn(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(O(),d(t,1,1,()=>{t=null}),j())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function nn(r){if(!r)return;const e=r.is_setup_script_agent?"Setup script generation":r.session_summary||"";return{id:r.remote_agent_id,title:e.length>30?e.substring(0,27)+"...":e}}function on(r,e){return r.replace("%MAX_AGENTS%",e.toString())}function Is(r,e,n){let t,o,s,{agentLimitErrorMessage:c}=e;const a=Re(st.key);it(r,a,$=>n(3,s=$));let i,l,m,u=!1,g=[];function w(){return s.agentOverviews.sort(($,v)=>new Date($.started_at).getTime()-new Date(v.started_at).getTime())}async function h(){if(!u&&(i!=null&&i.id))try{u=!0,await a.deleteAgent(i.id)}catch($){console.error("Failed to delete oldest agent:",$)}finally{u=!1}}async function f(){if(!u&&(l!=null&&l.id))try{u=!0,await a.pauseRemoteAgentWorkspace(l.id)}catch($){console.error("Failed to pause oldest active agent:",$)}finally{u=!1}}return r.$$set=$=>{"agentLimitErrorMessage"in $&&n(0,c=$.agentLimitErrorMessage)},r.$$.update=()=>{if(8&r.$$.dirty&&n(2,t=!!s.maxRemoteAgents&&s.agentOverviews.length>=s.maxRemoteAgents),8&r.$$.dirty&&n(1,o=!!s.maxActiveRemoteAgents&&s.agentOverviews.filter($=>$.workspace_status===xt.workspaceRunning).length>=s.maxActiveRemoteAgents),1806&r.$$.dirty)if(t)n(10,g=w()),n(8,i=nn(g[0])),n(0,c=on(at,s.maxRemoteAgents)),n(4,m="Delete Oldest Agent"+(i?`: ${i.title}`:""));else if(o){n(10,g=w());const $=g.filter(v=>v.workspace_status===xt.workspaceRunning);n(9,l=nn($[0])),n(0,c=on(lt,s.maxActiveRemoteAgents)),n(4,m="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,i=void 0),n(0,c=void 0)},[c,o,t,s,m,a,h,f,i,l,g,()=>{o?f():h()}]}class Fs extends ie{constructor(e){super(),ae(this,e,Is,Ss,le,{agentLimitErrorMessage:0})}}function rn(r){let e,n,t,o;return n=new rt({props:{color:"error",variant:"soft",size:2,$$slots:{default:[Ns]},$$scope:{ctx:r}}}),{c(){e=k("div"),_(n.$$.fragment),y(e,"class","error-message svelte-1klrgvd")},m(s,c){x(s,e,c),L(n,e,null),o=!0},p(s,c){const a={};33554496&c&&(a.$$scope={dirty:c,ctx:s}),n.$set(a)},i(s){o||(p(n.$$.fragment,s),s&&cn(()=>{o&&(t||(t=et(e,tt,{y:10},!0)),t.run(1))}),o=!0)},o(s){d(n.$$.fragment,s),s&&(t||(t=et(e,tt,{y:10},!1)),t.run(0)),o=!1},d(s){s&&b(e),C(n),s&&t&&t.end()}}}function Ns(r){let e,n=r[6].remoteAgentCreationError+"";return{c(){e=G(n)},m(t,o){x(t,e,o)},p(t,o){64&o&&n!==(n=t[6].remoteAgentCreationError+"")&&te(e,n)},d(t){t&&b(e)}}}function Es(r){let e;return{c(){e=G("Create agent")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function Bs(r){let e,n;return e=new Ue({props:{variant:"solid",color:"accent",size:2,loading:r[10],disabled:r[11],$$slots:{default:[Es]},$$scope:{ctx:r}}}),e.$on("click",r[16]),{c(){_(e.$$.fragment)},m(t,o){L(e,t,o),n=!0},p(t,o){const s={};1024&o&&(s.loading=t[10]),2048&o&&(s.disabled=t[11]),33554432&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Ds(r){var he,ue,F;let e,n,t,o,s,c,a,i,l,m,u,g,w,h,f,$,v,A,I,D,z,B,S,Y,H,X,oe,ne,pe;function Ne(E){r[18](E)}let de={};r[2]!==void 0&&(de.agentLimitErrorMessage=r[2]),a=new Fs({props:de}),ve.push(()=>be(a,"agentLimitErrorMessage",Ne));let V=r[6].remoteAgentCreationError&&rn(r);function Be(E){r[19](E)}function ke(E){r[20](E)}let ce={lastUsedRepoUrl:r[7],lastUsedBranchName:r[8]};return r[0]!==void 0&&(ce.errorMessage=r[0]),r[1]!==void 0&&(ce.isLoading=r[1]),f=new Ar({props:ce}),ve.push(()=>be(f,"errorMessage",Be)),ve.push(()=>be(f,"isLoading",ke)),f.$on("commitRefChange",r[14]),B=new vs({props:{lastUsedScriptPath:r[9],disableNewAgentCreation:!!r[2]||!((he=r[3])!=null&&he.name)||!((F=(ue=r[4])==null?void 0:ue.github_commit_ref)!=null&&F.repository_url)}}),B.$on("setupScriptChange",r[15]),H=new bn({props:{editable:!0,hasSendButton:!1}}),ne=new Fe({props:{class:"full-width-button",content:r[5],triggerOn:[pt.Hover],$$slots:{default:[Bs]},$$scope:{ctx:r}}}),{c(){e=k("div"),n=k("div"),t=k("div"),t.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-1klrgvd">in parallel</strong>, in an
        <strong class="svelte-1klrgvd">isolated environment</strong>
        that will keep running, <strong class="svelte-1klrgvd">even when you shut off your laptop</strong>.</p>`,o=P(),s=k("div"),c=k("div"),_(a.$$.fragment),m=P(),V&&V.c(),u=P(),g=k("div"),g.textContent="Start from any GitHub repo and branch:",w=P(),h=k("div"),_(f.$$.fragment),A=P(),I=k("div"),I.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,D=P(),z=k("div"),_(B.$$.fragment),S=P(),Y=k("div"),_(H.$$.fragment),X=P(),oe=k("div"),_(ne.$$.fragment),y(t,"class","main-description svelte-1klrgvd"),y(c,"class","error-message svelte-1klrgvd"),y(g,"class","description svelte-1klrgvd"),y(h,"class","commit-ref-selector svelte-1klrgvd"),y(I,"class","description svelte-1klrgvd"),y(z,"class","setup-script svelte-1klrgvd"),y(Y,"class","chat svelte-1klrgvd"),y(oe,"class","create-button svelte-1klrgvd"),y(s,"class","form-fields"),y(n,"class","content svelte-1klrgvd"),y(e,"class","remote-agent-setup svelte-1klrgvd")},m(E,M){x(E,e,M),N(e,n),N(n,t),N(n,o),N(n,s),N(s,c),L(a,c,null),N(s,m),V&&V.m(s,null),N(s,u),N(s,g),N(s,w),N(s,h),L(f,h,null),N(s,A),N(s,I),N(s,D),N(s,z),L(B,z,null),N(s,S),N(s,Y),L(H,Y,null),N(s,X),N(s,oe),L(ne,oe,null),pe=!0},p(E,[M]){var fe,Te,We;const W={};!i&&4&M&&(i=!0,W.agentLimitErrorMessage=E[2],ye(()=>i=!1)),a.$set(W),E[6].remoteAgentCreationError?V?(V.p(E,M),64&M&&p(V,1)):(V=rn(E),V.c(),p(V,1),V.m(s,u)):V&&(O(),d(V,1,1,()=>{V=null}),j());const q={};128&M&&(q.lastUsedRepoUrl=E[7]),256&M&&(q.lastUsedBranchName=E[8]),!$&&1&M&&($=!0,q.errorMessage=E[0],ye(()=>$=!1)),!v&&2&M&&(v=!0,q.isLoading=E[1],ye(()=>v=!1)),f.$set(q);const Le={};512&M&&(Le.lastUsedScriptPath=E[9]),28&M&&(Le.disableNewAgentCreation=!!E[2]||!((fe=E[3])!=null&&fe.name)||!((We=(Te=E[4])==null?void 0:Te.github_commit_ref)!=null&&We.repository_url)),B.$set(Le);const Se={};32&M&&(Se.content=E[5]),33557504&M&&(Se.$$scope={dirty:M,ctx:E}),ne.$set(Se)},i(E){pe||(p(a.$$.fragment,E),E&&cn(()=>{pe&&(l||(l=et(c,tt,{y:10},!0)),l.run(1))}),p(V),p(f.$$.fragment,E),p(B.$$.fragment,E),p(H.$$.fragment,E),p(ne.$$.fragment,E),pe=!0)},o(E){d(a.$$.fragment,E),E&&(l||(l=et(c,tt,{y:10},!1)),l.run(0)),d(V),d(f.$$.fragment,E),d(B.$$.fragment,E),d(H.$$.fragment,E),d(ne.$$.fragment,E),pe=!1},d(E){E&&b(e),C(a),E&&l&&l.end(),V&&V.d(),C(f),C(B),C(H),C(ne)}}}function zs(r,e,n){let t,o,s,c,a,i,l,m,u;const g=Re(st.key);it(r,g,B=>n(6,u=B));const w=Re("chatModel");it(r,w,B=>n(22,m=B));const h=Re(dt.key);let f,$="",v=!1,A=null,I=null,D=null;ot(async()=>{try{const B=await g.getLastRemoteAgentSetup();n(7,A=B.lastRemoteAgentGitRepoUrl),n(8,I=B.lastRemoteAgentGitBranch),n(9,D=B.lastRemoteAgentSetupScript),g.setHasEverUsedRemoteAgent(!0),await g.reportRemoteAgentEvent({eventName:xn.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(B){console.error("Failed to load last remote agent setup:",B)}}),sn(()=>{g.setNewAgentDraft(null),g.setCreationMetrics(void 0)});const z=yn(g,m.currentConversationModel,h);return r.$$.update=()=>{var B,S,Y;64&r.$$.dirty&&n(4,t=((B=u.newAgentDraft)==null?void 0:B.commitRef)??null),64&r.$$.dirty&&n(3,o=((S=u.newAgentDraft)==null?void 0:S.selectedBranch)??null),64&r.$$.dirty&&(s=((Y=u.newAgentDraft)==null?void 0:Y.setupScript)??null),31&r.$$.dirty&&n(5,i=(()=>{var oe;const H=(oe=t==null?void 0:t.github_commit_ref)==null?void 0:oe.repository_url,X=o==null?void 0:o.name;return $||f||(v?"Loading repos and branches...":"")||!H&&"Please select a repository"||!X&&"Please select a branch"||(!(!v&&H&&X)&&H&&X?"Loading branch data...":"")||""})()),32&r.$$.dirty&&n(17,l=!!i),131072&r.$$.dirty&&n(11,c=l),64&r.$$.dirty&&n(10,a=u.isCreatingAgent),131136&r.$$.dirty&&g.newAgentDraft&&!u.isCreatingAgent&&g.newAgentDraft.isDisabled!==l&&g.setNewAgentDraft({...g.newAgentDraft,isDisabled:l})},[$,v,f,o,t,i,u,A,I,D,a,c,g,w,async function(B){g.setRemoteAgentCreationError(null);const S=g.newAgentDraft;S?g.setNewAgentDraft({...S,commitRef:B.detail.commitRef,selectedBranch:B.detail.selectedBranch}):g.setNewAgentDraft({commitRef:B.detail.commitRef,selectedBranch:B.detail.selectedBranch,setupScript:null,isDisabled:l,enableNotification:!0})},function(B){g.setRemoteAgentCreationError(null);const S=g.newAgentDraft;S?g.setNewAgentDraft({...S,setupScript:B.detail.script}):g.setNewAgentDraft({commitRef:null,selectedBranch:null,setupScript:B.detail.script,isDisabled:l,enableNotification:!0})},async function(){try{z(),g.saveLastRemoteAgentSetup((t==null?void 0:t.github_commit_ref.repository_url)||null,(o==null?void 0:o.name)||null,(s==null?void 0:s.path)||null)}catch(B){console.error("Failed to create agent:",B)}},l,function(B){f=B,n(2,f)},function(B){$=B,n(0,$)},function(B){v=B,n(1,v)}]}class Fc extends ie{constructor(e){super(),ae(this,e,zs,Ds,le,{})}}export{Fc as default};

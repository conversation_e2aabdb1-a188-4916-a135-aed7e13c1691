#!/bin/bash

# Chrome Launch Script with Minimal Hardware Spoofing
# Only spoofs unique identifiers while keeping real hardware characteristics
# Maximum OAuth compatibility with targeted identity protection

echo "🎯 Launching Chrome with minimal hardware spoofing (OAuth-optimized)..."

# Check if Chrome is installed
CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
if [ ! -f "$CHROME_PATH" ]; then
    echo "❌ Google Chrome not found at: $CHROME_PATH"
    echo "Please install Google Chrome first."
    exit 1
fi

# Create minimal spoofed profile directory
PROFILE_DIR="$HOME/chrome_minimal_spoofed_profile"
mkdir -p "$PROFILE_DIR"

# Launch Chrome with minimal flags (only what's necessary)
"$CHROME_PATH" \
  --user-data-dir="$PROFILE_DIR" \
  --disable-background-networking \
  --disable-sync \
  --disable-translate \
  --no-first-run \
  --no-default-browser-check \
  --disable-logging \
  --user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" \
  > /dev/null 2>&1 &

echo "✅ Chrome launched with minimal spoofing at: $PROFILE_DIR"
echo "🎯 Minimal Spoofing Strategy (OAuth-Optimized)"
echo ""
echo "📋 What's Changed:"
echo "   • User Agent: Generic Chrome 120 ✅"
echo "   • Profile: Isolated from real browsing ✅"
echo "   • Sync: Disabled ✅"
echo "   • Background networking: Disabled ✅"
echo ""
echo "📋 What's NOT Changed (for OAuth compatibility):"
echo "   • WebGL: Real hardware data (for compatibility) ⚠️"
echo "   • Canvas: Real rendering (for compatibility) ⚠️"
echo "   • Audio: Real audio context (for compatibility) ⚠️"
echo "   • GPU: Real GPU data (for compatibility) ⚠️"
echo ""
echo "🔐 OAuth Compatibility:"
echo "   • Cloudflare challenges: Will work ✅"
echo "   • Auth0 flows: Will work ✅"
echo "   • CAPTCHA verification: Will work ✅"
echo "   • All hardware APIs functional ✅"
echo ""
echo "💡 Strategy: Use real hardware data for OAuth, spoof only in VS Code extension"
echo "🎯 This approach prioritizes OAuth success over fingerprint protection"

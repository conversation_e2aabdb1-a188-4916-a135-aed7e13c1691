#!/bin/bash

# Chrome Launch Script with Hardware Data Spoofing (Not Blocking)
# Returns fake but consistent hardware data instead of blocking APIs
# Perfect for OAuth flows - maintains functionality while spoofing fingerprints

echo "🎭 Launching Chrome with hardware data spoofing (fake data mode)..."

# Check if Chrome is installed
CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
if [ ! -f "$CHROME_PATH" ]; then
    echo "❌ Google Chrome not found at: $CHROME_PATH"
    echo "Please install Google Chrome first."
    exit 1
fi

# Create data-spoofed profile directory
PROFILE_DIR="$HOME/chrome_data_spoofed_profile"
mkdir -p "$PROFILE_DIR"

# Create spoofing extension directory
EXTENSION_DIR="$PROFILE_DIR/spoofing_extension"
mkdir -p "$EXTENSION_DIR"

# Create manifest.json for spoofing extension
cat > "$EXTENSION_DIR/manifest.json" << 'EOF'
{
  "manifest_version": 3,
  "name": "Hardware Data Spoofer",
  "version": "1.0",
  "description": "Spoofs hardware fingerprinting data",
  "permissions": ["storage"],
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content.js"],
      "run_at": "document_start",
      "all_frames": true
    }
  ]
}
EOF

# Create content.js for data spoofing
cat > "$EXTENSION_DIR/content.js" << 'EOF'
// Hardware Data Spoofing Script
// Returns fake but consistent data instead of blocking APIs

(function() {
    'use strict';
    
    // Consistent fake MacBook M2 Pro data (different from real machine)
    const SPOOFED_DATA = {
        // GPU spoofing - fake but realistic M2 Pro GPU
        gpu: {
            vendor: "Apple",
            renderer: "Apple M2 Pro",
            version: "WebGL 2.0 (OpenGL ES 3.0 Chromium)",
            shadingLanguageVersion: "WebGL GLSL ES 3.00 (OpenGL ES GLSL ES 3.0 Chromium)",
            maxTextureSize: 16384,
            maxViewportDims: [16384, 16384],
            aliasedLineWidthRange: [1, 1],
            aliasedPointSizeRange: [1, 1024],
            maxAnisotropy: 16,
            maxColorAttachments: 8,
            maxCombinedTextureImageUnits: 96,
            maxCubeMapTextureSize: 16384,
            maxFragmentUniformVectors: 1024,
            maxRenderbufferSize: 16384,
            maxTextureImageUnits: 16,
            maxVaryingVectors: 31,
            maxVertexAttribs: 16,
            maxVertexTextureImageUnits: 16,
            maxVertexUniformVectors: 1024
        },
        
        // Canvas spoofing - consistent fake fingerprint
        canvasFingerprint: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
        
        // Audio context spoofing
        audioFingerprint: {
            sampleRate: 48000,
            maxChannelCount: 2,
            numberOfInputs: 1,
            numberOfOutputs: 1,
            channelCount: 2,
            channelCountMode: "max",
            channelInterpretation: "speakers"
        },
        
        // Screen/display spoofing (common MacBook Pro 14" specs)
        screen: {
            width: 1512,
            height: 982,
            colorDepth: 24,
            pixelDepth: 24,
            availWidth: 1512,
            availHeight: 942,
            devicePixelRatio: 2
        },
        
        // Hardware concurrency (M2 Pro has 10 cores, spoof as 8)
        hardwareConcurrency: 8,
        
        // Memory spoofing (8GB machine)
        deviceMemory: 8,
        
        // Platform spoofing
        platform: "MacIntel",
        
        // Timezone spoofing (consistent)
        timezone: "America/New_York",
        timezoneOffset: -300,
        
        // Language spoofing
        languages: ["en-US", "en"],
        language: "en-US"
    };
    
    // WebGL Context Spoofing
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
        const context = originalGetContext.call(this, contextType, contextAttributes);
        
        if (contextType === 'webgl' || contextType === 'webgl2') {
            if (context) {
                // Spoof WebGL parameters
                const originalGetParameter = context.getParameter;
                context.getParameter = function(parameter) {
                    switch (parameter) {
                        case context.VENDOR:
                            return SPOOFED_DATA.gpu.vendor;
                        case context.RENDERER:
                            return SPOOFED_DATA.gpu.renderer;
                        case context.VERSION:
                            return SPOOFED_DATA.gpu.version;
                        case context.SHADING_LANGUAGE_VERSION:
                            return SPOOFED_DATA.gpu.shadingLanguageVersion;
                        case context.MAX_TEXTURE_SIZE:
                            return SPOOFED_DATA.gpu.maxTextureSize;
                        case context.MAX_VIEWPORT_DIMS:
                            return new Int32Array(SPOOFED_DATA.gpu.maxViewportDims);
                        case context.ALIASED_LINE_WIDTH_RANGE:
                            return new Float32Array(SPOOFED_DATA.gpu.aliasedLineWidthRange);
                        case context.ALIASED_POINT_SIZE_RANGE:
                            return new Float32Array(SPOOFED_DATA.gpu.aliasedPointSizeRange);
                        case context.MAX_TEXTURE_MAX_ANISOTROPY_EXT:
                            return SPOOFED_DATA.gpu.maxAnisotropy;
                        default:
                            return originalGetParameter.call(this, parameter);
                    }
                };
                
                // Spoof WebGL extensions
                const originalGetExtension = context.getExtension;
                context.getExtension = function(name) {
                    const ext = originalGetExtension.call(this, name);
                    if (name === 'WEBGL_debug_renderer_info' && ext) {
                        const originalGetParameter = context.getParameter;
                        context.getParameter = function(parameter) {
                            if (parameter === ext.UNMASKED_VENDOR_WEBGL) {
                                return SPOOFED_DATA.gpu.vendor;
                            }
                            if (parameter === ext.UNMASKED_RENDERER_WEBGL) {
                                return SPOOFED_DATA.gpu.renderer;
                            }
                            return originalGetParameter.call(this, parameter);
                        };
                    }
                    return ext;
                };
            }
        }
        
        // Canvas fingerprinting spoofing
        if (contextType === '2d' && context) {
            const originalToDataURL = this.toDataURL;
            this.toDataURL = function() {
                return SPOOFED_DATA.canvasFingerprint;
            };
        }
        
        return context;
    };
    
    // Audio Context Spoofing
    if (window.AudioContext || window.webkitAudioContext) {
        const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
        function SpoofedAudioContext() {
            const ctx = new OriginalAudioContext();
            
            // Override audio context properties
            Object.defineProperty(ctx, 'sampleRate', {
                value: SPOOFED_DATA.audioFingerprint.sampleRate,
                writable: false
            });
            
            return ctx;
        }
        
        SpoofedAudioContext.prototype = OriginalAudioContext.prototype;
        window.AudioContext = SpoofedAudioContext;
        if (window.webkitAudioContext) {
            window.webkitAudioContext = SpoofedAudioContext;
        }
    }
    
    // Screen properties spoofing
    Object.defineProperty(screen, 'width', { value: SPOOFED_DATA.screen.width });
    Object.defineProperty(screen, 'height', { value: SPOOFED_DATA.screen.height });
    Object.defineProperty(screen, 'colorDepth', { value: SPOOFED_DATA.screen.colorDepth });
    Object.defineProperty(screen, 'pixelDepth', { value: SPOOFED_DATA.screen.pixelDepth });
    Object.defineProperty(screen, 'availWidth', { value: SPOOFED_DATA.screen.availWidth });
    Object.defineProperty(screen, 'availHeight', { value: SPOOFED_DATA.screen.availHeight });
    
    // Navigator properties spoofing
    Object.defineProperty(navigator, 'hardwareConcurrency', { 
        value: SPOOFED_DATA.hardwareConcurrency 
    });
    
    if ('deviceMemory' in navigator) {
        Object.defineProperty(navigator, 'deviceMemory', { 
            value: SPOOFED_DATA.deviceMemory 
        });
    }
    
    Object.defineProperty(navigator, 'platform', { 
        value: SPOOFED_DATA.platform 
    });
    
    Object.defineProperty(navigator, 'languages', { 
        value: SPOOFED_DATA.languages 
    });
    
    Object.defineProperty(navigator, 'language', { 
        value: SPOOFED_DATA.language 
    });
    
    // Timezone spoofing
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    Date.prototype.getTimezoneOffset = function() {
        return SPOOFED_DATA.timezoneOffset;
    };
    
    // Device pixel ratio spoofing
    Object.defineProperty(window, 'devicePixelRatio', {
        value: SPOOFED_DATA.screen.devicePixelRatio
    });
    
    console.log('🎭 Hardware data spoofing active - returning fake but consistent data');
    
})();
EOF

# Launch Chrome with data spoofing extension
"$CHROME_PATH" \
  --user-data-dir="$PROFILE_DIR" \
  --load-extension="$EXTENSION_DIR" \
  --disable-web-security \
  --disable-features=VizDisplayCompositor \
  --no-first-run \
  --no-default-browser-check \
  --user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" \
  > /dev/null 2>&1 &

echo "✅ Chrome launched with hardware data spoofing at: $PROFILE_DIR"
echo "🎭 Data Spoofing Active (Fake Data Mode)"
echo ""
echo "📋 Spoofed Data Summary:"
echo "   • GPU: Apple M2 Pro (fake renderer data) ✅"
echo "   • Canvas: Consistent fake fingerprint ✅"
echo "   • Audio: Fake but realistic audio context ✅"
echo "   • Screen: MacBook Pro 14\" specs (spoofed) ✅"
echo "   • CPU Cores: 8 cores (spoofed from real count) ✅"
echo "   • Memory: 8GB (consistent fake value) ✅"
echo "   • Timezone: America/New_York (spoofed) ✅"
echo "   • Languages: en-US (spoofed) ✅"
echo ""
echo "🔐 OAuth Compatibility:"
echo "   • All APIs functional ✅"
echo "   • Cloudflare challenges: Will work ✅"
echo "   • Auth0 flows: Will work ✅"
echo "   • CAPTCHA verification: Will work ✅"
echo "   • Returns fake data instead of blocking ✅"
echo ""
echo "🧪 Test spoofed data at:"
echo "   • https://browserleaks.com/"
echo "   • https://fingerprintjs.com/demo/"
echo ""
echo "💡 This approach maintains full functionality while spoofing fingerprints."

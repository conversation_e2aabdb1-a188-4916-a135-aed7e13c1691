#!/bin/bash

# Chrome Startup Script Injection
# Injects spoofing code before any page loads using --user-script flag

echo "🎭 Launching Chrome with startup script injection..."

# Check if Chrome is installed
CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
if [ ! -f "$CHROME_PATH" ]; then
    echo "❌ Google Chrome not found at: $CHROME_PATH"
    exit 1
fi

# Create injection profile directory
PROFILE_DIR="$HOME/chrome_injection_spoofed"
mkdir -p "$PROFILE_DIR"

# Create startup injection script
INJECTION_SCRIPT="$PROFILE_DIR/hardware_spoof.js"
cat > "$INJECTION_SCRIPT" << 'EOF'
// Hardware Spoofing Startup Script
// Injected before any page content loads

(function() {
    'use strict';
    
    console.log('🎭 Startup Hardware Spoofing Active');
    
    // Spoofed hardware data for different MacBook M2 Pro
    const SPOOFED_DATA = {
        gpu: {
            vendor: "Apple",
            renderer: "Apple M2 Pro (ID: 8C92)",
            version: "WebGL 2.0 (OpenGL ES 3.0 Chromium)",
            unmaskedVendor: "Apple Inc.",
            unmaskedRenderer: "Apple M2 Pro GPU (Device ID: 8C92)"
        },
        screen: {
            width: 1800,      // Different resolution
            height: 1169,
            colorDepth: 30,
            pixelDepth: 30,
            availWidth: 1800,
            availHeight: 1129,
            devicePixelRatio: 2
        },
        hardware: {
            cores: 12,        // Spoofed core count
            memory: 32,       // Spoofed memory
            platform: "MacIntel"
        },
        fingerprints: {
            canvas: "spoofed_canvas_abc123def456",
            audio: {
                sampleRate: 48000,
                baseLatency: 0.004166666666666667,
                maxChannelCount: 2
            }
        }
    };
    
    // WebGL Spoofing - Override before any context creation
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(type, attributes) {
        const context = originalGetContext.call(this, type, attributes);
        
        if ((type === 'webgl' || type === 'webgl2') && context) {
            // Override getParameter
            const originalGetParameter = context.getParameter;
            context.getParameter = function(parameter) {
                switch (parameter) {
                    case context.VENDOR:
                        return SPOOFED_DATA.gpu.vendor;
                    case context.RENDERER:
                        return SPOOFED_DATA.gpu.renderer;
                    case context.VERSION:
                        return SPOOFED_DATA.gpu.version;
                    case context.SHADING_LANGUAGE_VERSION:
                        return "WebGL GLSL ES 3.00 (OpenGL ES GLSL ES 3.0 Chromium)";
                    case context.MAX_TEXTURE_SIZE:
                        return 16384;
                    case context.MAX_VIEWPORT_DIMS:
                        return new Int32Array([16384, 16384]);
                    default:
                        return originalGetParameter.call(this, parameter);
                }
            };
            
            // Override getExtension for debug info
            const originalGetExtension = context.getExtension;
            context.getExtension = function(name) {
                const ext = originalGetExtension.call(this, name);
                if (name === 'WEBGL_debug_renderer_info' && ext) {
                    const origGetParam = context.getParameter;
                    context.getParameter = function(param) {
                        if (param === ext.UNMASKED_VENDOR_WEBGL) {
                            return SPOOFED_DATA.gpu.unmaskedVendor;
                        }
                        if (param === ext.UNMASKED_RENDERER_WEBGL) {
                            return SPOOFED_DATA.gpu.unmaskedRenderer;
                        }
                        return origGetParam.call(this, param);
                    };
                }
                return ext;
            };
        }
        
        return context;
    };
    
    // Navigator Hardware Spoofing
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        value: SPOOFED_DATA.hardware.cores,
        writable: false,
        configurable: false
    });
    
    if ('deviceMemory' in navigator) {
        Object.defineProperty(navigator, 'deviceMemory', {
            value: SPOOFED_DATA.hardware.memory,
            writable: false,
            configurable: false
        });
    }
    
    // Screen Properties Spoofing
    ['width', 'height', 'colorDepth', 'pixelDepth', 'availWidth', 'availHeight'].forEach(prop => {
        if (SPOOFED_DATA.screen[prop] !== undefined) {
            Object.defineProperty(screen, prop, {
                value: SPOOFED_DATA.screen[prop],
                writable: false,
                configurable: false
            });
        }
    });
    
    // Device Pixel Ratio Spoofing
    Object.defineProperty(window, 'devicePixelRatio', {
        value: SPOOFED_DATA.screen.devicePixelRatio,
        writable: false,
        configurable: false
    });
    
    // Canvas Fingerprint Spoofing
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function() {
        return "data:image/png;base64," + SPOOFED_DATA.fingerprints.canvas;
    };
    
    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
    CanvasRenderingContext2D.prototype.getImageData = function() {
        const imageData = originalGetImageData.apply(this, arguments);
        // Slightly modify image data for consistent spoofing
        for (let i = 0; i < imageData.data.length; i += 4) {
            imageData.data[i] = (imageData.data[i] + 1) % 256;
        }
        return imageData;
    };
    
    // Audio Context Spoofing
    if (window.AudioContext || window.webkitAudioContext) {
        const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
        
        function SpoofedAudioContext() {
            const ctx = new OriginalAudioContext();
            
            // Override audio properties
            Object.defineProperty(ctx, 'sampleRate', {
                value: SPOOFED_DATA.fingerprints.audio.sampleRate,
                writable: false
            });
            
            Object.defineProperty(ctx, 'baseLatency', {
                value: SPOOFED_DATA.fingerprints.audio.baseLatency,
                writable: false
            });
            
            return ctx;
        }
        
        SpoofedAudioContext.prototype = OriginalAudioContext.prototype;
        window.AudioContext = SpoofedAudioContext;
        if (window.webkitAudioContext) {
            window.webkitAudioContext = SpoofedAudioContext;
        }
    }
    
    // Performance API Spoofing (timing attacks)
    const originalNow = performance.now;
    performance.now = function() {
        // Add small random variation to timing
        return originalNow.call(this) + (Math.random() - 0.5) * 0.1;
    };
    
    console.log('✅ Startup hardware spoofing injection complete');
    console.log('🎭 Hardware fingerprinting APIs overridden');
    
})();
EOF

# Launch Chrome with startup script injection
"$CHROME_PATH" \
  --user-data-dir="$PROFILE_DIR" \
  --enable-automation \
  --disable-background-networking \
  --disable-sync \
  --disable-translate \
  --no-first-run \
  --no-default-browser-check \
  --disable-logging \
  --user-script="$INJECTION_SCRIPT" \
  --allow-running-insecure-content \
  --disable-web-security \
  --user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" \
  > /dev/null 2>&1 &

echo "✅ Chrome launched with startup script injection"
echo "🎭 Hardware Spoofing via Startup Script"
echo ""
echo "📋 Injection Method:"
echo "   • Script injected before page load ✅"
echo "   • All hardware APIs overridden ✅"
echo "   • Consistent spoofed data ✅"
echo "   • Works on all websites ✅"
echo ""
echo "🔧 Spoofed Hardware:"
echo "   • GPU: Apple M2 Pro (ID: 8C92) ✅"
echo "   • Screen: 1800x1169 resolution ✅"
echo "   • CPU: 12 cores (spoofed) ✅"
echo "   • Memory: 32GB (spoofed) ✅"
echo "   • Canvas: Consistent fake fingerprint ✅"
echo "   • Audio: Spoofed audio context ✅"
echo ""
echo "🧪 Test injection at:"
echo "   • https://browserleaks.com/"
echo "   • https://fingerprintjs.com/demo/"
echo ""
echo "🔐 For Augment OAuth:"
echo "   • https://augmentcode.com/"
echo ""
echo "💡 This method injects before any page content loads"

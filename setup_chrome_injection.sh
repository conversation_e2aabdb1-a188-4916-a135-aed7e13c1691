#!/bin/bash

# Setup Chrome Injection Dependencies
# Installs required packages for Chrome data injection

echo "🔧 Setting up Chrome injection dependencies..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    echo "Please install Python 3 first"
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Install required Python packages
echo "📦 Installing Python dependencies..."

pip3 install --user websockets aiohttp asyncio

if [ $? -eq 0 ]; then
    echo "✅ Python dependencies installed successfully"
else
    echo "❌ Failed to install Python dependencies"
    echo "Try: pip3 install --user websockets aiohttp"
    exit 1
fi

# Make scripts executable
chmod +x chrome_cdp_injector.py
chmod +x launch_chrome_startup_injection.sh

echo ""
echo "🎭 Chrome Injection Setup Complete!"
echo "=================================="
echo ""
echo "Available injection methods:"
echo ""
echo "1. 🚀 CDP Injection (Most Powerful):"
echo "   python3 chrome_cdp_injector.py"
echo "   • Uses Chrome DevTools Protocol"
echo "   • Can inject into running Chrome"
echo "   • Most reliable and comprehensive"
echo ""
echo "2. 📜 Startup Script Injection:"
echo "   ./launch_chrome_startup_injection.sh"
echo "   • Injects before page load"
echo "   • Good compatibility"
echo "   • Simpler setup"
echo ""
echo "3. 🧪 Test injection effectiveness:"
echo "   Visit https://browserleaks.com/ after injection"
echo ""
echo "4. 🔐 For Augment OAuth:"
echo "   Use either method, then visit https://augmentcode.com/"
echo ""
echo "💡 Recommendation: Try CDP injection first (method 1)"

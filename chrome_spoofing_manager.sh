#!/bin/bash

# Chrome Spoofing Manager
# Manages spoofed Chrome profile and testing

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROFILE_DIR="$HOME/chrome_spoofed_profile"
OAUTH_PROFILE_DIR="$HOME/chrome_oauth_spoofed_profile"
DATA_SPOOFED_PROFILE_DIR="$HOME/chrome_data_spoofed_profile"
LAUNCH_SCRIPT="$SCRIPT_DIR/launch_chrome_spoofed.sh"
OAUTH_LAUNCH_SCRIPT="$SCRIPT_DIR/launch_chrome_oauth_compatible.sh"
DATA_SPOOFED_LAUNCH_SCRIPT="$SCRIPT_DIR/launch_chrome_data_spoofed.sh"

show_menu() {
    echo "🎭 Chrome Spoofing Manager"
    echo "=========================="
    echo "1. Launch Maximum Protection Chrome (may break <PERSON>Auth)"
    echo "2. Launch OAuth-Compatible Chrome"
    echo "3. Launch Data Spoofing Chrome (RECOMMENDED - fake data)"
    echo "4. Test Fingerprinting Protection"
    echo "5. Clean Spoofed Profiles"
    echo "6. View Profile Info"
    echo "7. Kill All Chrome Processes"
    echo "8. Setup Extensions"
    echo "9. Test Augment OAuth"
    echo "10. Exit"
    echo ""
    read -p "Choose option (1-10): " choice
}

launch_chrome() {
    echo "🚀 Launching maximum protection Chrome..."
    if [ -f "$LAUNCH_SCRIPT" ]; then
        chmod +x "$LAUNCH_SCRIPT"
        "$LAUNCH_SCRIPT"
    else
        echo "❌ Launch script not found: $LAUNCH_SCRIPT"
    fi
}

launch_oauth_chrome() {
    echo "🚀 Launching OAuth-compatible Chrome..."
    if [ -f "$OAUTH_LAUNCH_SCRIPT" ]; then
        chmod +x "$OAUTH_LAUNCH_SCRIPT"
        "$OAUTH_LAUNCH_SCRIPT"
    else
        echo "❌ OAuth launch script not found: $OAUTH_LAUNCH_SCRIPT"
    fi
}

launch_data_spoofed_chrome() {
    echo "🚀 Launching data spoofing Chrome (RECOMMENDED)..."
    if [ -f "$DATA_SPOOFED_LAUNCH_SCRIPT" ]; then
        chmod +x "$DATA_SPOOFED_LAUNCH_SCRIPT"
        "$DATA_SPOOFED_LAUNCH_SCRIPT"
    else
        echo "❌ Data spoofed launch script not found: $DATA_SPOOFED_LAUNCH_SCRIPT"
    fi
}

test_fingerprinting() {
    echo "🧪 Opening fingerprinting test sites..."
    
    # Launch spoofed Chrome if not running
    if ! pgrep -f "chrome_spoofed_profile" > /dev/null; then
        echo "Starting spoofed Chrome first..."
        launch_chrome
        sleep 3
    fi
    
    # Open test sites
    open -a "Google Chrome" --args --user-data-dir="$PROFILE_DIR" "https://browserleaks.com/"
    sleep 1
    open -a "Google Chrome" --args --user-data-dir="$PROFILE_DIR" "https://coveryourtracks.eff.org/"
    sleep 1
    open -a "Google Chrome" --args --user-data-dir="$PROFILE_DIR" "https://amiunique.org/"
    
    echo "✅ Test sites opened. Check results and document effectiveness."
}

clean_profile() {
    echo "🧹 Cleaning spoofed Chrome profiles..."

    # Kill Chrome processes using spoofed profiles
    pkill -f "chrome_spoofed_profile"
    pkill -f "chrome_oauth_spoofed_profile"
    sleep 2

    # Remove profile directories
    if [ -d "$PROFILE_DIR" ]; then
        rm -rf "$PROFILE_DIR"
        echo "✅ Maximum protection profile cleaned: $PROFILE_DIR"
    fi

    if [ -d "$OAUTH_PROFILE_DIR" ]; then
        rm -rf "$OAUTH_PROFILE_DIR"
        echo "✅ OAuth-compatible profile cleaned: $OAUTH_PROFILE_DIR"
    fi

    if [ ! -d "$PROFILE_DIR" ] && [ ! -d "$OAUTH_PROFILE_DIR" ]; then
        echo "ℹ️  No spoofed profiles found to clean."
    fi
}

view_profile_info() {
    echo "📊 Spoofed Profile Information"
    echo "=============================="

    echo "Maximum Protection Profile: $PROFILE_DIR"
    if [ -d "$PROFILE_DIR" ]; then
        echo "Status: ✅ Exists"
        echo "Size: $(du -sh "$PROFILE_DIR" | cut -f1)"
        echo "Created: $(stat -f "%SB" "$PROFILE_DIR")"
    else
        echo "Status: ❌ Not found"
    fi

    echo ""
    echo "OAuth-Compatible Profile: $OAUTH_PROFILE_DIR"
    if [ -d "$OAUTH_PROFILE_DIR" ]; then
        echo "Status: ✅ Exists"
        echo "Size: $(du -sh "$OAUTH_PROFILE_DIR" | cut -f1)"
        echo "Created: $(stat -f "%SB" "$OAUTH_PROFILE_DIR")"
    else
        echo "Status: ❌ Not found"
    fi
}

kill_chrome() {
    echo "💀 Killing all Chrome processes..."
    pkill -f "Google Chrome"
    pkill -f "chrome_spoofed_profile"
    pkill -f "chrome_oauth_spoofed_profile"
    sleep 2
    echo "✅ All Chrome processes terminated."
}

test_augment_oauth() {
    echo "🔐 Testing Augment OAuth Flow"
    echo "============================="

    # Launch OAuth-compatible Chrome if not running
    if ! pgrep -f "chrome_oauth_spoofed_profile" > /dev/null; then
        echo "Starting OAuth-compatible Chrome first..."
        launch_oauth_chrome
        sleep 3
    fi

    # Open Augment login
    open -a "Google Chrome" --args --user-data-dir="$OAUTH_PROFILE_DIR" "https://augmentcode.com/"

    echo "✅ Augment website opened in OAuth-compatible Chrome."
    echo ""
    echo "📋 Test Steps:"
    echo "1. Click 'Sign In' on Augment website"
    echo "2. Complete OAuth flow (should work without errors)"
    echo "3. Check for successful authentication"
    echo "4. Note any error codes or issues"
    echo ""
    echo "🔍 Expected Results:"
    echo "   • No 'Auth Challenge Error Code 600010'"
    echo "   • Cloudflare challenges should pass"
    echo "   • OAuth flow should complete successfully"
    echo ""
    read -p "Press Enter after testing OAuth flow..."
}

setup_extensions() {
    echo "🔌 Extension Setup Guide"
    echo "======================="
    echo ""
    echo "After launching spoofed Chrome, install these extensions:"
    echo ""
    echo "1. Canvas Blocker"
    echo "   https://chrome.google.com/webstore/detail/canvas-blocker/nomnklagbgmgghhjidfhnoelnjfndfpd"
    echo ""
    echo "2. WebGL Fingerprint Defender"
    echo "   https://chrome.google.com/webstore/detail/webgl-fingerprint-defende/olnbjpaejebpnokblkepbphhembdicik"
    echo ""
    echo "3. AudioContext Fingerprint Defender"
    echo "   https://chrome.google.com/webstore/detail/audiocontext-fingerprint/pcbjiidheaempljdefbdplebgdgpjcbe"
    echo ""
    echo "4. User-Agent Switcher"
    echo "   https://chrome.google.com/webstore/detail/user-agent-switcher/bhchdcejhohfmigjafbampogmaanbfkg"
    echo ""
    echo "5. uBlock Origin"
    echo "   https://chrome.google.com/webstore/detail/ublock-origin/cjpalhdlnbpafiamejdnhcphjbkeiagm"
    echo ""
    echo "📋 Installation Steps:"
    echo "1. Launch spoofed Chrome (option 1)"
    echo "2. Visit each extension URL above"
    echo "3. Click 'Add to Chrome' for each extension"
    echo "4. Configure each extension for maximum protection"
    echo ""
    read -p "Press Enter to continue..."
}

# Main menu loop
while true; do
    show_menu
    
    case $choice in
        1)
            launch_chrome
            ;;
        2)
            launch_oauth_chrome
            ;;
        3)
            launch_data_spoofed_chrome
            ;;
        4)
            test_fingerprinting
            ;;
        5)
            clean_profile
            ;;
        6)
            view_profile_info
            ;;
        7)
            kill_chrome
            ;;
        8)
            setup_extensions
            ;;
        9)
            test_augment_oauth
            ;;
        10)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Please choose 1-10."
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
    clear
done
